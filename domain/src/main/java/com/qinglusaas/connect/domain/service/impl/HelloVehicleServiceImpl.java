package com.qinglusaas.connect.domain.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.qinglusaas.connect.client.common.constants.ChannelEnum;
import com.qinglusaas.connect.client.common.error.ClientName;
import com.qinglusaas.connect.client.saas.error.ErrorCode;
import com.qinglusaas.connect.client.saas.vo.response.ChannelVehicleModelMatchResp;
import com.qinglusaas.connect.client.saas.vo.response.SaasResultResp;
import com.qinglusaas.connect.domain.converter.hello.ChannelVehicleModelMatchDTOFromEntityConverterV2;
import com.qinglusaas.connect.domain.converter.hello.HelloVehicleChangeNotifyReqConverter;
import com.qinglusaas.connect.domain.converter.hello.HelloVehicleStatusReqConverter;
import com.qinglusaas.connect.domain.converter.saas.HelloVehicleInfoConverter;
import com.qinglusaas.connect.domain.service.IHelloBaseService;
import com.qinglusaas.connect.infra.exception.BizException;
import com.qinglusaas.connect.infra.persistence.dao.ApiConnRepository;
import com.qinglusaas.connect.infra.persistence.dao.store.ThirdIdRelationRepository;
import com.qinglusaas.connect.infra.persistence.dao.vehicle.*;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.HelloSubSeryEntity;
import com.qinglusaas.connect.domain.service.IHelloVehicleService;
import com.qinglusaas.connect.domain.util.VehicleMatchUtil;
import com.qinglusaas.connect.infra.logger.LogName;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.*;
import com.qinglusaas.connect.infra.remote.hello.client.HelloClientWrapper;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.*;
import com.qinglusaas.connect.infra.remote.hello.vo.request.*;
import com.qinglusaas.connect.infra.remote.saas.client.SaasClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasVehicleClient;
import com.qinglusaas.connect.infra.remote.saas.contants.IdRelationEnum;
import com.qinglusaas.connect.infra.remote.saas.vo.SaasResponse;
import com.qinglusaas.connect.infra.remote.saas.vo.request.VehicleStatusNotifyReq;
import com.qinglusaas.connect.infra.remote.saas.vo.response.ThirdPlatformVehicleModelResp;
import com.qinglusaas.connect.infra.util.UrlProxyUtil;
import com.ql.dto.ApiResultResp;
import com.ql.dto.msg.PlatformSyncParam;
import com.ql.dto.msg.VehicleModelModificationReq;
import com.ql.dto.vehicle.VehicleInfoDTO;
import com.ql.dto.vehicle.VehicleModelDTO;
import com.ql.dto.vehicle.request.GetlVehicleRequest;
import com.ql.enums.VehicleInfoEnums;
import com.ql.enums.open.OpenStoreEnum;
import com.ql.enums.open.OpenVehicleEnum;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Scope;
import io.quarkus.arc.log.LoggerName;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ExecutionException;

import static com.ql.Constant.ChannelId.HELLO;

/**
 * <AUTHOR>
 */
@Singleton
public class HelloVehicleServiceImpl extends AbsConsumer implements IHelloVehicleService {

    @LoggerName(LogName.HELLO)
    Logger logger;
    @Inject
    VehicleBindRepository vehicleBindRepository;
    @Inject
    HelloClientWrapper helloClientWrapper;
    @Inject
    ThirdVehicleIdRelationRepository thirdVehicleIdRelationRepository;
    @RestClient
    SaasVehicleClient vehicleClient;
    @RestClient
    SaasClient saasClient;
    @Inject
    ThirdIdRelationRepository thirdIdRelationRepository;
    @Inject
    HelloSubSeryRepository helloSubSeryRepository;
    @Inject
    ApiConnRepository apiConnRepository;
    @Inject
    IHelloBaseService helloBaseService;
    @Inject
    RentMainRepository rentingMainRepository;
    @Inject
    VehicleSubSeryRepository vehicleSubSeryRepository;
    @Inject
    VehicleModelRepository vehicleModelRepository;
    @Inject
    VehicleInfoRepository vehicleInfoRepository;
    @Inject
    UrlProxyUtil urlProxyUtil;

    private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(5);
    // 新增一个固定大小的线程池用于处理并发请求
    private final ExecutorService concurrentRequestExecutor = Executors.newFixedThreadPool(10);

    @Override
    public ChannelVehicleModelMatchResp vehicleModelMatch(VehicleBrandEntity vehicleBrandEntity,
            VehicleSeryEntity vehicleSeryEntity, VehicleSubSeryEntity vehicleSubSeryEntity) {

        if (vehicleBrandEntity == null || vehicleBrandEntity.getBrandName() == null) {
            return new ChannelVehicleModelMatchResp();
        }

        // 根据品牌和车系查询列表
        List<HelloSubSeryEntity> helloVehicleEntities = HelloSubSeryEntity
                .find(HelloSubSeryEntity_.BRAND_NAME + " = ?1", vehicleBrandEntity.getBrandName()).list();
        if (null == helloVehicleEntities || helloVehicleEntities.isEmpty()) {
            return new ChannelVehicleModelMatchResp();
        }

        List<HelloSubSeryEntity> filterVehicleList = helloVehicleEntities.stream()
                .filter(entity -> entity.getSeriesName() != null
                        && entity.getSeriesName().equalsIgnoreCase(vehicleSeryEntity.getSeryName()))
                .collect(Collectors.toList());
        if (Objects.nonNull(filterVehicleList) && filterVehicleList.size() > 0) {
            helloVehicleEntities = filterVehicleList;
        }

        helloVehicleEntities.sort(Comparator.comparing(entity -> VehicleMatchUtil.score(vehicleSubSeryEntity,
                this.displacementMapper(vehicleSubSeryEntity.getDisplacement()),
                fuelFormMapper(vehicleSubSeryEntity.getFuelForm()),
                gearboxMapper(vehicleSubSeryEntity.getTransmission()),
                Objects.nonNull(entity.getDoorNum()) ? entity.getDoorNum().toString() : null,
                Objects.nonNull(entity.getSeatNum()) ? entity.getSeatNum().toString() : null,
                entity.getModelYear().toString()), Comparator.reverseOrder()));
        // 车系
        List<HelloSubSeryEntity> helloVehicleEntitiesPrecise = helloVehicleEntities.stream()
                .filter(entity -> VehicleMatchUtil.match(vehicleSubSeryEntity,
                        displacementMapper(vehicleSubSeryEntity.getDisplacement()),
                        fuelFormMapper(vehicleSubSeryEntity.getFuelForm()),
                        gearboxMapper(vehicleSubSeryEntity.getTransmission()),
                        Objects.nonNull(entity.getDoorNum()) ? entity.getDoorNum().toString() : null,
                        Objects.nonNull(entity.getSeatNum()) ? entity.getSeatNum().toString() : null,
                        entity.getModelYear().toString()))
                .toList();
        if (helloVehicleEntitiesPrecise.size() > 1) {
            // 精准匹配到多个时warn日志
            logger.warnv("[HELLO]vehicleSubSery id: %s matched multi entity");
            // by helloVehicleEntitiesPrecise filter helloVehicleEntitiesBySery id attribute
            // exclusive.
            helloVehicleEntities = helloVehicleEntities.stream()
                    .filter(entity -> !helloVehicleEntitiesPrecise.stream()
                            .map(HelloSubSeryEntity::getHelloId).toList().contains(entity.getId()))
                    .collect(Collectors.toList());
        }
        ChannelVehicleModelMatchDTOFromEntityConverterV2 channelVehicleModelMatchDTOFromEntityConverter = new ChannelVehicleModelMatchDTOFromEntityConverterV2();
        return new ChannelVehicleModelMatchResp(
                helloVehicleEntities.stream().map(channelVehicleModelMatchDTOFromEntityConverter::convert)
                        .collect(Collectors.toList()),
                helloVehicleEntitiesPrecise.stream().map(channelVehicleModelMatchDTOFromEntityConverter::convert)
                        .collect(Collectors.toList()));
    }

    @Override
    public void vehicleModelNotify(PlatformSyncParam platformSyncParam) {
        // 基本参数校验
        if (!this.validatePlatformSyncParam(platformSyncParam)) {
            return;
        }
        Span current = Span.current();
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(
                platformSyncParam.getMerchantId(),
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            current.setStatus(StatusCode.ERROR, "商家未开通HELLO渠道");
            return;
        }

        String paramJson = platformSyncParam.getData();
        VehicleModelModificationReq vehicleModelModificationReq = this.deserialize(paramJson,
                VehicleModelModificationReq.class);
        // 查询车辆列表，向哈啰创建对应的车辆
        Long merchantId = platformSyncParam.getMerchantId();

        List<VehicleModelDTO> beforeUpdateModelList = vehicleModelModificationReq.getBeforeUpdateModelList();
        for (VehicleModelDTO vehicleModelDTO : beforeUpdateModelList) {
            try {
                // 车型未绑定或者未修改绑定车型，不做任何处理
                Optional<VehicleBindEntity> newBindOpt = vehicleBindRepository
                        .findByVehicleModelIdAndChannel(vehicleModelDTO.getId(), ChannelEnum.HELLO.getCode());
                if (newBindOpt.isEmpty()) {
                    continue;
                }

                VehicleBindEntity newBind = newBindOpt.get();
                String newBindSeryId = newBind.getBindChannelVehicleSeryId();
                boolean notChangeBind = vehicleModelDTO.getThirdBinds().stream()
                        .anyMatch(e -> ChannelEnum.HELLO.getCode().equals(e.getChannelId())
                                && newBindSeryId.equals(e.getBindChannelVehicleSeryId()));
                if (notChangeBind) {
                    continue;
                }

                GetlVehicleRequest getlVehicleRequest = new GetlVehicleRequest();
                getlVehicleRequest.setId(1L);
                getlVehicleRequest.setSize(Integer.MAX_VALUE);
                getlVehicleRequest.setType("ALL");
                getlVehicleRequest.setVehicleModelId(vehicleModelDTO.getId());
                ApiResultResp<List<VehicleInfoDTO>> vehiclesResp = vehicleClient.getBasicVehicleInfo(
                        merchantId, ChannelEnum.HELLO.getCode(), getlVehicleRequest);
                if (!ApiResultResp.isSuccess(vehiclesResp) || vehiclesResp.getData() == null
                        || vehiclesResp.getData().isEmpty()) {
                    current.setAttribute("没有需要同步的车辆, modelId", vehicleModelDTO.getId());
                    continue;
                }
                for (VehicleInfoDTO vehicleInfoDTO : vehiclesResp.getData()) {
                    // 下架车辆 再修改车辆
                    boolean offlineSuccess = this.offlineHelloVehicle(vehicleInfoDTO.getId(), merchantId,
                            apiConnOpt.get().getChannelVendorCode());
                    if (offlineSuccess) {
                        this.syncVehicle(merchantId, vehicleInfoDTO);
                    }
                }
            } catch (Exception e) {
                logger.errorv("[HELLO]处理车型同步异常, modelId: {0}, merchantId{1}", vehicleModelDTO.getId(), merchantId, e);
            }
        }
    }

    /**
     * 下架哈啰车辆
     * 
     * @param merchantId        商家ID
     * @param vehicleId         车辆ID
     * @param channelVendorCode hello商家ID
     */
    private boolean offlineHelloVehicle(Long vehicleId, Long merchantId, String channelVendorCode) {
        try {
            // 查询车辆映射关系
            Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                    .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                            ChannelEnum.HELLO.getCode(),
                            merchantId,
                            OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                            vehicleId);
            if (vehicleRelationOpt.isEmpty()) {
                // 认为未在哈啰上架，先视为成功
                return true;
            }

            // 调用哈啰下架接口
            HelloVehicleIdReq dto = new HelloVehicleIdReq();
            dto.setCarId(vehicleRelationOpt.get().getThirdId());
            ResultResp<?> resp = helloClientWrapper.vehicleOfflineNotify(channelVendorCode, dto);
            if (!ResultResp.isSuccess(resp)) {
                logger.errorv("[HELLO]哈啰车辆下架失败, merchantId{0} vehicleId:{1}, resp:{2}", merchantId, vehicleId, resp);
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.errorv("[HELLO]哈啰车辆下架异常, merchantId{0}, vehicleId:{1}, error:{2}", merchantId, vehicleId, e);
            return false;
        }
    }

    @Override
    public void vehicleAddNotify(PlatformSyncParam platformSyncParam) {
        if (!validatePlatformSyncParam(platformSyncParam)) {
            return;
        }

        VehicleInfoDTO vehicle = this.deserialize(platformSyncParam.getData(), VehicleInfoDTO.class);
        if (vehicle == null) {
            return;
        }

        this.syncVehicle(platformSyncParam.getMerchantId(), vehicle);
    }

    @Override
    public void vehicleUpdateNotify(PlatformSyncParam platformSyncParam) {
        this.vehicleAddNotify(platformSyncParam);
    }

    @Override
    public SaasResultResp initVehicleData(VehicleInitReq req, Long merchantId) {
        // 1. 验证merchant和获取基础数据
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO);
        Span current = Span.current();
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO-INIT]商家开通信息丢失, merchantId:{0}", merchantId);
            current.setStatus(StatusCode.ERROR, "商家开通信息丢失");
            return SaasResultResp.failed("1", "商家开通信息丢失");
        }

        // 获取第三方商户ID
        String thirdMerchantId = apiConnOpt.get().getChannelVendorCode();

        // 查询车型和商品的映射关系
        List<ThirdVehicleIdRelationEntity> vehicleModelRelations = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantId(ChannelEnum.HELLO.getCode(),
                        merchantId, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType());
        if (vehicleModelRelations.isEmpty()) {
            logger.errorv("[HELLO-INIT]车型商品映射未创建");
            current.setStatus(StatusCode.ERROR, "商家开通信息丢失");
            return SaasResultResp.failed("1", "商品映射为空");
        }

        // 查询SaaS门店和哈啰门店的映射关系
        List<ThirdIdRelationEntity> storeRelations = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndType(HELLO, merchantId, IdRelationEnum.Store.STORE.getType());
        if (vehicleModelRelations.isEmpty()) {
            logger.errorv("[HELLO-INIT]门店映射未创建");
            return SaasResultResp.failed("1", "门店映射为空");
        }

        List<String> thirdStoreIds = storeRelations.stream().map(ThirdIdRelationEntity::getThirdId).toList();
        try {
            // 3. 获取SaaS系统中的车辆信息，建立车牌号索引
            Map<String, VehicleInfoEntity> saasVehiclesByLicense = this.getSaasVehiclesByLicense(merchantId);
            if (saasVehiclesByLicense == null) {
                return SaasResultResp.failed("1", "获取SaaS车辆列表失败");
            }

            // 4. 处理每个门店的车辆
            VehicleProcessStats stats = new VehicleProcessStats();
            for (String thirdStoreId : thirdStoreIds) {
                // 查询哈啰门店下的车辆
                List<HelloVehicleInfoListResp.CarInfo> storeVehicles = this.fetchStoreVehicles(merchantId,
                        thirdMerchantId, Collections.singletonList(thirdStoreId));
                if (storeVehicles.isEmpty()) {
                    continue;
                }

                for (HelloVehicleInfoListResp.CarInfo helloCarInfo : storeVehicles) {
                    String licensePlate = helloCarInfo.getLicencePlate();
                    if (Strings.isNullOrEmpty(licensePlate)) {
                        logger.infov("[HELLO—INIT]哈啰车辆车牌号为空, carId: {0}", helloCarInfo.getCarId());
                        continue;
                    }

                    // 查询车辆映射
                    ThirdVehicleIdRelationEntity vehicleRelation = this.validateVehicleRelation(merchantId,
                            helloCarInfo.getCarId());

                    // 情况1和2: 车牌号在SaaS系统中存在
                    if (saasVehiclesByLicense.containsKey(licensePlate)) {
                        this.processExistingLicensePlate(
                                merchantId,
                                thirdMerchantId,
                                helloCarInfo,
                                licensePlate,
                                vehicleRelation,
                                saasVehiclesByLicense.get(licensePlate),
                                vehicleModelRelations,
                                stats);
                    } else {
                        // 情况3: 车牌在SaaS系统中不存在, 尝试创建车辆
                        this.processNonExistingLicensePlate(merchantId, thirdMerchantId,
                                helloCarInfo, vehicleModelRelations, stats);
                    }
                }
            }

            // 5. 处理在SaaS系统中存在,但在Hello系统中不存在的车辆
            this.processVehiclesOnlyInSaaS(merchantId, saasVehiclesByLicense, stats);

            // 记录日志
            logger.infov(
                    "[HELLO-INIT]哈啰车辆初始化完成: 匹配车辆数={0}, 修复车型不匹配数={1}, SaaS新增车辆数={2}, 同步哈啰车辆数={3}, 删除无匹配车辆数={4}, merchantId{5}",
                    stats.matchedCount, stats.mismatchModelCount, stats.createdCount, stats.pushVehicleCount,
                    stats.deleteFromHelloCount, merchantId);

            // 通知任务成功
            helloBaseService.initSuccessV2(req.getMerchantId(), 50);
            return SaasResultResp.success();
        } catch (Exception e) {
            logger.error("[HELLO-INIT]初始化车辆数据失败 merchantId{0}", merchantId, e);
            helloBaseService.initFailV2(req.getMerchantId(), e.getMessage(), "初始化车辆数据失败", 50);
            return SaasResultResp.failed(ErrorCode.NOT_FOUND_STORE.getCode(), e.getMessage());
        }
    }

    /**
     * 获取SaaS中所有车辆列表，并按车牌号建立索引
     */
    private Map<String, VehicleInfoEntity> getSaasVehiclesByLicense(Long merchantId) {
        return vehicleInfoRepository.findAllByMerchantId(merchantId).stream()
                .collect(Collectors.toMap(VehicleInfoEntity::getLicense, e -> e));
    }

    /**
     * 获取哈啰门店下的所有车辆
     */
    private List<HelloVehicleInfoListResp.CarInfo> fetchStoreVehicles(Long merchantId, String thirdMerchantId,
            List<String> thirdStoreIds) {
        int pageIndex = 1;
        int pageSize = 50;
        // 最终结果集
        List<HelloVehicleInfoListResp.CarInfo> storeVehicleList = new ArrayList<>();
        try {
            // 获取该门店下的所有车辆（分页）
            HelloVehicleListReq vehicleReq = new HelloVehicleListReq();
            vehicleReq.setMerchantId(thirdMerchantId);
            vehicleReq.setSiteIdList(thirdStoreIds);
            vehicleReq.setPageIndex(pageIndex);
            vehicleReq.setPageSize(pageSize);

            ResultResp<HelloVehicleInfoListResp> vehicleListResp = helloClientWrapper.vehicleList(merchantId,
                    vehicleReq);
            if (Objects.isNull(vehicleListResp) || !vehicleListResp.isSuccess()) {
                logger.errorv("[HELLO]获取哈啰门店车辆列表失败, merchantId:{0},storeId:{1}, response:{2}",
                        merchantId, thirdStoreIds, vehicleListResp);
                return storeVehicleList;
            }

            HelloVehicleInfoListResp vehicleInfoListResp = vehicleListResp.getData();
            HelloVehicleInfoListResp.PageResult pageResult = vehicleInfoListResp.getPageResult();

            // 把首页结果加入最终结果集，并计算分页
            storeVehicleList.addAll(Optional.ofNullable(pageResult).map(HelloVehicleInfoListResp.PageResult::getItems)
                    .orElse(new ArrayList<>()));
            int total = Optional.ofNullable(pageResult).map(HelloVehicleInfoListResp.PageResult::getTotal).orElse(0);

            // 计算总页数
            int totalPage = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;

            if (totalPage > 1) {
                // 使用共享的线程池来处理剩余页的请求
                // 存储所有Future对象
                List<Future<List<HelloVehicleInfoListResp.CarInfo>>> futures = new ArrayList<>();

                // 提交任务到线程池
                for (int i = 2; i <= totalPage; i++) {
                    final int pageNum = i;
                    Future<List<HelloVehicleInfoListResp.CarInfo>> future = concurrentRequestExecutor.submit(() -> {
                        HelloVehicleListReq threadReq = new HelloVehicleListReq();
                        threadReq.setMerchantId(thirdMerchantId);
                        threadReq.setSiteIdList(thirdStoreIds);
                        threadReq.setPageIndex(pageNum);
                        threadReq.setPageSize(pageSize);

                        ResultResp<HelloVehicleInfoListResp> threadResp = helloClientWrapper.vehicleList(merchantId,
                                threadReq);
                        if (Objects.isNull(threadResp) || !threadResp.isSuccess()) {
                            logger.errorv("[HELLO]获取哈啰车辆列表失败, merchantId{0} page: {1}, error:{2}",
                                    merchantId, pageNum, threadResp.getMsg());
                            return new ArrayList<>();
                        }

                        return Optional.ofNullable(threadResp.getData().getPageResult().getItems())
                                .orElse(new ArrayList<>());
                    });

                    futures.add(future);
                }

                // 获取所有线程执行结果
                for (Future<List<HelloVehicleInfoListResp.CarInfo>> future : futures) {
                    try {
                        storeVehicleList.addAll(future.get());
                    } catch (InterruptedException | ExecutionException e) {
                        logger.errorv("[HELLO]并发获取哈啰车辆列表异常, merchantId{0}", merchantId, e);
                    }
                }
            }
            return storeVehicleList;
        } catch (Exception e) {
            logger.errorv("[HELLO]获取哈啰门店车辆列表异常, storeIds: {0} merchantId{1}", thirdStoreIds, merchantId, e);
            return storeVehicleList;
        }
    }

    /**
     * 处理存在于SaaS系统的车牌号
     */
    private void processExistingLicensePlate(Long merchantId, String thirdMerchantId,
            HelloVehicleInfoListResp.CarInfo helloCarInfo, String licensePlate,
            ThirdVehicleIdRelationEntity relation, VehicleInfoEntity saasVehicle,
            List<ThirdVehicleIdRelationEntity> vehicleModelRelations, VehicleProcessStats stats) {
        // 已存在映射，无需处理
        if (relation != null) {
            logger.infov(" [HELLO-INIT]车辆映射已存在,重复处理 hello车辆Id-车牌:{0}-{1}, SaaSId-车牌:{2}-{3}, merchantId{4}",
                    helloCarInfo.getCarId(), helloCarInfo.getLicencePlate(), saasVehicle.getId(),
                    saasVehicle.getLicense(),
                    merchantId);
            return;
        }
        // 获取Hello车辆详情，用于检查车型匹配
        HelloVehicleInfoDTO helloVehicleData = this.getHelloVehicle(helloCarInfo.getCarId(), thirdMerchantId);

        // 创建车辆映射关系
        relation = this.createVehicleMapping(merchantId, helloCarInfo.getCarId(), saasVehicle);
        // 查询车辆商品 和 hello的映射关系
        Optional<ThirdVehicleIdRelationEntity> goodsRelation = vehicleModelRelations.stream()
                .filter(e -> e.getSaasId().equals(saasVehicle.getVehicleModelId())
                        && e.getStoreId().equals(saasVehicle.getStoreId()))
                .findFirst();
        if (goodsRelation.isEmpty()) {
            logger.warnv("[HELLO-INIT]系统内车辆未找到对应的商品映射");
            stats.mismatchModelCount++;
            return;
        }

        // 找到了匹配的车型映射，需要更新Hello车辆的商品ID
        ThirdVehicleIdRelationEntity vehicleModelRelation = goodsRelation.get();
        if (vehicleModelRelation.getThirdId().equals(helloVehicleData.getGoodsId())) {
            stats.matchedCount++;
            return;
        }

        // 下架车辆 -》 重新同步车辆
        boolean isOnline = helloVehicleData.getChannelCodeStatus() != 0;
        if (isOnline) {
            HelloVehicleIdReq dto = new HelloVehicleIdReq();
            dto.setCarId(relation.getThirdId());
            ResultResp<?> resp = helloClientWrapper.vehicleOfflineNotify(thirdMerchantId, dto);
            if (!ResultResp.isSuccess(resp)) {
                stats.mismatchModelCount++;
                logger.errorv("[HELLO][HELLO-INIT]哈啰车辆下架失败, 无法修改年款, merchantId{0} vehicleId:{1}, 车牌:{2}, resp:{3}",
                        merchantId, saasVehicle.getId(), licensePlate, resp);
                return;
            }
        }

        ApiResultResp<VehicleInfoDTO> saasVehicleResp = vehicleClient.getVehicleDetailById(saasVehicle.getId());
        if (saasVehicleResp == null || saasVehicleResp.getData() == null) {
            stats.mismatchModelCount++;
            logger.errorv("[HELLO][HELLO-INIT]查询内部车辆信息失败, 无法修改年款, vehicleId:{0}, 车牌:{1}, resp:{2}",
                    saasVehicle.getId(), licensePlate, saasVehicleResp);
            return;
        }

        // 调用修改接口
        VehicleInfoDTO saasVehicleDTO = saasVehicleResp.getData();
        HelloVehicleInfoDTO helloVehicleParam = this.buildHelloVehicleParam(merchantId, saasVehicleDTO, relation);
        ResultResp<BaseResultDTO> updResp = helloClientWrapper.vehicleUpdateNotify(thirdMerchantId, helloVehicleParam);
        if (ResultResp.isSuccess(updResp)) {
            stats.matchedCount++;
        } else {
            logger.warnv(
                    "[HELLO][HELLO-INIT]哈啰车辆与SaaS车辆车牌匹配，但车型无法匹配, licensePlate: {0}, carId: {1}, modelId: {2}, merchantId{3}",
                    licensePlate, helloCarInfo.getCarId(), saasVehicle.getVehicleModelId(), merchantId);
            stats.mismatchModelCount++;
        }
        if (isOnline) {
            this.commitHelloVehicleAudit(thirdMerchantId, saasVehicleDTO, helloCarInfo.getCarId(), Span.current());
        }
    }

    /**
     * 创建车辆映射关系
     */
    private ThirdVehicleIdRelationEntity createVehicleMapping(Long merchantId, String thirdVehicleId,
            VehicleInfoEntity saasVehicle) {
        ThirdVehicleIdRelationEntity newRelation = new ThirdVehicleIdRelationEntity();
        newRelation.setMerchantId(merchantId);
        newRelation.setStoreId(saasVehicle.getStoreId());
        newRelation.setChannelId(HELLO);
        newRelation.setType(OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType());
        newRelation.setThirdId(thirdVehicleId);
        newRelation.setSaasId(saasVehicle.getId());
        thirdVehicleIdRelationRepository.save(newRelation);
        return newRelation;
    }

    /**
     * 处理不存在于SaaS系统的车牌号
     */
    private void processNonExistingLicensePlate(Long merchantId, String thirdMerchantId,
            HelloVehicleInfoListResp.CarInfo helloCarInfo,
            List<ThirdVehicleIdRelationEntity> allVehicleModelRelations,
            VehicleProcessStats stats) {
        // 获取Hello车辆详情，用于检查车型匹配
        HelloVehicleInfoDetailReq carInfoReq = new HelloVehicleInfoDetailReq();
        carInfoReq.setCarId(helloCarInfo.getCarId());
        ResultResp<HelloVehicleInfoDTO> vehicleInfo = helloClientWrapper.vehicleDetail(thirdMerchantId, carInfoReq);
        if (Objects.isNull(vehicleInfo) || !vehicleInfo.isSuccess()) {
            logger.errorv("[HELLO][HELLO-INIT]获取哈啰车辆详情失败, carId: {0}, response: {1}, merchantId{2}",
                    helloCarInfo.getCarId(), vehicleInfo, merchantId);
            return;
        }

        HelloVehicleInfoDTO helloVehicleData = vehicleInfo.getData();
        boolean shopExist = allVehicleModelRelations.stream()
                .anyMatch(e -> e.getThirdId().equals(helloVehicleData.getGoodsId()));
        if (!shopExist) {
            // 情况3.3: 没有对应的商品映射，删除Hello系统中的车辆
            HelloVehicleIdReq deleteReq = new HelloVehicleIdReq();
            deleteReq.setCarId(helloCarInfo.getCarId());
            ResultResp<?> deleteResp = helloClientWrapper.vehicleDeleteNotify(thirdMerchantId, deleteReq);

            if (ResultResp.isSuccess(deleteResp)) {
                logger.infov("[HELLO-INIT]删除哈啰系统中的无匹配车辆成功, carId: {0}, licensePlate: {1} merchantId{2}",
                        helloCarInfo.getCarId(), helloCarInfo.getLicencePlate(), merchantId);
                stats.deleteFromHelloCount++;
            } else {
                logger.errorv(
                        "[HELLO-INIT]删除哈啰系统中的无匹配车辆失败, carId: {0}, licensePlate: {1}, response: {2}, merchantId{3}",
                        helloCarInfo.getCarId(), helloCarInfo.getLicencePlate(), deleteResp, merchantId);
            }
        } else {
            // 情况3.3: 有对应的商品映射，在SaaS系统中创建这辆车
            this.createVehicleInSaaS(merchantId, thirdMerchantId, helloCarInfo, stats);
        }
    }

    /**
     * 处理只存在于SaaS系统中的车辆:同步到Hello系统
     */
    private void processVehiclesOnlyInSaaS(Long merchantId, Map<String, VehicleInfoEntity> saasVehiclesByLicense,
            VehicleProcessStats stats) {
        for (VehicleInfoEntity saasVehicle : saasVehiclesByLicense.values()) {

            ThirdVehicleIdRelationEntity relation = this.findVehicleRelation(merchantId, saasVehicle.getId());
            if (relation != null) {
                // 已有映射，跳过
                continue;
            }
            logger.infov("[HELLO-INIT]向HELLO推送车辆, vehicleId: {0}, license: {1}",
                    saasVehicle.getId(), saasVehicle.getLicense());

            // 同步车辆到Hello系统
            try {
                ApiResultResp<VehicleInfoDTO> vehicleResp = vehicleClient.getVehicleDetailById(saasVehicle.getId());
                if (ApiResultResp.isSuccess(vehicleResp)) {
                    this.syncVehicle(merchantId, vehicleResp.getData());
                    stats.pushVehicleCount++;
                } else {
                    logger.errorv("[HELLO-INIT]向HELLO推送车辆失败, vehicleId: {0}, error: {1}", saasVehicle.getId(),
                            vehicleResp);
                }
            } catch (Throwable e) {
                logger.errorv("[HELLO-INIT]向HELLO推送车辆失败, vehicleId: {0}, error: {1}", saasVehicle.getId(),
                        e.getMessage());
            }
        }
    }

    /**
     * 在SaaS系统中创建车辆
     */
    private void createVehicleInSaaS(Long merchantId, String thirdMerchantId,
            HelloVehicleInfoListResp.CarInfo carInfo,
            VehicleProcessStats stats) {
        // 获取详情进行车型匹配
        HelloVehicleInfoDetailReq carInfoReq = new HelloVehicleInfoDetailReq();
        carInfoReq.setCarId(carInfo.getCarId());
        ResultResp<HelloVehicleInfoDTO> vehicleInfo = helloClientWrapper.vehicleDetail(thirdMerchantId, carInfoReq);
        if (Objects.isNull(vehicleInfo) || !vehicleInfo.isSuccess()) {
            logger.errorv("[HELLO][HELLO-INIT]获取哈啰车辆详情失败, carId:{0}, response:{1}, merchantId{}",
                    carInfo.getCarId(), vehicleInfo, merchantId);
            return;
        }

        HelloVehicleInfoDTO helloVehicleData = vehicleInfo.getData();

        // 验证车型绑定关系
        Optional<VehicleBindEntity> vehicleOpt = validateVehicleBinding(helloVehicleData.getModelId());
        if (vehicleOpt.isEmpty()) {
            logger.errorv("[HELLO][HELLO-INIT]未找到车型映射关系, modelId: {0}", helloVehicleData.getModelId());
            return;
        }

        // 查询门店映射
        Optional<ThirdIdRelationEntity> storeRelationOpt = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndThirdId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenStoreEnum.IdRelationEnum.STORE.getType(),
                        helloVehicleData.getSiteId());
        if (storeRelationOpt.isEmpty()) {
            // 删除
            HelloVehicleIdReq req = new HelloVehicleIdReq();
            req.setCarId(helloVehicleData.getGuid());
            ResultResp resultResp = helloClientWrapper.vehicleDeleteNotify(thirdMerchantId, req);
            // todo
            return;
        }

        // 转换为saas车辆数据
        VehicleInfoDTO convert = new HelloVehicleInfoConverter(vehicleOpt.get(), storeRelationOpt.get())
                .convert(helloVehicleData);

        // 保存车辆数据
        SaasResponse<VehicleInfoDTO> insertDataResp = vehicleClient.createVehicle(merchantId, convert);
        if (!insertDataResp.isOk()) {
            logger.errorv("[HELLO][HELLO-INIT]新增车辆失败, licensePlate: {0}, response: {1}",
                    helloVehicleData.getLicensePlate(), insertDataResp);
            // todo
            return;
        }

        VehicleInfoDTO insertData = insertDataResp.getData();

        // 保存映射
        ThirdVehicleIdRelationEntity entity = new ThirdVehicleIdRelationEntity();
        entity.setMerchantId(merchantId);
        entity.setStoreId(convert.getSaasStoreId());
        entity.setChannelId(HELLO);
        entity.setType(OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType());
        entity.setThirdId(carInfo.getCarId());
        entity.setSaasId(insertData.getId());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setOpTime(System.currentTimeMillis());
        thirdVehicleIdRelationRepository.save(entity);
        stats.createdCount++;
    }

    /**
     * 车辆处理统计
     */
    private static class VehicleProcessStats {
        int matchedCount = 0;
        int mismatchModelCount = 0;
        int createdCount = 0;
        int pushVehicleCount = 0;
        int deleteFromHelloCount = 0;
    }

    /**
     * 校验平台同步参数
     * 
     * @param platformSyncParam 平台同步参数
     * @return 是否有效
     */
    private boolean validatePlatformSyncParam(PlatformSyncParam platformSyncParam) {
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            logger.infov("同步数据为空, platformSyncParam: {0}", platformSyncParam);
            return false;
        }
        return true;
    }

    /**
     * 构建请求车辆新增/修改参数
     */
    private HelloVehicleInfoDTO buildHelloVehicleParam(Long merchantId, VehicleInfoDTO vehicle,
            ThirdVehicleIdRelationEntity vehicleRelation) {
        Span currentSpan = Span.current();
        // 2. 验证车型绑定关系
        Optional<VehicleBindEntity> vehicleOpt = validateVehicleBinding(vehicle);
        if (vehicleOpt.isEmpty()) {
            currentSpan.setStatus(StatusCode.ERROR, "车型映射未绑定");
            return null;
        }

        // 3. 验证门店/hello子车系关系
        ThirdIdRelationEntity storeRelation = this.validateStoreRelation(merchantId, vehicle);
        if (storeRelation == null) {
            currentSpan.setStatus(StatusCode.ERROR, "HELLO门店未匹配");
            return null;
        }

        // 查询hello所属子车系
        Optional<HelloSubSeryEntity> helloVehicleOpt = this.validateVehicleSeries(vehicleOpt.get());
        if (helloVehicleOpt.isEmpty()) {
            currentSpan.setStatus(StatusCode.ERROR, "未知的HELLO子车系数据");
            return null;
        }

        // sku是否售卖
        Boolean skuSable = rentingMainRepository
                .findByVehicleModelIdAndStoreId(vehicle.getVehicleModelId(), vehicle.getSaasStoreId())
                .map(e -> Integer.valueOf(1).equals(e.getStatus())).orElse(false);
        HelloVehicleChangeNotifyReqConverter converter = new HelloVehicleChangeNotifyReqConverter(
                helloVehicleOpt.get(), vehicleRelation, storeRelation, skuSable,
                this.getVehicleSeryByModelId(vehicle.getVehicleModelId()), urlProxyUtil);
        return converter.convert(vehicle);
    }

    /**
     * 同步单个车辆信息
     * 
     * @param merchantId 商户ID
     * @param vehicle    车辆信息
     */
    private void syncVehicle(Long merchantId, VehicleInfoDTO vehicle) {
        Span currentSpan = Span.current();
        Optional<ApiConnEntity> apiConnEntity = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnEntity.isEmpty()) {
            logger.infov(" [HELLO]商家未开通 {0}", merchantId);
            return;
        }
        String channelVendorCode = apiConnEntity.get().getChannelVendorCode();

        // 1. 查询车辆映射关系
        ThirdVehicleIdRelationEntity vehicleRelation = this.findVehicleRelation(merchantId, vehicle.getId());
        // 平台不售卖
        if (vehicle.getPlatformSold() == null || vehicle.getPlatformSold() != 1) {
            logger.infov("[HELLO] 车辆不售卖 {0}", vehicle.getDrivingLicenseInfo().getPlateNumber());
            // 车辆未到平台，不做任何处理
            if (vehicleRelation == null) {
                currentSpan.setAttribute("车辆取消平台售卖", vehicle.getDrivingLicenseInfo().getPlateNumber());
                return;
            }
            currentSpan.setAttribute("车辆取消平台售卖", vehicle.getDrivingLicenseInfo().getPlateNumber());
            this.deleteHelloVehicle(vehicle.getId(), apiConnEntity.get(), currentSpan);
            return;
        }

        // 构建车辆修改请求
        HelloVehicleInfoDTO resultVehicle = this.buildHelloVehicleParam(merchantId, vehicle, vehicleRelation);
        if (resultVehicle == null) {
            logger.errorv("[HELLO]车辆同步参数构建为空 {0}", vehicle);
            return;
        }

        // failedResp为调用hello失败的响应，可能是da
        ResultResp<?> failedResp = null;
        String helloCarId;
        if (vehicleRelation == null) {
            logger.infov("[HELLO]车辆新增:{0}", resultVehicle);
            // 5.1 新增车辆
            ResultResp<VehicleAddNotifyResp> resp = helloClientWrapper.vehicleAddNotify(channelVendorCode,
                    resultVehicle);
            if (!ResultResp.isSuccess(resp)) {
                currentSpan.setStatus(StatusCode.ERROR, "新增HELLO车辆失败");
                // 如果有失败，通知车辆状态
                VehicleStatusNotifyReq statusNotifyReq = new VehicleStatusNotifyReq();
                statusNotifyReq.setPlateNumber(vehicle.getDrivingLicenseInfo().getPlateNumber());
                statusNotifyReq.setType("AUDIT");
                statusNotifyReq.setChannelId(ChannelEnum.HELLO.getCode());
                statusNotifyReq.setAuditFailReason(this.getHelloErrorMsg(resp.getSubMsg()));
                statusNotifyReq.setVehicleStatus(
                        VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_FAILED.getStatus().intValue());
                saasClient.vehicleStatusNotify(merchantId, ChannelEnum.HELLO.getCode(), statusNotifyReq);
                return;
            }
            helloCarId = resp.getData().getCarId();
            currentSpan.setAttribute("Hello新增车辆结果", "SUCCESS");

            // 5.2 保存内外部映射关系
            this.saveVehicleRelation(apiConnEntity.get(), vehicle, resp.getData(), currentSpan);
            // 5.3 查询对应的商品id, 和数据库对比
            this.compareVehicleModelRelation(resp.getData().getCarId(), apiConnEntity.get().getChannelVendorCode(),
                    vehicle, apiConnEntity.get().getMerchantId(), currentSpan);
            // 5.4 主动推送一次价格
            this.syncPrice(vehicle, merchantId, currentSpan);
            // 5.5 车辆新增,hello提交送审
            failedResp = this.commitHelloVehicleAudit(channelVendorCode, vehicle, resp.getData().getCarId(),
                    currentSpan);
        } else {
            logger.infov("[HELLO]车辆修改:{0}", resultVehicle);
            helloCarId = resultVehicle.getGuid();
            // 5.1 更新车辆
            ResultResp<BaseResultDTO> updResp = helloClientWrapper.vehicleUpdateNotify(channelVendorCode,
                    resultVehicle);
            if (ResultResp.isSuccess(updResp)) {
                // 5.2 查询对应的商品id, 和数据库对比
                this.compareVehicleModelRelation(vehicleRelation.getThirdId(), channelVendorCode, vehicle, merchantId,
                        currentSpan);
                // 5.4 主动推送一次价格
                this.syncPrice(vehicle, merchantId, currentSpan);
                // 5.5 车辆修改后，对比车辆状态(作为补偿:如果前面价格推送失败，会导致无法送审)
                HelloVehicleInfoDTO helloVehicle = this.getHelloVehicle(vehicleRelation.getThirdId(),
                        channelVendorCode);
                if (helloVehicle != null && helloVehicle.getChannelCodeStatus() == 0) {
                    failedResp = this.commitHelloVehicleAudit(channelVendorCode, vehicle, vehicleRelation.getThirdId(),
                            currentSpan);
                }
            } else {
                failedResp = updResp;
                currentSpan.setAttribute("哈啰车辆更新失败", updResp.getSubMsg());
                logger.errorv("[hello]车辆更新通知失败, resp:{0}", updResp);
            }
        }

        // 6.查询车辆详情状态,写入SaaS
        this.notifyPlateVehicleStatusToSaaS(failedResp, channelVendorCode, helloCarId, merchantId, currentSpan);
    }

    private void notifyPlateVehicleStatusToSaaS(ResultResp<?> olineResp, String channelVendorCode,
            String helloCarId, Long merchantId, Span currentSpan) {
        String auditReason = olineResp != null ? this.getHelloErrorMsg(olineResp.getSubMsg()) : "车辆未上架";
        if (olineResp != null) {
            // 触发了哈啰提交送审的情况，等待5s后查询车辆详情状态
            // 使用scheduled executor进行异步延迟任务处理
            final String finalAuditReason = auditReason;

            scheduledExecutor.schedule(() -> {
                try (Scope ignored = currentSpan.makeCurrent()) {
                    // 记录异步任务的日志
                    logger.infov("[HELLO]开始执行异步车辆状态查询, helloCarId:{0}, merchantId:{1}",
                            helloCarId, merchantId);

                    doNotifyVehicleStatusToSaaS(finalAuditReason, channelVendorCode, helloCarId, merchantId);

                    logger.infov("[HELLO]异步车辆状态查询成功, helloCarId:{0}", helloCarId);
                } catch (Exception e) {
                    logger.errorv("[HELLO]异步通知车辆状态异常, helloCarId:{0}, error:{1}", helloCarId, e.getMessage(), e);
                }
            }, 5, TimeUnit.SECONDS);
            return;
        }

        // 没有提交送审，直接执行通知
        doNotifyVehicleStatusToSaaS(auditReason, channelVendorCode, helloCarId, merchantId);
    }

    private void doNotifyVehicleStatusToSaaS(String auditReason, String channelVendorCode,
            String helloCarId, Long merchantId) {
        // todo
        HelloVehicleInfoDTO helloVehicle = this.getHelloVehicle(helloCarId, channelVendorCode);
        if (helloVehicle != null) {
            if (helloVehicle.getChannelCodeStatus() != null) {
                logger.infov("[HELLO]车辆状态: {0}", helloVehicle.getChannelCodeStatus().toString());
            }

            // 同步车辆状态到内部系统
            VehicleStatusNotifyReq statusNotifyReq = new VehicleStatusNotifyReq();
            statusNotifyReq.setPlateNumber(helloVehicle.getLicensePlate());
            statusNotifyReq.setType("AUDIT");
            statusNotifyReq.setChannelId(ChannelEnum.HELLO.getCode());

            /*
             * 哈啰状态 SaaS状态
             * 未上架 -》 审核状态:审核失败 车辆状态:系统下线（页面车辆状态:不可售卖）
             * 上架 -》 审核状态:审核成功 车辆状态:可售卖
             * 审核中 -> 审核状态:审核中 车辆状态: 待上架（页面车辆状态:审核中）
             */
            switch (helloVehicle.getChannelCodeStatus()) {
                case 0:
                    statusNotifyReq.setVehicleStatus(
                        (int)VehicleInfoEnums.VehicleChannelVehicleStatusEnum.SYSTEM_OFFLINE.getStatus());
                    statusNotifyReq.setAuditStatus(
                        (int)VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_FAILED.getStatus());
                    if (auditReason != null && !auditReason.isBlank()) {
                        statusNotifyReq.setAuditFailReason(auditReason);
                    }
                    break;
                case 1:
                    statusNotifyReq.setAuditStatus(
                        (int)VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_SUCCESS.getStatus());
                    statusNotifyReq.setVehicleStatus(
                        (int)VehicleInfoEnums.VehicleChannelVehicleStatusEnum.FOR_RENT.getStatus());
                    break;
                case 2:
                    statusNotifyReq.setAuditStatus(
                        (int)VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_PROCESSING.getStatus());
                    statusNotifyReq.setVehicleStatus(
                        (int)VehicleInfoEnums.VehicleChannelVehicleStatusEnum.FOR_ONLINE.getStatus());
                    break;
                default:
                    return;
            }

            if (auditReason != null && !auditReason.isBlank()) {
                statusNotifyReq.setAuditStatus(
                    (int)VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_FAILED.getStatus());
                statusNotifyReq.setAuditFailReason(auditReason);
            }
            saasClient.vehicleStatusNotify(merchantId, ChannelEnum.HELLO.getCode(), statusNotifyReq);
        }
    }

    private void compareVehicleModelRelation(String carId, String channelVendorCode,
            VehicleInfoDTO vehicleInfo, Long merchantId, Span currentSpan) {
        HelloVehicleInfoDTO helloVehicle = this.getHelloVehicle(carId, channelVendorCode);
        if (helloVehicle == null) {
            return;
        }

        Long saasStoreId = vehicleInfo.getSaasStoreId();
        // 查询门店映射
        Optional<ThirdIdRelationEntity> storeRelationOpt = thirdIdRelationRepository
            .findByChannelIdAndMerchantIdAndTypeAndThirdId(
                ChannelEnum.HELLO.getCode(),
                merchantId,
                OpenStoreEnum.IdRelationEnum.STORE.getType(),
                helloVehicle.getSiteId());
        if (storeRelationOpt.isEmpty()) {
            return;
        }
        if (!storeRelationOpt.get().getStoreId().equals(saasStoreId)) {
            logger.warnv("车辆门店与哈啰不一致,车辆id{0}, 哈啰门店:{1} SaaS门店:{2}", vehicleInfo.getId(), helloVehicle.getSiteId(), saasStoreId);
        }

        String targetGoodsId = helloVehicle.getGoodsId();
        Optional<ThirdVehicleIdRelationEntity> vehicleModelRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(ChannelEnum.HELLO.getCode(),
                    merchantId,
                    saasStoreId,
                    OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(),
                    vehicleInfo.getVehicleModelId());

        if (vehicleModelRelationOpt.isPresent()) {
            ThirdVehicleIdRelationEntity oldRelation = vehicleModelRelationOpt.get();
            if (!oldRelation.getThirdId().equals(targetGoodsId)) {
                oldRelation.setThirdId(targetGoodsId);
                thirdVehicleIdRelationRepository.save(oldRelation);
                currentSpan.setAttribute("更新车型商品映射",
                        String.format("商品ID:%s, 车型ID:%s,门店ID:%s", targetGoodsId,
                                vehicleInfo.getVehicleModelId(), saasStoreId));
            }
        } else {
            ThirdVehicleIdRelationEntity newRelation = ThirdVehicleIdRelationEntity.builder()
                    .merchantId(merchantId)
                    .storeId(saasStoreId)
                    .type(OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType())
                    .channelId(ChannelEnum.HELLO.getCode())
                    .thirdId(targetGoodsId)
                    .saasId(vehicleInfo.getVehicleModelId())
                    .build();
            thirdVehicleIdRelationRepository.save(newRelation);
            currentSpan.setAttribute("保存车型商品映射",
                    String.format("商品ID:%s, 车型ID:%s,门店ID:%s", targetGoodsId,
                            vehicleInfo.getVehicleModelId(), saasStoreId));
        }
    }

    private HelloVehicleInfoDTO getHelloVehicle(String carId, String channelVendorCode) {
        HelloVehicleInfoDetailReq req = new HelloVehicleInfoDetailReq();
        req.setCarId(carId);
        ResultResp<HelloVehicleInfoDTO> resultResp = helloClientWrapper.vehicleDetail(channelVendorCode, req);
        if (!ResultResp.isSuccess(resultResp) || resultResp.getData() == null) {
            logger.warnv("[HELLO]哈啰车辆, 更新后反查错误, carId:{0}, resp:{1}", carId, resultResp);
            return null;
        }
        return resultResp.getData();
    }

    private void syncPrice(VehicleInfoDTO vehicle, Long merchantId, Span currentSpan) {
        try {
            // 5.3 尝试推送车型门店价格
            boolean priceResult = helloBaseService.syncPrice(vehicle.getVehicleModelId(), vehicle.getSaasStoreId(),
                    merchantId);
            if (!Boolean.TRUE.equals(priceResult)) {
                currentSpan.setAttribute("哈啰价格同步失败", "FAILED");
            }
        } catch (Exception e) {
            currentSpan.recordException(e);
            logger.errorv("[HELLO]车辆同步后处理异常, vehicleId:{0}, error:{1}", vehicle.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理哈啰车辆同步后的操作
     * 
     * @param channelVendorCode 商户ID
     * @param vehicle           车辆信息
     * @param carId             hello车辆id
     * @param currentSpan
     */
    private ResultResp<?> commitHelloVehicleAudit(String channelVendorCode, VehicleInfoDTO vehicle, String carId,
            Span currentSpan) {
        if (Strings.isNullOrEmpty(carId)) {
            logger.errorv("[hello]车辆同步响应数据异常, vehicleId:{0}, resp:{1}", vehicle.getId(), carId);
            return null;
        }
        try {
            // 5.4 推送价格成功。全部调用hello车辆送审，后续用车辆借调来控制
            HelloVehicleOnlineReq dto = new HelloVehicleOnlineReq();
            dto.setCarIdList(Collections.singletonList(carId));
            ResultResp<?> onlineResp = helloClientWrapper.vehicleOnlineNotify(channelVendorCode, dto);
            if (ResultResp.isSuccess(onlineResp)) {
                currentSpan.setAttribute("哈啰车辆送审成功", "SUCCESS");
                // 上线成功，重置车辆渠道状态为已上线(10)
            } else {
                currentSpan.setAttribute("哈啰上线车辆失败", onlineResp.getSubMsg());
            }
            return onlineResp;
        } catch (Exception e) {
            currentSpan.recordException(e);
            logger.errorv("[HELLO]车辆同步后处理异常, vehicleId:{0}, error:{1}", vehicle.getId(), e.getMessage(), e);
        }
        return null;
    }

    /**
     * 验证车型绑定关系
     */
    private Optional<VehicleBindEntity> validateVehicleBinding(VehicleInfoDTO vehicle) {
        Optional<VehicleBindEntity> vehicleOpt = vehicleBindRepository
                .findByVehicleModelIdAndChannel(vehicle.getVehicleModelId(), ChannelEnum.HELLO.getCode());
        if (vehicleOpt.isEmpty()) {
            Span.current().setAttribute("推送哈啰车辆失败", "车型未匹配");
        }
        return vehicleOpt;
    }

    /**
     * 验证车型绑定关系
     */
    private Optional<VehicleBindEntity> validateVehicleBinding(String modelId) {
        List<VehicleBindEntity> vehicleOpt = vehicleBindRepository
                .findAllByBindChannelVehicleSeryIdAndChannelId(modelId, ChannelEnum.HELLO.getCode());
        if (vehicleOpt == null || vehicleOpt.isEmpty()) {
            Span.current().setAttribute("拉取车辆失败", "车型未匹配");
            return Optional.empty();
        }
        return Optional.of(vehicleOpt.get(0));
    }

    /**
     * 验证门店关系
     */
    private ThirdIdRelationEntity validateStoreRelation(Long merchantId, VehicleInfoDTO vehicle) {
        ThirdIdRelationEntity storeRelation = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenStoreEnum.IdRelationEnum.STORE.getType(),
                        vehicle.getSaasStoreId())
                .orElse(null);
        if (storeRelation == null) {
            Span.current().setAttribute("推送哈啰车辆失败", "门店未匹配");
        }
        return storeRelation;
    }

    /**
     * 验证车系匹配
     */
    private Optional<HelloSubSeryEntity> validateVehicleSeries(VehicleBindEntity vehicleBindEntity) {
        Optional<HelloSubSeryEntity> helloVehicleOpt = helloSubSeryRepository
                .findByHelloId(vehicleBindEntity.getBindChannelVehicleSeryId());
        if (helloVehicleOpt.isEmpty()) {
            Span.current().setAttribute("推送哈啰车辆失败", "车系未匹配");
        }
        return helloVehicleOpt;
    }

    /**
     * 查询车辆映射关系
     */
    private ThirdVehicleIdRelationEntity findVehicleRelation(Long merchantId, Long vehicleId) {
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicleId)
                .orElse(null);
    }

    /**
     * 保存车辆ID映射关系
     * 
     * @param merchantId     商户ID
     * @param saasVehicleId  内部车辆ID
     * @param saasStoreId    内部门店ID
     * @param thirdVehicleId 第三方车辆ID
     * @return 保存的映射关系实体
     */
    private ThirdVehicleIdRelationEntity saveVehicleIdRelation(Long merchantId, Long saasVehicleId,
            Long saasStoreId, String thirdVehicleId) {
        if (thirdVehicleId == null) {
            logger.errorv("[HELLO]保存车辆ID映射失败，第三方车辆ID为空");
            return null;
        }

        ThirdVehicleIdRelationEntity vehicleRelation = ThirdVehicleIdRelationEntity.builder()
                .merchantId(merchantId)
                .saasId(saasVehicleId)
                .storeId(saasStoreId)
                .thirdId(thirdVehicleId)
                .type(OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType())
                .channelId(ChannelEnum.HELLO.getCode())
                .opTime(System.currentTimeMillis())
                .build();
        thirdVehicleIdRelationRepository.save(vehicleRelation);
        logger.infov("[HELLO]保存车辆ID映射成功, 内部vehicleId:{0}, 第三方carId:{1}", saasVehicleId, thirdVehicleId);
        return vehicleRelation;
    }

    /**
     * 保存车型商品映射关系
     * 
     * @param merchantId   商户ID
     * @param saasModelId  内部车型ID
     * @param saasStoreId  内部门店ID
     * @param thirdGoodsId 第三方商品ID
     * @return 保存的映射关系实体
     */
    private ThirdVehicleIdRelationEntity saveModelGoodsRelation(Long merchantId, Long saasModelId, Long saasStoreId,
            String thirdGoodsId) {
        if (thirdGoodsId == null || saasModelId == null) {
            logger.errorv("[HELLO]保存车型商品映射失败，参数为空, modelId:{0}, goodsId:{1}", saasModelId, thirdGoodsId);
            return null;
        }

        // 先查询是否已存在映射关系
        Optional<ThirdVehicleIdRelationEntity> modelRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        saasStoreId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(),
                        saasModelId);

        if (modelRelationOpt.isEmpty()) {
            long currentTime = System.currentTimeMillis();
            ThirdVehicleIdRelationEntity modelRelation = ThirdVehicleIdRelationEntity.builder()
                    .merchantId(merchantId)
                    .storeId(saasStoreId)
                    .saasId(saasModelId)
                    .thirdId(thirdGoodsId.trim())
                    .type(OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType())
                    .opTime(currentTime)
                    .channelId(ChannelEnum.HELLO.getCode())
                    .build();
            thirdVehicleIdRelationRepository.save(modelRelation);
            logger.infov("[HELLO]保存车型商品映射成功, 内部modelId:{0}, 第三方goodsId:{1}", saasModelId, thirdGoodsId);
            return modelRelation;
        }

        return modelRelationOpt.get();
    }

    private void saveVehicleRelation(ApiConnEntity apiConnEntity, VehicleInfoDTO vehicle,
            VehicleAddNotifyResp vehicleResp, Span currentSpan) {
        if (vehicleResp == null || vehicleResp.getCarId() == null) {
            logger.errorv("[HELLO]车辆添加通知失败, resp:{0}", vehicleResp);
            return;
        }

        if (vehicleResp.getCarId() != null) {
            ThirdVehicleIdRelationEntity vehicleRelation = this.saveVehicleIdRelation(
                    apiConnEntity.getMerchantId(),
                    vehicle.getId(),
                    vehicle.getSaasStoreId(),
                    vehicleResp.getCarId());

            if (vehicleRelation != null) {
                currentSpan.setAttribute("HELLO车辆ID-SaaS车辆ID", vehicleResp.getCarId() + "-" + vehicle.getId());
            }
        }
    }

    @Override
    public void vehicleOnlineNotify(PlatformSyncParam platformSyncParam) {
        this.handleVehicleStatus(platformSyncParam);
    }

    @Override
    public void vehicleOfflineNotify(PlatformSyncParam platformSyncParam) {
        this.handleVehicleStatus(platformSyncParam);
    }

    /**
     * 处理车辆上/下线通知
     *
     */
    private void handleVehicleStatus(PlatformSyncParam platformSyncParam) {
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            logger.infov("[HELLO]哈啰车辆下线数据为空, platformSyncParam: {0}", platformSyncParam);
            return;
        }

        Long merchantId = platformSyncParam.getMerchantId();
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]哈啰车辆下线数据为空, merchantId: {0}", merchantId);
            return;
        }

        VehicleInfoDTO vehicle = this.deserialize(platformSyncParam.getData(), VehicleInfoDTO.class);
        if (vehicle == null) {
            return;
        }
        this.handleVehicleStatus(vehicle, merchantId, apiConnOpt.get());
    }

    /**
     * 处理车辆上/下线通知
     *
     */
    private void handleVehicleStatus(VehicleInfoDTO vehicle, Long merchantId, ApiConnEntity apiConnEntity) {

        Span currentSpan = Span.current();
        // 查询车辆映射关系
        Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(), merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicle.getId());
        if (vehicleRelationOpt.isEmpty()) {
            logger.errorv("[HELLO]车辆未找到映射关系，无法操作下线，内部vehicleId: {0}", vehicle.getId());
            return;
        }

        // 反查hello的车辆信息,用于塞回去请求
        String channelVendorCode = apiConnEntity.getChannelVendorCode();
        HelloVehicleInfoDTO helloVehicle = this.getHelloVehicle(vehicleRelationOpt.get().getThirdId(),
                channelVendorCode);
        if (helloVehicle == null) {
            currentSpan.setStatus(StatusCode.ERROR, "未查询到hello车辆详情");
            return;
        }
        boolean skuSable = rentingMainRepository
                .findByVehicleModelIdAndStoreId(vehicle.getVehicleModelId(), vehicle.getSaasStoreId())
                .map(e -> Integer.valueOf(1).equals(e.getStatus())).orElse(false);

        HelloVehicleStatusReqConverter converter = new HelloVehicleStatusReqConverter(skuSable && vehicle.isSalable(),
                vehicle, this.getVehicleSeryByModelId(vehicle.getVehicleModelId()));
        HelloVehicleInfoDTO helloVehicleStatusReq = converter.doForward(helloVehicle);
        ResultResp resp = helloClientWrapper.vehicleUpdateNotify(channelVendorCode, helloVehicleStatusReq);

        HelloVehicleInfoDTO postHelloVehicle = this.getHelloVehicle(vehicleRelationOpt.get().getThirdId(),
                channelVendorCode);
        if (postHelloVehicle == null || postHelloVehicle.getChannelCodeStatus() == null) {
            logger.errorv("[HELLO]车辆下线通知失败, vehicleId:{0}, resp:{1}", vehicle.getId(), resp);
            return;
        }
        currentSpan.setAttribute("反查哈啰车辆状态", postHelloVehicle.getChannelCodeStatus());

        // 6.查询车辆详情状态,写入SaaS
        this.notifyPlateVehicleStatusToSaaS(null, channelVendorCode,
                vehicleRelationOpt.get().getThirdId(), merchantId, currentSpan);
    }

    @Override
    public void changeSiteNotify(PlatformSyncParam platformSyncParam) {
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            logger.warnv("[HELLO]哈啰车辆调换门店数据为空, platformSyncParam: {0}", platformSyncParam);
            return;
        }
        Long merchantId = platformSyncParam.getMerchantId();
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]哈啰车辆调换门店, 商家不存在, merchantId: {0}", merchantId);
            return;
        }
        String channelVendorCode = apiConnOpt.get().getChannelVendorCode();

        VehicleInfoDTO vehicle = this.deserialize(platformSyncParam.getData(), VehicleInfoDTO.class);
        if (vehicle == null) {
            return;
        }

        // 1.1 查询新旧门店映射关系
        Long saasStoreId = vehicle.getSaasStoreId();
        ThirdIdRelationEntity newStoreRelation = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenStoreEnum.IdRelationEnum.STORE.getType(),
                        saasStoreId)
                .orElse(null);
        if (newStoreRelation == null) {
            logger.errorv("[HELLO]新门店未找到映射关系，storeId: {0}", saasStoreId);
            return;
        }

        // 1.2 查询车辆映射关系
        Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicle.getId());
        if (vehicleRelationOpt.isEmpty()) {
            logger.errorv("[HELLO]车辆未找到映射关系，无法调换门店，内部vehicleId: {0}", vehicle.getId());
            return;
        }
        // 2.2 查询哈啰商品
        HelloVehicleInfoDetailReq carInfoReq = new HelloVehicleInfoDetailReq();
        carInfoReq.setCarId(vehicleRelationOpt.get().getThirdId());
        ResultResp<HelloVehicleInfoDTO> vehicleDetailResp = helloClientWrapper.vehicleDetail(channelVendorCode,
                carInfoReq);
        if (!ResultResp.isSuccess(vehicleDetailResp)) {
            logger.errorv("[HELLO]哈啰车辆详情查询失败, vehicleId:{0}, resp:{1}", vehicle.getId(), vehicleDetailResp);
        }

        // 2. 调用哈啰接口
        HelloVehicleAllocateReq dto = new HelloVehicleAllocateReq();
        dto.setCarIds(Collections.singletonList(vehicleRelationOpt.get().getThirdId()));
        dto.setSiteId(newStoreRelation.getThirdId());
        dto.setGoodsId(vehicleDetailResp.getData().getGoodsId());
        VehicleShuntingResp.VehicleShuntingResult resp = helloClientWrapper.vehicleShuntNotify(channelVendorCode, dto);
        if (resp == null || resp.isFailed()) {
            logger.errorv("[HELLO]车辆调换门店失败, vehicleId:{0}, resp:{1}", vehicle.getId(), resp);
            return;
        }
        logger.infov("[HELLO]车辆调换门店成功, vehicleId:{0}, 新站点ID:{1}", vehicle.getId(), newStoreRelation.getThirdId());

        // 3.1 更新本地存储的门店ID
        ThirdVehicleIdRelationEntity relation = vehicleRelationOpt.get();
        relation.setStoreId(saasStoreId);
        relation.setOpTime(System.currentTimeMillis());
        thirdVehicleIdRelationRepository.save(relation);

        // 3.2 保存新商品ID（如果返回）
        if (resp.getData() != null && resp.getData().getNewGoodsId() != null) {
            String newThirdModelId = resp.getData().getNewGoodsId();
            logger.infov("[HELLO]车辆调换门店返回新商品ID, vehicleId:{0}, newId:{1}", vehicle.getId(), newThirdModelId);
            // 3.3 保存新的商品ID映射关系
            this.saveModelGoodsRelation(merchantId, vehicle.getVehicleModelId(), saasStoreId,
                    newThirdModelId);
            // 3.4 主动推送价格(这步必做，哈啰测会把老门店的商品价格，默认复制到新门店)
            Boolean priceResult = helloBaseService.syncPrice(vehicle.getVehicleModelId(), saasStoreId, merchantId);
            if (!Boolean.TRUE.equals(priceResult)) {
                // todo 尝试一些消息补偿 消息告知
            }
        }
    }

    @Override
    public void vehicleDeleteNotify(PlatformSyncParam platformSyncParam) {
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            logger.infov("[HELLO]哈啰车辆下线数据为空, platformSyncParam: {0}", platformSyncParam);
            return;
        }

        Span current = Span.current();
        Long merchantId = platformSyncParam.getMerchantId();
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]哈啰车辆下线数据为空, merchantId: {0}", merchantId);
            return;
        }

        VehicleInfoDTO vehicle = this.deserialize(platformSyncParam.getData(), VehicleInfoDTO.class);
        if (vehicle == null) {
            return;
        }

        // 查询车辆映射关系
        this.deleteHelloVehicle(vehicle.getId(), apiConnOpt.get(), current);
    }

    /**
     * 删除车辆
     */
    private void deleteHelloVehicle(Long vehicleId, ApiConnEntity apiConn, Span currentSpan) {
        Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(), apiConn.getMerchantId(),
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicleId);

        if (vehicleRelationOpt.isEmpty()) {
            logger.errorv("[HELLO]车辆未找到映射关系，无法操作hello车辆，内部vehicleId: {0}", vehicleId);
            return;
        }

        HelloVehicleIdReq dto = new HelloVehicleIdReq();
        dto.setCarId(vehicleRelationOpt.get().getThirdId());
        ResultResp<?> resp = helloClientWrapper.vehicleDeleteNotify(apiConn.getChannelVendorCode(), dto);
        if (ResultResp.isSuccess(resp)) {
            // 删除车辆mapping
            thirdVehicleIdRelationRepository.logicalDelete(vehicleRelationOpt.get().getId());
            currentSpan.setStatus(StatusCode.OK);
        } else {
            currentSpan.setStatus(StatusCode.ERROR, resp.getSubMsg());
        }
    }

    @Override
    public void batchVehicleOfflineNotify(List<Long> vehicleModelIds, List<Long> storeIds, Long merchantId) {
        if ((isEmpty(vehicleModelIds) && isEmpty(storeIds)) || merchantId == null) {
            return;
        }

        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]哈啰车辆下线数据为空, merchantId: {0}", merchantId);
            return;
        }

        // 粗暴拉全量
        GetlVehicleRequest getlVehicleRequest = new GetlVehicleRequest();
        getlVehicleRequest.setId(1L);
        getlVehicleRequest.setSize(Integer.MAX_VALUE);
        getlVehicleRequest.setVehicleModelIds(vehicleModelIds);
        getlVehicleRequest.setStoreIds(storeIds);
        getlVehicleRequest.setType("ALL");
        ApiResultResp<List<VehicleInfoDTO>> vehiclesResp = vehicleClient.getBasicVehicleInfo(merchantId,
                ChannelEnum.HELLO.getCode(), getlVehicleRequest);

        if (!ApiResultResp.isSuccess(vehiclesResp) || vehiclesResp.getData() == null
                || vehiclesResp.getData().isEmpty()) {
            logger.infov("[HELLO]没有需要同步的车辆, modelId:{0}, storeIds:{1}, merchantId:{2} ", vehicleModelIds, storeIds,
                    merchantId);
            return;
        }

        // 遍历车辆列表，调用vehicleNotify进行同步
        for (VehicleInfoDTO vehicle : vehiclesResp.getData()) {
            try {
                this.handleVehicleStatus(vehicle, merchantId, apiConnOpt.get());
            } catch (Exception e) {
                logger.errorv("[HELLO]处理车辆同步异常, vehicleId: {0}, error: {1}", vehicle.getId(), e.getMessage());
            }
        }
    }

    @Override
    public void compareHelloVehicleStatus(Long vehicleId, Long merchantId) {
        Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(), merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicleId);
        if (vehicleRelationOpt.isEmpty()) {
            logger.errorv("[HELLO]车辆未找到映射关系，无法操作hello车辆，内部vehicleId: {0}", vehicleId);
            return;
        }
    }

    @Override
    public SaasResultResp initHelloVehicleModel(Long merchantId) {
        try {
            List<VehicleBindEntity> allHelloBind = vehicleBindRepository.findAllByMerchantIdAndChannelId(merchantId,
                    ChannelEnum.HELLO.getCode());
            Span.current().setAttribute("初始化车型数量", String.valueOf(allHelloBind.size()));
            if (allHelloBind.isEmpty()) {
                return SaasResultResp.failed("1", "车型绑定数量为空");
            }

            // hello门店映射
            List<ThirdIdRelationEntity> helloStoreRelation = thirdIdRelationRepository.findByChannelIdAndMerchantId(
                    ChannelEnum.HELLO.getCode(),
                    merchantId,
                    OpenStoreEnum.IdRelationEnum.STORE.getType());
            if (helloStoreRelation.isEmpty()) {
                return SaasResultResp.failed("1", "门店映射为空");
            }

            // key为hello的子车系ID, value为SaaS车型id
            Map<String, List<Long>> helloToVehicleModelMap = allHelloBind.stream()
                    .collect(Collectors.groupingBy(VehicleBindEntity::getBindChannelVehicleSeryId,
                            Collectors.mapping(VehicleBindEntity::getVehicleModelId, Collectors.toList())));
            Map<String, Long> helloStoreMap = helloStoreRelation.stream()
                    .collect(Collectors.toMap(ThirdIdRelationEntity::getThirdId, ThirdIdRelationEntity::getSaasId));

            // todo 分页查询哈啰商品 保存映射到 third_vehicle_id_relation
            Set<String> hellStoreIds = helloStoreMap.keySet();

            // 分页查询哈啰商品
            int pageIndex = 1;
            int pageSize = 50;
            HelloGoodsListReq req = new HelloGoodsListReq();
            req.setMerchantId(apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO)
                    .map(ApiConnEntity::getChannelVendorCode)
                    .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                            ErrorCode.NOT_FOUND_MERCHANT.getDesc())));
            req.setSiteIds(hellStoreIds);
            req.setPageIndex(pageIndex);
            req.setPageSize(pageSize);

            // 获取第一页数据及总数
            ResultResp<HelloGoodsInfoList> goodsListResp = helloClientWrapper.goodsList(merchantId, req);
            if (Objects.isNull(goodsListResp) || !goodsListResp.isSuccess()) {
                helloBaseService.initFailV2(merchantId, goodsListResp.toString(), "拉取哈啰商品错误", 40);
                return SaasResultResp.failed(goodsListResp.getCode(), goodsListResp.getMsg());
            }

            HelloGoodsInfoList data = goodsListResp.getData();
            List<HelloGoodsInfoDetail> goodsList = Optional.ofNullable(data.getItems()).orElse(new ArrayList<>());
            Integer total = data.getTotal();

            // 计算总页数
            int totalPage = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;

            // 使用线程池并发处理剩余页
            if (totalPage > 1) {
                List<Future<List<HelloGoodsInfoDetail>>> futures = new ArrayList<>();

                for (int i = 2; i <= totalPage; i++) {
                    final int pageNum = i;
                    Future<List<HelloGoodsInfoDetail>> future = concurrentRequestExecutor.submit(() -> {
                        HelloGoodsListReq threadReq = new HelloGoodsListReq();
                        threadReq.setMerchantId(req.getMerchantId());
                        threadReq.setSiteIds(req.getSiteIds());
                        threadReq.setPageIndex(pageNum);
                        threadReq.setPageSize(pageSize);

                        ResultResp<HelloGoodsInfoList> threadResp = helloClientWrapper.goodsList(merchantId, threadReq);
                        if (Objects.isNull(threadResp) || !threadResp.isSuccess()) {
                            logger.errorv("[HELLO-INIT]获取哈啰商品列表失败, page: {0}, error: {1}", pageNum,
                                    threadResp.getMsg());
                            return new ArrayList<>();
                        }

                        return Optional.ofNullable(threadResp.getData().getItems()).orElse(new ArrayList<>());
                    });
                    futures.add(future);
                }

                // 获取所有线程执行结果
                for (Future<List<HelloGoodsInfoDetail>> future : futures) {
                    try {
                        goodsList.addAll(future.get());
                    } catch (InterruptedException | ExecutionException e) {
                        logger.errorv("[HELLO-INIT]并发获取哈啰商品列表异常", e);
                    }
                }
            }

            // 处理商品数据，保存映射关系
            for (HelloGoodsInfoDetail goods : goodsList) {
                try {
                    HelloGoodsInfoDetail.Vehicle vehicle = goods.getVehicle();
                    if (vehicle == null || vehicle.getModelId() == null) {
                        continue;
                    }
                    HelloGoodsInfoDetail.Site site = goods.getSite();
                    if (site == null || site.getSiteId() == null) {
                        continue;
                    }
                    Long saasStoreId = helloStoreMap.get(site.getSiteId());
                    if (saasStoreId == null) {
                        continue;
                    }

                    // 获取对应的SaaS车型ID列表
                    List<Long> saasModelIds = helloToVehicleModelMap.get(vehicle.getModelId());
                    if (saasModelIds == null || saasModelIds.isEmpty()) {
                        continue;
                    }

                    // 保存车型商品映射关系
                    for (Long saasModelId : saasModelIds) {
                        this.saveModelGoodsRelation(merchantId, saasModelId, saasStoreId, goods.getGoodsId());
                    }
                } catch (Exception e) {
                    logger.errorv("[HELLO-INIT]处理哈啰商品数据异常, goodsId: {0}", goods.getGoodsId());
                }
            }
            helloBaseService.initSuccessV2(merchantId, 40);
        } catch (Exception e) {
            logger.errorv("[HELLO-INIT]初始化哈啰商品是失败", e);
            helloBaseService.initFailV2(merchantId, e.getMessage(), "初始化哈啰商品是失败", 40);
        }
        return SaasResultResp.success();
    }

    @Override
    public SaasResultResp<VehicleInfoDTO> getHelloVehicleDetail(Long vehicleId, Long merchantId) {
        // 1. 验证商户是否开通Hello渠道
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]商户未开通哈啰渠道, merchantId: {0}", merchantId);
            return SaasResultResp.failed("7000", "商户未开通哈啰渠道");
        }

        // 2. 查询车辆映射关系
        Optional<ThirdVehicleIdRelationEntity> vehicleRelationOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(
                        ChannelEnum.HELLO.getCode(), merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        vehicleId);

        if (vehicleRelationOpt.isEmpty()) {
            logger.errorv("[HELLO]车辆未找到映射关系，无法查询哈啰车辆详情，内部vehicleId: {0}", vehicleId);
            return SaasResultResp.failed("5000", "车辆未在哈啰渠道注册");
        }

        // 3. 查询车辆详情
        String channelVendorCode = apiConnOpt.get().getChannelVendorCode();
        HelloVehicleInfoDetailReq req = new HelloVehicleInfoDetailReq();
        req.setCarId(vehicleRelationOpt.get().getThirdId());
        ResultResp<HelloVehicleInfoDTO> vehicleDetailResp = helloClientWrapper.vehicleDetail(channelVendorCode, req);

        if (!ResultResp.isSuccess(vehicleDetailResp)) {
            logger.errorv("[HELLO]哈啰车辆详情查询失败, vehicleId:{0}, resp:{1}", vehicleId, vehicleDetailResp);
            return SaasResultResp.failed("0002",
                    vehicleDetailResp.getMsg(),
                    vehicleDetailResp.getSubCode(),
                    vehicleDetailResp.getSubMsg());
        }

        HelloVehicleInfoDTO helloVehicle = vehicleDetailResp.getData();
        if (helloVehicle == null) {
            logger.errorv("[HELLO]哈啰车辆详情查询数据为空, vehicleId:{0}", vehicleId);
            return SaasResultResp.failed("5000", "哈啰车辆数据不存在");
        }

        // 5. 查询门店关系
        ThirdIdRelationEntity storeRelation = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndThirdId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenStoreEnum.IdRelationEnum.STORE.getType(),
                        helloVehicle.getSiteId())
                .orElse(null);
        if (storeRelation == null) {
            logger.errorv("[HELLO]未找到门店映射关系, vehicleId:{0}, siteId:{1}", vehicleId, helloVehicle.getSiteId());
            return SaasResultResp.failed("3000", "门店未在哈啰渠道映射");
        }

        // 6. 转换为Saas车辆信息DTO
        try {
            // 使用转换器将Hello车辆数据转换为SaaS车辆数据
            HelloVehicleInfoConverter converter = new HelloVehicleInfoConverter(
                    null, storeRelation);
            VehicleInfoDTO vehicleInfoDTO = converter.convert(helloVehicle);
            return SaasResultResp.success(vehicleInfoDTO);
        } catch (Exception e) {
            logger.errorv("[HELLO]转换哈啰车辆详情数据异常, vehicleId:{0}", vehicleId);
            return SaasResultResp.failed("0002", "转换哈啰车辆详情数据异常: " + e.getMessage());
        }
    }

    @Override
    public SaasResultResp<List<ThirdPlatformVehicleModelResp>> getAllHelloVehiclePlateNumber(Long merchantId,
            List<String> thirdStoreIds) {

        if (merchantId == null) {
            return SaasResultResp.failed("1", "参数缺失");
        }
        if (thirdStoreIds == null || thirdStoreIds.isEmpty()) {
            return SaasResultResp.success(Collections.emptyList());
        }

        // 查询hello商家的全部车辆
        Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                ChannelEnum.HELLO.getCode());
        if (apiConnOpt.isEmpty()) {
            logger.errorv("[HELLO]商户未开通哈啰渠道, merchantId: {0}", merchantId);
            return SaasResultResp.failed("7000", "商户未开通哈啰渠道");
        }

        // 查询哈啰所有车辆信息
        List<HelloVehicleInfoListResp.CarInfo> vehicleList = this.fetchStoreVehicles(merchantId,
                apiConnOpt.get().getChannelVendorCode(), thirdStoreIds);

        // 转换为ThirdPlatformVehicleModelResp对象
        List<ThirdPlatformVehicleModelResp> result = vehicleList.stream()
                .map(this::convertToThirdPlatformVehicleModelResp)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return SaasResultResp.success(result);
    }

    /**
     * 将哈啰车辆信息转换为平台通用车辆模型
     *
     * @param carInfo 哈啰车辆信息
     * @return 平台通用车辆模型
     */
    private ThirdPlatformVehicleModelResp convertToThirdPlatformVehicleModelResp(
            HelloVehicleInfoListResp.CarInfo carInfo) {
        if (carInfo == null) {
            return null;
        }

        ThirdPlatformVehicleModelResp resp = new ThirdPlatformVehicleModelResp();
        // 车系ID，使用seriesId
        resp.setVehicleSeriesId(carInfo.getSeriesId() != null ? carInfo.getModelId().toString() : "");
        String fullName = String.format("%s %s %s",
                Optional.ofNullable(carInfo.getBrandName()).orElse(""),
                Optional.ofNullable(carInfo.getSeriesName()).orElse(""),
                Optional.ofNullable(carInfo.getModelName()).orElse("")).trim();

        // 车型名称，使用modelName
        resp.setName(fullName);
        // 车型完整名称，使用品牌+系列+车型名称
        resp.setFullName(fullName);
        // 车牌号
        resp.setLicense(carInfo.getLicencePlate());
        // 翻译牌照类型
        resp.setLicenseType(this.convertToQingluLicenceType(carInfo.getLicenceType()));
        return resp;
    }

    public String displacementMapper(String displacement) {
        if (displacement != null && !"".equals(displacement)) {
            if ("纯电动".equals(displacement) || "纯电".equals(displacement))
                return "";
            Pattern pattern = Pattern.compile("\\.0"); // 去掉空格符合换行符
            Matcher matcher = pattern.matcher(displacement);
            return matcher.replaceAll("");
        } else {
            return Objects.nonNull(displacement) ? displacement : "";
        }
    }

    private String gearboxMapper(String gearbox) {
        if (gearbox.contains("手动")) {
            return "手动";
        } else if (gearbox.contains("自动")) {
            return "自动";
        } else {
            return "其他";
        }
    }

    private String fuelFormMapper(String fuelForm) {
        if ("增程式".equals(fuelForm)) {
            return "增程式";
        } else if ("油电混合".equals(fuelForm)) {
            return "混动";
        } else if ("氢燃料".equals(fuelForm)) {
            return "氢燃料电池";
        } else if ("汽油".equals(fuelForm) ||
                "汽油电驱".equals(fuelForm) ||
                "汽油+天然气".equals(fuelForm) ||
                "汽油+CNG".equals(fuelForm) ||
                "汽油+90V轻混系统".equals(fuelForm) ||
                "汽油+24V轻混系统".equals(fuelForm)) {
            return "汽油";
        } else if ("汽油+48V轻混系统".equals(fuelForm)) {
            return "汽油+48V轻混系统";
        } else if ("纯电动".equals(fuelForm)) {
            return "纯电";
        } else if ("柴油".equals(fuelForm)) {
            return "柴油";
        } else if ("插电式混合动力".equals(fuelForm)) {
            return "插电式混合动力";
        } else {
            return "其他";
        }
    }

    private String getHelloErrorMsg(String subMsgStr) {
        if (Strings.isNullOrEmpty(subMsgStr)) {
            return "";
        }
        try {
            VehicleSubMsg vehicleSubMsg = mapper.readValue(subMsgStr, VehicleSubMsg.class);
            return Optional.ofNullable(vehicleSubMsg).map(VehicleSubMsg::getProto)
                    .map(VehicleSubMsg.Proto::getMsg).orElse("");
        } catch (JsonProcessingException e) {
            return subMsgStr;
        }
    }

    private boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    private VehicleSubSeryEntity getVehicleSeryByModelId(Long vehicleModelId) {
        VehicleModelEntity vehicleModelEntity = vehicleModelRepository.findById(vehicleModelId);
        if (vehicleModelEntity == null) {
            return null;
        }
        return vehicleSubSeryRepository.findById(vehicleModelEntity.getVehicleSubSeryId());
    }

    @PreDestroy
    public void shutdown() {
        try {
            // 关闭定时任务线程池
            scheduledExecutor.shutdown();
            if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }

            // 关闭并发请求线程池
            concurrentRequestExecutor.shutdown();
            if (!concurrentRequestExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                concurrentRequestExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 如果等待被中断，强制关闭线程池
            scheduledExecutor.shutdownNow();
            concurrentRequestExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private Long convertToQingluLicenceType(Integer helloLicenceType) {
        if (Objects.isNull(helloLicenceType)) {
            return 0L;
        }
        return switch (helloLicenceType) {
            case 3 -> 1L; // 沪牌
            case 1 -> 3L; // 京牌
            case 7 -> 4L; // 粤A牌
            case 5 -> 5L; // 深牌
            default -> 0L; // 普通牌照
        };
    }

    private ThirdVehicleIdRelationEntity validateVehicleRelation(Long merchantId, String helloCarId) {
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndThirdId(
                        ChannelEnum.HELLO.getCode(),
                        merchantId,
                        OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                        helloCarId)
                .orElse(null);
    }

    @Override
    public SaasResultResp<OrderInventoryOccupyQueryResp> orderInventoryOccupyQuery(List<Long> orderIdList,
            Long merchantId) {
        Span span = Span.current();
        try {
            // 1. 验证商户是否开通Hello渠道
            Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId,
                    ChannelEnum.HELLO.getCode());
            if (apiConnOpt.isEmpty()) {
                logger.errorv("[HELLO]商户未开通哈啰渠道, merchantId: {0}", merchantId);
                span.setStatus(StatusCode.ERROR);
                return SaasResultResp.failed("7000", "商户未开通哈啰渠道");
            }

            // 2. 构建请求参数
            OrderInventoryOccupyQueryReq req = new OrderInventoryOccupyQueryReq();
            req.setOrderIdList(orderIdList);
            req.setMerchantId(apiConnOpt.get().getChannelVendorCode());

            // 3. 调用hello接口
            ResultResp<OrderInventoryOccupyQueryResp> resultResp = helloClientWrapper
                    .orderInventoryOccupyQuery(merchantId, req);

            // 4. 处理响应结果
            if (!ResultResp.isSuccess(resultResp)) {
                logger.errorv("[HELLO]哈啰订单库存占用查询失败, orderIdList:{0}, resp:{1}", orderIdList, resultResp);
                span.setStatus(StatusCode.ERROR);
                return SaasResultResp.failed("0002",
                        resultResp.getMsg(),
                        resultResp.getSubCode(),
                        resultResp.getSubMsg());
            }

            return SaasResultResp.success(resultResp.getData());
        } catch (Exception e) {
            logger.errorv("[HELLO]查询订单库存占用异常: {0}", e.getMessage(), e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return SaasResultResp.failed(ErrorCode.REMOTE_HELLO_ERROR.getCode(), "查询订单库存占用异常: " + e.getMessage());
        }
    }
}
