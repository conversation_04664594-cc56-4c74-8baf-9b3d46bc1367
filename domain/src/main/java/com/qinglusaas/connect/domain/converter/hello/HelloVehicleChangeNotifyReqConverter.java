package com.qinglusaas.connect.domain.converter.hello;

import com.qinglusaas.connect.infra.persistence.entity.vehicle.HelloSubSeryEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.ThirdVehicleIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleSubSeryEntity;
import com.qinglusaas.connect.infra.remote.hello.dto.InstallmentDTO;
import com.qinglusaas.connect.infra.remote.hello.vo.request.HelloVehicleInfoDTO;
import com.qinglusaas.connect.infra.util.UrlProxyUtil;
import com.ql.dto.vehicle.*;
import com.ql.enums.VehicleInfoEnums;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 哈啰车辆信息转换器
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
public class HelloVehicleChangeNotifyReqConverter extends AbstractConverter<VehicleInfoDTO, HelloVehicleInfoDTO> {

    private final HelloSubSeryEntity helloSubSeryEntity;
    private final ThirdVehicleIdRelationEntity catRelation;
    private final ThirdIdRelationEntity storeRelation;
    private final boolean skuSable;
    private final VehicleSubSeryEntity vehicleSubSeryEntity;
    private final UrlProxyUtil urlProxyUtil;

    @Override
    protected HelloVehicleInfoDTO doForward(VehicleInfoDTO vehicleInfoDTO) {
        HelloVehicleInfoDTO helloVehicleInfoDTO = new HelloVehicleInfoDTO();

        // 设置基本信息
        this.setBasicInfo(vehicleInfoDTO, helloVehicleInfoDTO);
        // 设置驾驶证信息
        this.setLicenseInfo(vehicleInfoDTO, helloVehicleInfoDTO);
        // 设置年检信息
        this.setInspectionInfo(vehicleInfoDTO, helloVehicleInfoDTO);
        // 设置商业保险信息
        setCommercialInsurance(vehicleInfoDTO, helloVehicleInfoDTO);
        // 设置交强险信息
        setCompulsoryInsurance(vehicleInfoDTO, helloVehicleInfoDTO);
        // 车辆来源全部设置为5-借调, todo 再根据是否售卖情况设置
        helloVehicleInfoDTO.setHoldWay(5);
        helloVehicleInfoDTO
                .setInstallmentList(InstallmentDTO.getByVehicleStatus(skuSable && vehicleInfoDTO.isSalable()));

        // 设置一个虚假的ocr结果
        HelloVehicleInfoDTO.OcrInfo ocrInfo = new HelloVehicleInfoDTO.OcrInfo();
        helloVehicleInfoDTO.setLicenseOcrInfo(ocrInfo);
        helloVehicleInfoDTO.setVinOcrInfo(ocrInfo);
        return helloVehicleInfoDTO;
    }

    private void setBasicInfo(VehicleInfoDTO vehicleInfoDTO, HelloVehicleInfoDTO helloVehicleInfoDTO) {
        // 设置车辆ID（使用 ThirdVehicleIdRelation 中的映射）
        if (catRelation != null) {
            helloVehicleInfoDTO.setGuid(catRelation.getThirdId());
        }

        // 设置站点ID（使用 ThirdIdRelation 中的映射）
        if (storeRelation != null) {
            helloVehicleInfoDTO.setSiteId(storeRelation.getThirdId());
        }
        helloVehicleInfoDTO.setBrandId(helloSubSeryEntity.getBrandId());
        helloVehicleInfoDTO.setSeriesId(helloSubSeryEntity.getSeriesId());
        helloVehicleInfoDTO.setModelId(helloSubSeryEntity.getHelloId());
        BigDecimal price = vehicleSubSeryEntity.getPrice();
        if (price != null && price.compareTo(BigDecimal.ZERO) != 0) {
            String priceStr = price.multiply(BigDecimal.valueOf(10000)).setScale(2, RoundingMode.HALF_UP).toString();
            helloVehicleInfoDTO.setSuggestPrice(priceStr);
            helloVehicleInfoDTO.setBuyPrice(priceStr);
        } else {
            helloVehicleInfoDTO.setSuggestPrice("1000000");
            helloVehicleInfoDTO.setBuyPrice("1000000");
        }
        // -------无关字段 全部传默认值------------
        helloVehicleInfoDTO.setHasFinalPayment(0);
        helloVehicleInfoDTO.setHasFirstPayment(0);
        helloVehicleInfoDTO.setInstallment(0);

        CarBaseInfo carBaseInfo = vehicleInfoDTO.getCarBaseInfo();
        if (carBaseInfo != null) {
            // todo 颜色传rgb
            helloVehicleInfoDTO.setCarColor("#111111");
            String mileage = carBaseInfo.getMileage();
            if (mileage != null && !mileage.trim().isEmpty()) {
                helloVehicleInfoDTO.setUpMileage(Long.parseLong(mileage));
            }
        }
    }

    public void setCommercialInsurance(VehicleInfoDTO vehicleInfoDTO, HelloVehicleInfoDTO helloVehicleInfoDTO) {
        if (vehicleInfoDTO.getCommercialInsurance() == null ||
                isEmptyList(vehicleInfoDTO.getCommercialInsurance().getCommercialDetailInfo())) {
            return;
        }
        InsuranceDetailInfo detailInfo = vehicleInfoDTO.getCommercialInsurance().getCommercialDetailInfo().get(0);
        String endTime = detailInfo.getEndTime();
        if (endTime != null) {
            helloVehicleInfoDTO.setCommercialInsuranceEndTime(dateFormat(endTime.split(" ")[0], true));
        }
        helloVehicleInfoDTO.setCommercialInsuranceRecord(
                convertToInsuranceRecords(vehicleInfoDTO.getCommercialInsurance().getCommercialDetailInfo()));
    }

    private void setInspectionInfo(VehicleInfoDTO vehicleInfoDTO, HelloVehicleInfoDTO helloVehicleInfoDTO) {
        AnnualCheckInfo annualCheckInfo = vehicleInfoDTO.getAnnualCheckInfo();
        if (annualCheckInfo != null) {
            String activeData = annualCheckInfo.getActiveData();
            if (activeData != null && !activeData.trim().isEmpty()) {
                LocalDate parse = LocalDate.parse(activeData);
                helloVehicleInfoDTO.setInspectionEndTime(parse.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            }
            helloVehicleInfoDTO.setInspectPicList(
                    Collections.singletonList(annualCheckInfo.getUrl()));
        }
    }

    private void setLicenseInfo(VehicleInfoDTO vehicleInfoDTO, HelloVehicleInfoDTO helloVehicleInfoDTO) {
        DrivingLicenseInfo drivingLicenseInfo = vehicleInfoDTO.getDrivingLicenseInfo();
        if (drivingLicenseInfo == null) {
            return;
        }
        String registerDate = drivingLicenseInfo.getRegisterDate();

        // 原有驾驶证信息设置
        helloVehicleInfoDTO.setLicenceType(changeToHelloLicenceType(drivingLicenseInfo.getLicenseType()));
        if (registerDate != null) {
            helloVehicleInfoDTO.setLicenceRegisterDate(registerDate.split(" ")[0]);
        }
        helloVehicleInfoDTO.setLicensePlate(drivingLicenseInfo.getPlateNumber());
        helloVehicleInfoDTO.setVin(drivingLicenseInfo.getVin());
        helloVehicleInfoDTO.setEngineNo(drivingLicenseInfo.getEngineNo());
        helloVehicleInfoDTO.setLicenceUseType(convertUsage(drivingLicenseInfo));
        helloVehicleInfoDTO.setDriverLicencePicList(drivingLicenseInfo.getUrlList());
    }

    // 转换使用性质字段
    public static int convertUsage(DrivingLicenseInfo drivingLicenseInfo) {
        // 使用性质字段转换
        return switch (drivingLicenseInfo.getUseCharacter()) {
            case NON_OPERATION, OPERATION_TO_NON -> 1;
            case RENT, TOURIST_TRANS, BOOKING_TAXI -> 0;
            default -> -1;
        };
    }

    public void setCompulsoryInsurance(VehicleInfoDTO vehicleInfoDTO, HelloVehicleInfoDTO helloVehicleInfoDTO) {
        if (vehicleInfoDTO.getCompulsoryInsurance() == null ||
                isEmptyList(vehicleInfoDTO.getCompulsoryInsurance().getCompulsoryDetailInfo())) {
            return;
        }

        InsuranceDetailInfo detailInfo = vehicleInfoDTO.getCompulsoryInsurance().getCompulsoryDetailInfo().get(0);
        if (detailInfo.getEndTime() != null) {
            helloVehicleInfoDTO.setCompulsoryInsuranceEndTime(dateFormat(detailInfo.getEndTime().split(" ")[0], true));
        }
        helloVehicleInfoDTO.setCompulsoryInsuranceRecord(
                convertToInsuranceRecords(vehicleInfoDTO.getCompulsoryInsurance().getCompulsoryDetailInfo()));
    }

    private List<HelloVehicleInfoDTO.InsuranceRecord> convertToInsuranceRecords(
            List<InsuranceDetailInfo> detailInfos) {
        if (isEmptyList(detailInfos)) {
            return Collections.emptyList();
        }
        return detailInfos.stream()
                .map(this::convertToInsuranceRecord)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private HelloVehicleInfoDTO.InsuranceRecord convertToInsuranceRecord(InsuranceDetailInfo info) {
        LocalDate now = LocalDate.now();

        // 哈啰只能传一个在保的保险
        HelloVehicleInfoDTO.InsuranceRecord record = new HelloVehicleInfoDTO.InsuranceRecord();
        String endTime = info.getEndTime();
        record.setTimeEnd(dateFormat(endTime.split(" ")[0], true));
        LocalDate endDate = LocalDate.parse(endTime.split(" ")[0]);
        if (endDate.isBefore(now)) {
            return null;
        }
        String startTime = info.getStartTime();
        LocalDate startDate = LocalDate.parse(startTime.split(" ")[0]);
        if (startDate.isAfter(now)) {
            return null;
        }
        record.setTimeStart(dateFormat(startTime.split(" ")[0], true));

        record.setVin(info.getVin());

        // -------todo SaaS已无车损起配额，需要确认是否影响哈啰售卖------------
        record.setVehicleDamageInsurance("0");
        record.setIdNumber(info.getInsuranceHolderId());
        record.setInsureCompany(info.getInsuranceCompany());

        List<String> urlList = info.getUrlList() != null ? info.getUrlList() : Collections.emptyList();
        urlList = urlList.stream().map(this::getDomain).collect(Collectors.toList());
        List<String> proxyUrl = urlProxyUtil.replaceUrlDomains(UrlProxyUtil::isPdf, urlList);

        record.setPicList(proxyUrl);
        record.setInsureNo(info.getInsuranceId());
        record.setInsureFee("0");
        record.setPolicyOcrInfo(new HelloVehicleInfoDTO.OcrInfo());
        record.setThirdPartyInsurance(convertToInsuranceFee(info.getCoverageLevel()));
        return record;
    }

    /**
     * 传入一个完整的url，根据？ 去掉路由参数
     *
     * @param url
     * @return
     */
    private String getDomain(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        // 去掉查询参数（? 后面的部分）
        return url.split("\\?")[0];
    }

    private static boolean isEmptyList(List<?> list) {
        return list == null || list.isEmpty();
    }

    private int convertSourceToHoldWay(Byte vehicleSource) {
        if (vehicleSource == null) {
            return 1;
        }
        // 自有、普通车辆 -》 自购
        if (VehicleInfoEnums.VehicleSourceEnum.SELF_OWN.getSource().equals(vehicleSource)
                || VehicleInfoEnums.VehicleSourceEnum.PLATFORM_VEHICLE.getSource().equals(vehicleSource)) {
            return 1;
        }
        // 长期借调、托管 -》 其他
        if (VehicleInfoEnums.VehicleSourceEnum.LOAN_LONG_TERM.getSource().equals(vehicleSource)
                || VehicleInfoEnums.VehicleSourceEnum.TRUSTEESHIP.getSource().equals(vehicleSource)) {
            return 4;
        }
        // 临时借调
        if (VehicleInfoEnums.VehicleSourceEnum.TEMPORARY_LOAN.getSource().equals(vehicleSource)) {
            return 5;
        }
        // 挂靠
        if (VehicleInfoEnums.VehicleSourceEnum.AFFILIATION.getSource().equals(vehicleSource)) {
            return 6;
        }

        // 融资租赁
        if (VehicleInfoEnums.VehicleSourceEnum.FINANCE_LEASE.getSource().equals(vehicleSource)) {
            return 3;
        }
        return 4;
    }

    public static int changeToHelloLicenceType(Long licenseType) {
        if (Objects.isNull(licenseType)) {
            return 0;
        }
        return switch (licenseType.intValue()) {
            case 1 -> 3; // 沪牌
            case 3 -> 1; // 京牌
            case 4 -> 7; // 粤A牌
            case 5 -> 5; // 深牌
            default -> 0; // 普通牌照
        };
    }

    // 将这三者险枚举翻译成金额
    private static String convertToInsuranceFee(String insuranceCoverage) {
        if (insuranceCoverage == null) {
            return null;
        }
        return switch (insuranceCoverage) {
            case "TWENTY_MILLI" -> "200000";
            case "THIRTY_MILI" -> "300000";
            case "FIFTY_MILI" -> "500000";
            case "HUNDRED_MILI" -> "1000000";
            case "HUNDRED_FIFTY_MILI" -> "1500000";
            case "TWO_HUNDRED_MILI" -> "2000000";
            case "ABOVE_TWO_HUNDRED_MILI" -> "2000000";
            case "THREE_HUNDRED_MILI" -> "3000000";
            default -> null;
        };
    }
}
