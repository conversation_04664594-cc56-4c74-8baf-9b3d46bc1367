package com.qinglusaas.connect.domain.converter.ctrip;

import java.util.ArrayList;
import java.util.List;

import com.qinglusaas.connect.client.ctrip.dto.PriceDailyRangeDTO;

/**
 * 价格日历清洗转换器
 * 功能：将连续价格相同的对象合并为一个对象
 * 
 * <AUTHOR>
 * @date 2024/01/XX
 */
public class PriceDailyRangeCleanerConverter
        extends AbstractConverter<List<PriceDailyRangeDTO>, List<PriceDailyRangeDTO>> {

    private final static PriceDailyRangeCleanerConverter CONVERTER = new PriceDailyRangeCleanerConverter();

    public static List<PriceDailyRangeDTO> forward(List<PriceDailyRangeDTO> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        return CONVERTER.convert(list);
    }

    @Override
    protected List<PriceDailyRangeDTO> doForward(List<PriceDailyRangeDTO> priceDailyRangeList) {
        if (priceDailyRangeList == null || priceDailyRangeList.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果只有一个元素，直接返回
        if (priceDailyRangeList.size() == 1) {
            PriceDailyRangeDTO priceDailyRangeDTO = priceDailyRangeList.get(0);
            if (null != priceDailyRangeDTO) {
                if (null != priceDailyRangeDTO.getAllDay() && priceDailyRangeDTO.getAllDay()) {
                    priceDailyRangeDTO.setPartDailyPrice(null);
                }
                priceDailyRangeDTO.setAllDay(null);
            }
            return new ArrayList<>(priceDailyRangeList);
        }

        List<PriceDailyRangeDTO> cleanedList = new ArrayList<>();

        PriceDailyRangeDTO currentGroup = null;
        int consecutiveCount = 0;
        int totalQuantity = 0;

        for (int i = 0; i < priceDailyRangeList.size(); i++) {
            PriceDailyRangeDTO current = priceDailyRangeList.get(i);

            if (currentGroup == null) {
                // 开始新的分组
                currentGroup = new PriceDailyRangeDTO();
                currentGroup.setAllDay(current.getAllDay());
                currentGroup.setStartDate(current.getStartDate());
                currentGroup.setEndDate(current.getEndDate());
                currentGroup.setWholeDailyPrice(current.getWholeDailyPrice());
                currentGroup.setPartDailyPrice(current.getPartDailyPrice());
                currentGroup.setQuantity(current.getQuantity());
                consecutiveCount = 1;
                totalQuantity = current.getQuantity() != null ? current.getQuantity() : 0;
            } else {
                // 检查是否与当前分组的价格相同
                boolean samePrices = isSamePrices(currentGroup, current);

                if (samePrices) {
                    // 价格相同，合并到当前分组
                    currentGroup.setEndDate(current.getEndDate());
                    consecutiveCount++;
                    totalQuantity += (current.getQuantity() != null ? current.getQuantity() : 0);
                } else {
                    // 价格不同，结束当前分组
                    finishCurrentGroup(cleanedList, currentGroup, consecutiveCount, totalQuantity);

                    // 开始新的分组
                    currentGroup = new PriceDailyRangeDTO();
                    currentGroup.setAllDay(current.getAllDay());
                    currentGroup.setStartDate(current.getStartDate());
                    currentGroup.setEndDate(current.getEndDate());
                    currentGroup.setWholeDailyPrice(current.getWholeDailyPrice());
                    currentGroup.setPartDailyPrice(current.getPartDailyPrice());
                    currentGroup.setQuantity(current.getQuantity());
                    consecutiveCount = 1;
                    totalQuantity = current.getQuantity() != null ? current.getQuantity() : 0;
                }
            }
        }

        // 处理最后一个分组
        if (currentGroup != null) {
            finishCurrentGroup(cleanedList, currentGroup, consecutiveCount, totalQuantity);
        }

        return cleanedList;
    }

    /**
     * 判断两个价格对象是否价格相同
     */
    private boolean isSamePrices(PriceDailyRangeDTO group, PriceDailyRangeDTO current) {
        // 比较wholeDailyPrice
        boolean sameWholePrice = (group.getWholeDailyPrice() == null && current.getWholeDailyPrice() == null) ||
                (group.getWholeDailyPrice() != null && group.getWholeDailyPrice().equals(current.getWholeDailyPrice()));

        // 比较partDailyPrice
        boolean samePartPrice = (group.getPartDailyPrice() == null && current.getPartDailyPrice() == null) ||
                (group.getPartDailyPrice() != null && group.getPartDailyPrice().equals(current.getPartDailyPrice()));

        // 检查quantity是否都为1
        boolean groupQuantityIsOne = group.getQuantity() != null && group.getQuantity().equals(1);
        boolean currentQuantityIsOne = current.getQuantity() != null && current.getQuantity().equals(1);

        return sameWholePrice && samePartPrice && groupQuantityIsOne && currentQuantityIsOne;
    }

    /**
     * 完成当前分组的处理
     */
    private void finishCurrentGroup(List<PriceDailyRangeDTO> cleanedList, PriceDailyRangeDTO currentGroup,
            int consecutiveCount, int totalQuantity) {
        if (null != currentGroup.getAllDay() && currentGroup.getAllDay()) {
            currentGroup.setPartDailyPrice(null);
        }
        currentGroup.setAllDay(null);
        if (consecutiveCount > 1) {
            // 连续对象大于1，使用合并后的数据，quantity为总数量
            currentGroup.setQuantity(1);
            cleanedList.add(currentGroup);
        } else {
            // 只有1个对象，直接添加原对象
            cleanedList.add(currentGroup);
        }
    }
}