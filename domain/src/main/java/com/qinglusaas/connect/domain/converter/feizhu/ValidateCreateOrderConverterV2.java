package com.qinglusaas.connect.domain.converter.feizhu;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import javax.enterprise.context.ApplicationScoped;

import lombok.extern.slf4j.Slf4j;

import com.qinglusaas.connect.client.common.constants.ChannelEnum;
import com.qinglusaas.connect.client.common.error.ClientName;
import com.qinglusaas.connect.client.feizhu.dto.ThirdExtInfo;
import com.qinglusaas.connect.client.feizhu.vo.request.CreateOrderRequest;
import com.qinglusaas.connect.client.feizhu.vo.request.ValidateCreateOrderRequest;
import com.qinglusaas.connect.client.feizhu.vo.request.ValidateCreateOrderRequest.PromotionDetail;
import com.qinglusaas.connect.client.saas.constants.PickUpDropOffType;
import com.qinglusaas.connect.client.saas.dto.PointDTO;
import com.qinglusaas.connect.client.saas.error.ErrorCode;
import com.qinglusaas.connect.domain.util.DateTimeUtil;
import com.qinglusaas.connect.domain.util.PriceTransUtil;
import com.qinglusaas.connect.infra.exception.BizException;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBindEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBindEntity_;
import com.qinglusaas.connect.infra.remote.saas.contants.OrderEnum;
import com.qinglusaas.connect.infra.remote.saas.contants.ServiceItemEnum;
import com.qinglusaas.connect.infra.remote.saas.contants.UserEnum;
import com.qinglusaas.connect.infra.remote.saas.dto.DropOffStoreDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.PickUpStoreDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.UserDTO;
import com.qinglusaas.connect.infra.remote.saas.vo.request.ValidateCreateOrderReq;

import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/3, Friday
 **/
@Slf4j
@ApplicationScoped
public class ValidateCreateOrderConverterV2 extends AbstractConverter<ValidateCreateOrderRequest, ValidateCreateOrderReq> {

    private static final String ANXIN_ORDER_PROMOTION_ID = "trustRentOnePriceDiscount";
    private static final int ANXIN_ORDER_INVESTOR = 2;

    @Override
    protected ValidateCreateOrderReq doForward(ValidateCreateOrderRequest validateCreateOrderRequest) {
        ValidateCreateOrderReq validateCreateOrderReq = new ValidateCreateOrderReq();
        return getValidateCreateOrderReq(validateCreateOrderRequest, validateCreateOrderReq);
    }

    public ValidateCreateOrderReq getValidateCreateOrderReq(ValidateCreateOrderRequest validateCreateOrderRequest, ValidateCreateOrderReq validateCreateOrderReq) {
        VehicleBindEntity vehicleBindEntity = (VehicleBindEntity) VehicleBindEntity.find(VehicleBindEntity_.BIND_CHANNEL_VEHICLE_ID + " = ?1 and " + VehicleBindEntity_.CHANNEL_ID + " = ?2", validateCreateOrderRequest.getCarUseInfo().getOutCarId(),
                ChannelEnum.NEW_FEI_ZHU.getCode())
                .firstResultOptional().orElseThrow(() -> new BizException(ClientName.FEIZHU, ErrorCode.NOT_FOUND_VEHICLE.getCode(), ErrorCode.NOT_FOUND_VEHICLE.getDesc()));
        // 车型ID
        validateCreateOrderReq.setVehicleModelId(vehicleBindEntity.getVehicleModelId());
        if (Integer.valueOf(1).equals(validateCreateOrderRequest.getPriceInfo().getPayType())) {
            // 在线付款
            validateCreateOrderReq.setPayMode(OrderEnum.PayMode.ONLINE);
        } else if (Integer.valueOf(2).equals(validateCreateOrderRequest.getPriceInfo().getPayType())) {
            // 到店付款
            validateCreateOrderReq.setPayMode(OrderEnum.PayMode.IN_STORE);
        } else {
            throw new BizException(ClientName.FEIZHU, ErrorCode.PAY_TYPE_ERROR.getCode(), ErrorCode.PAY_TYPE_ERROR.getDesc());
        }
        // 实付金额
        String payment = validateCreateOrderRequest.getPriceInfo().getPayment();
        if (Objects.nonNull(payment)) {
            BigDecimal bigDecimal = BigDecimal.valueOf(Double.valueOf(payment));
            validateCreateOrderReq.setPayAmount(PriceTransUtil.yuan2cent(bigDecimal));
        }
        // 应付金额
        String totalFee = validateCreateOrderRequest.getPriceInfo().getTotalFee();
        if (Objects.nonNull(totalFee)) {
            BigDecimal bigDecimal = BigDecimal.valueOf(Double.valueOf(totalFee));
            validateCreateOrderReq.setReceivableAmount(PriceTransUtil.yuan2cent(bigDecimal));
        }
        // 价格码
        validateCreateOrderReq.setPriceKey(validateCreateOrderRequest.getPriceInfo().getPriceMark());
        // 驾驶员信息
        CreateOrderRequest.TravellerInfo travellerInfo = validateCreateOrderRequest.getTravellerInfo();
        if (null != travellerInfo) {
            UserDTO userDTO = new UserDTO();
            userDTO.setName(travellerInfo.getName());
            userDTO.setMobile(travellerInfo.getPhone());
            // 0:身份证 1:护照 2:学生证 3:军官证 4:回乡证 5:台胞证 6:港澳通行证 10:警官证 11:士兵证 12:台湾通行证
            Integer credentialType = travellerInfo.getCredentialType();
            if (Integer.valueOf(0).equals(credentialType)) {
                userDTO.setIdType(UserEnum.IdTypeEnum.ID_CARD);
            } else if (Integer.valueOf(1).equals(credentialType)) {
                userDTO.setIdType(UserEnum.IdTypeEnum.PASSPORT);
            } else if (Integer.valueOf(5).equals(credentialType)) {
                userDTO.setIdType(UserEnum.IdTypeEnum.TAIWAN);
            } else if (Integer.valueOf(4).equals(credentialType)) {
                userDTO.setIdType(UserEnum.IdTypeEnum.RETURN_HOME);
            }
            userDTO.setIdNo(travellerInfo.getCredentialNo());
            validateCreateOrderReq.setPartnerUser(userDTO);
        }
        // 取车/还车
        CreateOrderRequest.CarUseInfo carUseInfo = validateCreateOrderRequest.getCarUseInfo();
        if (Objects.nonNull(carUseInfo)) {
            AtomicReference<Long> pickUpCityId = new AtomicReference<>();
            AtomicReference<Long> returnCityId = new AtomicReference<>();
            AtomicReference<Integer> returnDeliveryServiceType = new AtomicReference<>();
            AtomicReference<Integer> pickUpDeliveryServiceType = new AtomicReference<>();
            AtomicReference<Long> pickUpServiceCircleId = new AtomicReference<>();
            AtomicReference<Long> returnServiceCircleId = new AtomicReference<>();
            AtomicReference<String> pickUpAddr = new AtomicReference<>(null);
            AtomicReference<String> pickOffAddr = new AtomicReference<>(null);
            AtomicReference<PointDTO> pickUpPoint = new AtomicReference<>(null);
            AtomicReference<PointDTO> pickOffPoint = new AtomicReference<>(null);
            //if (Objects.isNull(carUseInfo.getOriginCityCode()) && Objects.isNull(carUseInfo.getReturnCityCode())) {
            try {
                String thirdExtInfos = validateCreateOrderRequest.getCarUseInfo().getThirdExtInfos();
                // convert thirdExtInfos to ThirdExtInfo
                if (Objects.nonNull(thirdExtInfos)) {
                    ThirdExtInfo thirdExtInfo = Json.decodeValue(thirdExtInfos, ThirdExtInfo.class);
                    if (Objects.nonNull(thirdExtInfo.getThirdExt())) {
                        JsonObject jsonObject = new JsonObject(thirdExtInfo.getThirdExt());
                        if (Objects.nonNull(jsonObject)) {
                            returnDeliveryServiceType.set(jsonObject.getString("returnDeliveryServiceType", null) == null ? null : Integer.parseInt(jsonObject.getString("returnDeliveryServiceType")));
                            pickUpDeliveryServiceType.set(jsonObject.getString("pickUpDeliveryServiceType", null) == null ? null : Integer.parseInt(jsonObject.getString("pickUpDeliveryServiceType")));
                            pickUpCityId.set(jsonObject.getString("pickUpCityId", null) == null ? null : Long.parseLong(jsonObject.getString("pickUpCityId")));
                            returnCityId.set(jsonObject.getString("returnCityId", null) == null ? null : Long.parseLong(jsonObject.getString("returnCityId")));
                            pickUpServiceCircleId.set(jsonObject.getString("pickUpServiceCircleId", null) == null ? null : Long.parseLong(jsonObject.getString("pickUpServiceCircleId")));
                            returnServiceCircleId.set(jsonObject.getString("returnServiceCircleId", null) == null ? null : Long.parseLong(jsonObject.getString("returnServiceCircleId")));
                        } else {
                            throw new BizException(ClientName.FEIZHU, ErrorCode.MODEL_VALID.getCode(), ErrorCode.MODEL_VALID.getDesc());
                        }
                    }
                    if (Objects.nonNull(thirdExtInfo.getAddressInfos())) {
                        JsonArray jsonArray = new JsonArray(thirdExtInfo.getAddressInfos());
                        if (Objects.nonNull(jsonArray)) {
                            for (int i = 0; i < jsonArray.size(); i++) {
                                JsonObject jsonObject = jsonArray.getJsonObject(i);
                                if (Objects.nonNull(jsonObject)) {
                                    String positionType = jsonObject.getString("positionType");
                                    if (Objects.nonNull(positionType)) {
                                        if ("pickUp".equals(positionType)) {
                                            pickUpAddr.set(jsonObject.getString("positionName"));
                                            pickUpPoint.set(new PointDTO(Double.parseDouble(jsonObject.getString("longitude")), Double.parseDouble(jsonObject.getString("latitude"))));
                                        } else if ("dropOff".equals(positionType)) {
                                            pickOffAddr.set(jsonObject.getString("positionName"));
                                            pickOffPoint.set(new PointDTO(Double.parseDouble(jsonObject.getString("longitude")), Double.parseDouble(jsonObject.getString("latitude"))));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("raptor fz ValidateCreateOrder error:{}", e.getMessage());
                throw new BizException(ClientName.FEIZHU, ErrorCode.NOT_FOUND_CITY.getCode(), ErrorCode.NOT_FOUND_CITY.getDesc());
            }
            if (Objects.isNull(pickUpCityId.get()) && Objects.isNull(returnCityId.get())) {
                throw new BizException(ClientName.FEIZHU, ErrorCode.NOT_FOUND_CITY.getCode(), ErrorCode.NOT_FOUND_CITY.getDesc());
            }

            PickUpStoreDTO pickUpStoreDTO = new PickUpStoreDTO();
            pickUpStoreDTO.setDatetime(DateTimeUtil.formatToDate(carUseInfo.getCarUseTime(), DateTimeUtil.PATTERN));
            pickUpStoreDTO.setStoreId(Long.parseLong(carUseInfo.getOriginStoreCode()));
            pickUpStoreDTO.setCityId(pickUpCityId.get());
            pickUpStoreDTO.setAddress(Objects.nonNull(pickUpAddr.get()) ? pickUpAddr.get() : carUseInfo.getPickUpAddr().getAddress());
            pickUpStoreDTO.setPoint(Objects.nonNull(pickUpPoint.get()) ? pickUpPoint.get() : new PointDTO(Double.parseDouble(carUseInfo.getPickUpAddr().getLongitude()), Double.parseDouble(carUseInfo.getPickUpAddr().getLatitude())));
            pickUpStoreDTO.setServiceCircleIds(pickUpServiceCircleId.get() == null ? null : Collections.singletonList(pickUpServiceCircleId.get()));
            ServiceItemEnum.DeliveryServiceType pickUpServiceType = ServiceItemEnum.DeliveryServiceType.fromCode(pickUpDeliveryServiceType.get());
            if (Objects.nonNull(pickUpServiceType)) {
                switch (pickUpServiceType) {
                    case FREE_SHUTTLE:
                        pickUpStoreDTO.setPickUpDropOffType(PickUpDropOffType.FREE_PICKUP_DROP);
                        break;
                    case ON_DOOR:
                        pickUpStoreDTO.setPickUpDropOffType(PickUpDropOffType.DOOR_PICKUP_DROP);
                        break;
                    case IN_STORE:
                        pickUpStoreDTO.setPickUpDropOffType(PickUpDropOffType.STORE_SELF_PICKUP_DROP);
                        break;
                }
            }
            validateCreateOrderReq.setPickUpStore(pickUpStoreDTO);

            DropOffStoreDTO dropOffStoreDTO = new DropOffStoreDTO();
            dropOffStoreDTO.setDatetime(DateTimeUtil.formatToDate(carUseInfo.getCarReturnTime(), DateTimeUtil.PATTERN));
            dropOffStoreDTO.setStoreId(Long.parseLong(carUseInfo.getReturnStoreCode()));
            dropOffStoreDTO.setCityId(returnCityId.get());
            dropOffStoreDTO.setAddress(Objects.nonNull(pickOffAddr.get()) ? pickOffAddr.get() : carUseInfo.getPickOffAddr().getAddress());
            dropOffStoreDTO.setPoint(Objects.nonNull(pickOffPoint.get()) ? pickOffPoint.get() : new PointDTO(Double.parseDouble(carUseInfo.getPickOffAddr().getLongitude()), Double.parseDouble(carUseInfo.getPickOffAddr().getLatitude())));
            dropOffStoreDTO.setServiceCircleIds(returnServiceCircleId.get() == null ? null : Collections.singletonList(returnServiceCircleId.get()));
            ServiceItemEnum.DeliveryServiceType returnServiceType = ServiceItemEnum.DeliveryServiceType.fromCode(returnDeliveryServiceType.get());
            if (Objects.nonNull(returnServiceType)) {
                switch (returnServiceType) {
                    case FREE_SHUTTLE:
                        dropOffStoreDTO.setPickUpDropOffType(PickUpDropOffType.FREE_PICKUP_DROP);
                        break;
                    case ON_DOOR:
                        dropOffStoreDTO.setPickUpDropOffType(PickUpDropOffType.DOOR_PICKUP_DROP);
                        break;
                    case IN_STORE:
                        dropOffStoreDTO.setPickUpDropOffType(PickUpDropOffType.STORE_SELF_PICKUP_DROP);
                        break;
                }
            }
            validateCreateOrderReq.setDropOffStore(dropOffStoreDTO);
        } else {
            throw new BizException(ClientName.FEIZHU, ErrorCode.NOT_FOUND_CITY.getCode(), ErrorCode.NOT_FOUND_CITY.getDesc());
        }
        // 附加服务项
        String addedServices = validateCreateOrderRequest.getPriceInfo().getAddedServices();
        if (Objects.nonNull(addedServices)) {
            String[] addedServiceList = addedServices.split(",");
            List<String> ads = new ArrayList<>();
            Collections.addAll(ads, addedServiceList);
            validateCreateOrderReq.setAddedServices(ads);
        }
        AtomicBoolean isAnxinOrder = new AtomicBoolean(false);
        List<PromotionDetail> promotionDetails = validateCreateOrderRequest.getPromotionDetails();
        if (Objects.nonNull(promotionDetails) && !promotionDetails.isEmpty()) {
            List<com.qinglusaas.connect.infra.remote.saas.dto.PromotionDetailDTO> promotionDetailList = promotionDetails.stream().map(promotionDetail -> {
                if(null != promotionDetail.getInvestor() && promotionDetail.getInvestor() == ANXIN_ORDER_INVESTOR && ANXIN_ORDER_PROMOTION_ID.equals(promotionDetail.getId())) {
                    isAnxinOrder.set(true);
                }
                com.qinglusaas.connect.infra.remote.saas.dto.PromotionDetailDTO promotionDetail1 = new com.qinglusaas.connect.infra.remote.saas.dto.PromotionDetailDTO();
                promotionDetail1.setName(promotionDetail.getName());
                promotionDetail1.setId(promotionDetail.getId());
                promotionDetail1.setCouponValue(promotionDetail.getCouponValue());
                return promotionDetail1;
            }).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            validateCreateOrderReq.setPromotionDetails(promotionDetailList);
        }
        validateCreateOrderReq.setAnxinOrder(isAnxinOrder.get());
        return validateCreateOrderReq;
    }


    @Override
    protected ValidateCreateOrderRequest doBackward(ValidateCreateOrderReq validateCreateOrderReq) {
        return super.doBackward(validateCreateOrderReq);
    }

    public static void main(String[] a){
        String thirdExtInfos = "\"{\"addressInfos\":\"[{\\\"cityId\\\":\\\"460100\\\",\\\"cityName\\\":\\\"海口\\\",\\\"countryCode\\\":\\\"CN\\\",\\\"countryName\\\":\\\"中国\\\",\\\"latitude\\\":\\\"19.9425070\\\",\\\"longitude\\\":\\\"110.4673600\\\",\\\"positionName\\\":\\\"美兰国际机场\\\",\\\"positionType\\\":\\\"pickUp\\\"},{\\\"cityId\\\":\\\"460100\\\",\\\"cityName\\\":\\\"海口\\\",\\\"countryCode\\\":\\\"CN\\\",\\\"countryName\\\":\\\"中国\\\",\\\"latitude\\\":\\\"19.9425070\\\",\\\"longitude\\\":\\\"110.4673600\\\",\\\"positionName\\\":\\\"美兰国际机场\\\",\\\"positionType\\\":\\\"dropOff\\\"}]\",\"bserviceOnDoorType\":3,\"thirdExt\":\"{\\\"returnDeliveryServiceType\\\":\\\"-1\\\",\\\"pickUpCityId\\\":\\\"263\\\",\\\"pickUpDeliveryServiceType\\\":\\\"-1\\\",\\\"returnServiceCircleId\\\":\\\"-1\\\",\\\"pickUpServiceCircleId\\\":\\\"-1\\\",\\\"returnCityId\\\":\\\"263\\\"}\",\"serviceOnDoorType\":3,\"trustRent\":1,\"carLicense\":false}\"";
        // convert thirdExtInfos to ThirdExtInfo
        if (Objects.nonNull(thirdExtInfos)) {
            ThirdExtInfo thirdExtInfo = Json.decodeValue(thirdExtInfos, ThirdExtInfo.class);
            if (Objects.nonNull(thirdExtInfo.getThirdExt())) {
                JsonObject jsonObject = new JsonObject(thirdExtInfo.getThirdExt());
                return;
            }
        }
    }
}
