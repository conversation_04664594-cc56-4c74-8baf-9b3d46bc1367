package com.qinglusaas.connect.domain.model.store;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/7/1, Saturday
 **/
@Data
@Builder
public class SuspendBusinessTime {

    /**
     * 名称
     */
    private String name;

    /**
     * 开始日期
     */
    @NotNull(message = "[开始时间]不能为空")
    private Long startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "[结束时间]不能为空")
    private Long endDate;

    /**
     * 开始时间 00:00
     */
    @NotNull(message = "[开始时间]不能为空")
    private Integer startTime;

    /**
     * 结束时间 00:00
     */
    @NotNull(message = "[结束时间]不能为空")
    private Integer endTime;

}