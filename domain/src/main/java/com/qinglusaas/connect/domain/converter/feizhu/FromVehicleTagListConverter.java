package com.qinglusaas.connect.domain.converter.feizhu;

import com.qinglusaas.connect.infra.constant.VehicleEnum;
import com.qinglusaas.connect.infra.remote.feizhu.constants.FeiZhuVehicleEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FromVehicleTagListConverter extends AbstractConverter<List<String>, List<Integer>> {
    private static final Map<String, FeiZhuVehicleEnum.Tag> TAG_MAPPING_MAP = new HashMap<>() {{
        put(VehicleEnum.TagProp.NEW_SIX_MONTHS.getName(), FeiZhuVehicleEnum.Tag.TAG_39);
        put(VehicleEnum.TagProp.NEW_ONE_YEAR.getName(), FeiZhuVehicleEnum.Tag.TAG_32);
        put(VehicleEnum.TagProp.NEW_TWO_YEAR.getName(), FeiZhuVehicleEnum.Tag.TAG_33);
        put(VehicleEnum.TagProp.NEW_THREE_YEAR.getName(), FeiZhuVehicleEnum.Tag.TAG_34);
        put(VehicleEnum.TagProp.REVERSING_RADAR.getName(), FeiZhuVehicleEnum.Tag.TAG_18);
        put(VehicleEnum.TagProp.REVERSING_CAMERA.getName(), FeiZhuVehicleEnum.Tag.TAG_26);
        put(VehicleEnum.TagProp.CAR_RECORDERS.getName(), FeiZhuVehicleEnum.Tag.TAG_27);
        put(VehicleEnum.TagProp.PHONE_HOLDER.getName(), FeiZhuVehicleEnum.Tag.TAG_14);
        put(VehicleEnum.TagProp.ONE_CAR_WASH.getName(), FeiZhuVehicleEnum.Tag.TAG_30);
        put(VehicleEnum.TagProp.FUEL_GUARANTEE.getName(), FeiZhuVehicleEnum.Tag.TAG_40);
        put(VehicleEnum.TagProp.UPGRADE_VEHICLE_DAMAGE.getName(), FeiZhuVehicleEnum.Tag.TAG_218);
        put(VehicleEnum.TagProp.SMALL_DAMAGE_FREE.getName(), FeiZhuVehicleEnum.Tag.TAG_107);
    }};

    @Override
    protected List<Integer> doForward(List<String> tags) {
        return tags.stream().filter(TAG_MAPPING_MAP::containsKey)
                .map(tag -> TAG_MAPPING_MAP.get(tag).getCode())
                .collect(Collectors.toList());
    }
}
