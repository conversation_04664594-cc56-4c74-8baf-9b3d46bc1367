package com.qinglusaas.connect.client.feizhu.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/2, Thursday
 **/
@AllArgsConstructor
@Getter
public enum BizErrorCode {

    STOCK_OUT("抱歉，车型库存不足，请选择其他⻋型或租车公司", "2002"),
    PRICE_CHANGE("车辆价格已发生变动，请重新查询后再下单", "2001"),
    CREATE_ORDER_FAIL("创建订单失败", "2000"),
    DRIVER_SECURITY_VALIDATE_FAIL("驾驶员未通过租车公司的用户安全验证，建议您更换租车公司或驾驶员重新下单", "2003"),
    DRIVER_EXISTED_ORDER("驾驶员在租车公司已存在租期重叠的未完成订单，建议您更换租车公司重新下单", "2004"),
    UNKNOWN_ADD_CREDIT_SUPPORT_TYPE("不支持的信用支持类型", "3000"),
    QUERY_ORDER_ERROR("查询订单错误", "3001"),
    PARAMETER_ERROR("参数错误", "4000"),
    UNKNOWN("未知错误", "-1");

    private String desc;
    private String code;

}
