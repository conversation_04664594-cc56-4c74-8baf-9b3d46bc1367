package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;
/**
 * 定后加购check请求
 *
 * <AUTHOR>
 */
@Data
public class AddedServiceCheckParam {
    /**
     * 飞猪订单号（租车单单号）
     */
    private Long orderId;
    /**
     * 供应商订单号（租车单号，商家可自行校验相关参数。eg：用户是否重复购买某保险）
     */
    private String vendorOrderId;
    /**
     * 加购的增值服务列表
     */
    private List<AddedService> addedServices;
}
