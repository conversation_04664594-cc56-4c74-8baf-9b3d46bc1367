package com.qinglusaas.connect.client.feizhu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DriverStrategy {

    /**
     * 须知详细描述
     */
    private String desc;
    /**
     * 类型，1-取车凭证，2-押金预授权，3-发票 4-里程限制 5-门店准备时间,6-油费说明,7-限行禁行等。
     */
    private Long type;
}
