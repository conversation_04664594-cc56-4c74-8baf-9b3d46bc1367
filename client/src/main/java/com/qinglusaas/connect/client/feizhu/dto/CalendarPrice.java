package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/1/31 21:49
 */
@Data
public class CalendarPrice {

    /**
     * 每日租期
     */
    private String date;
    /**
     * 每日租金,单位:(元)
     */
    private String dayPrice;
    /**
     * 命中零散小时费规则的时长,单位:分钟
     */
    private Long scatteredMinutes;
    /**
     * 命中零散小时费规则的价格,单位:元
     */
    private String scatteredPrice;
}
