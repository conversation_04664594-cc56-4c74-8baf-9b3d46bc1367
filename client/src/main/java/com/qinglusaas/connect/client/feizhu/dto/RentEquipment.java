package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:56
 */
@Data
public class RentEquipment {

    /**
     * 设备费用，人民币
     */
    private String chargeCNY;
    /**
     * 设备费用，当地货币单位
     */
    private String chargeLocal;
    /**
     * 设备代码
     */
    private String code;
    /**
     * 当地货币单位（国际标准），如USD（美元），JPY（日元）
     */
    private String currencyLocal;
    /**
     * 设备描述
     */
    private String desc;
    /**
     * 设备名称
     */
    private String name;

}
