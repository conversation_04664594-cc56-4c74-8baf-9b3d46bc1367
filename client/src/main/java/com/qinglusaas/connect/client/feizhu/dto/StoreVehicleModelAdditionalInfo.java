package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.client.ctrip.dto.VehicleBasicInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7 11:45
 */
@Data
public class StoreVehicleModelAdditionalInfo {
    /**
     * SaaS门店id
     */
    @JsonProperty("store_code")
    private String storeCode;

    /**
     * SaaS车型
     */
    @JsonProperty("vehicle_model_code")
    private String vehicleModelCode;

    /**
     * 保险信息列表
     */
    @JsonProperty("insurance_info_list")
    private List<InsuranceInfo> insuranceInfoList;

    /**
     * 额外服务信息列表
     */
    @JsonProperty("extra_service_info_list")
    private List<ExtraServiceInfo> extraServiceInfoList;

    @Data
    public static class InsuranceInfo{
        /**
         * 是否生效
         */
        @JsonProperty("is_active")
        private Boolean isActive;

        /**
         * 保险类型，21-基础，22-优享，23-尊享
         */
        @JsonProperty("insurance_type")
        private Long insuranceType;

        /**
         * 保险价格
         */
        @JsonProperty("price")
        private Long price;

        /**
         * 第三方人身或财产随时，整数，单位：万
         */
        @JsonProperty("third_party_liability_amount")
        private Long thirdPartyLiabilityAmount;

        /**
         * 免受折旧金额
         */
        @JsonProperty("depreciation_waiver_amount")
        private Long depreciationWaiverAmount;

        /**
         * 折旧比例，百分比
         */
        @JsonProperty("depreciation_fee_rate")
        private Long depreciationFeeRate;
    }

    @Data
    public static class ExtraServiceInfo {
        /**
         * 是否生效
         */
        @JsonProperty("is_active")
        private Boolean isActive;

        /**
         * 额外服务类型，13-手续费，101-婴儿座椅，102-儿童座椅，103-儿童增高座椅垫，111-防滑链，112-雪地胎
         */
        @JsonProperty("extra_type")
        private Long extraType;

        /**
         * 收费类型，1-收费，0-免费
         */
        @JsonProperty("charge_type")
        private Long chargeType;

        /**
         * 收费金额，只在chargeType为1时生效，单位：元，整数
         */
        @JsonProperty("price")
        private Long price;
    }
}
