package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:09
 */
@Data
public class AddedService {
    /**
     * 增值服务总价
     */
    private String amount;
    /**
     * 是否必选服务，必选服务不能取消
     */
    private Boolean isFixed;
    /**
     * 是否一口价，0-否，1-是。如果是一口价，即下单时候计算金额不会乘以天数。例如手续费等,价格以增值服务单价为准。
     */
    private Long isOneWayFee;
    /**
     * 不包含增值服务说明
     */
    private List<String> listExcloud;
    /**
     * 包含增值服务说明
     */
    private List<String> listInclude;
    /**
     * 增值服务单价
     */
    private String price;
    /**
     * 增值服务数量
     */
    private String quantity;
    /**
     * 增值服务Code
     */
    private String serviceCode;
    /**
     * 增值服务描述
     */
    private String serviceDesc;
    /**
     * 增值服务名称 基本服务费必填
     */
    private String serviceName;
    /**
     * 1-基础服务费(基本险-必传) 2-尊享服务费（不计免赔险）  3-其他保险  4-其他服务费
     */
    private Long serviceType;
    /**
     * 飞猪商家增值服务标准编码 11：车辆租金 12：零散小时费 13：手续费 14：商家优惠，传负值 21：基础服务 22：优享服务 23：尊享服务 24：其他保险服务费 31：送车上门服务费 32：上门还车服务费 33：异地还车费 34：夜间服务费 101：婴儿座椅（0~15个月） 102：儿童座椅（9个月~6岁） 103：儿童增高座垫（6~12岁） 111：防滑链 112：雪地胎 201：一口价活动 OTHER：其他费用
     */
    private String sid;
    /**
     * 保险总价（用户实付）
     */
    private String paymentFee;
}
