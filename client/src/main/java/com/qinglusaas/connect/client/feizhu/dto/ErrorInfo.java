package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:47
 */
@Data
public class ErrorInfo implements IErrorInfo {

    /**
     * 自定义错误码。1001：商家接口 系统异常。2001：商家接口 业务异常。3001：uac内部系统异常。4001：uac业务校验异常。
     */
    private String code;
    /**
     * 自定义错误信息。记录各链路错误详细描述，以“|”分隔，如：商家接口出现系统异常业务错误（uac）|商家具体错误描述。
     */
    private String message;
    /**
     * 子错误码
     */
    private String subCode;
    /**
     * 子错误信息
     */
    private String subMessage;

    /**
     * 错误码, 兼容编辑订单信息接口
     */
    private String errCode;

    /**
     * 错误信息, 兼容编辑订单信息接口
     */
    private String errMessage;
}
