package com.qinglusaas.connect.client.feizhu.vo.response;

import com.qinglusaas.connect.client.feizhu.dto.AddedService;
import com.qinglusaas.connect.client.feizhu.dto.AddedServiceRenew;
import com.qinglusaas.connect.client.feizhu.dto.BasicPriceInfo;
import com.qinglusaas.connect.client.feizhu.dto.CancelStrategyStruct;
import lombok.Data;

import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/1/31 21:43
 */
@Data
public class AlitripRentcarRenewalpricesQueryResponse {
    /**
     * 增值服务列表
     */
    private List<AddedServiceRenew> addedServiceList;

    /**
     * 取消政策（国内租车专用）
     */
    private CancelStrategyStruct cancelStrategyStruct;

    /**
     * 车辆租金信息（国内租车专用）
     */
    private BasicPriceInfo priceInfo;

    /**
     * 错误码
     */
    private String retCode;

    /**
     * 错误原因
     */
    private String retMessage;

    /**
     * 调用成功
     */
    private Boolean success;
}
