package com.qinglusaas.connect.client.ctrip.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qinglusaas.connect.client.ctrip.dto.AddressDTO;
import com.qinglusaas.connect.client.ctrip.utility.IStorePair;
import com.qinglusaas.connect.client.ctrip.vo.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 价格明细.
 *
 * @date 2022/9/3 21:29
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetPriceDetailReq extends BaseRequest implements IStorePair {

    /**
     * 供应商车型编码。
     * 必传 是
     */
    @NotBlank
    private String vehicleCode;

    /**
     * 取车时间，格式yyyy-MM-dd HH:mm。如2017-09-05 12:00。
     * 必传 是
     */
    @NotBlank
    private String pickupDate;

    /**
     * 还车时间，格式yyyy-MM-dd HH:mm。如2017-09-07 12:00。
     * 必传 是
     */
    @NotBlank
    private String returnDate;

    /**
     * 取车门店的供应商门店code。
     * 必传 是
     */
    @NotBlank
    private String pickupStoreCode;

    /**
     * 还车门店的供应商门店code。
     * 必传 是
     */
    @NotBlank
    private String returnStoreCode;

    /**
     * 取车城市的供应商城市code。
     * 必传 否
     */
    private String pickupCityCode;

    /**
     * 还车城市的供应商城市code。
     * 必传 否
     */
    private String returnCityCode;

    /**
     * 供应商优惠code。
     * 必传 否
     */
    private String couponCode;

    /**
     * 是否送车上门。对于对接了多种取还车方式的供应商，isPickupOndoor的值与getStores接口返回的取车方式一致（字段：isSendForPickUpCar）；如果没有对接多种取还车方式，此字段为false。如果供应商对接了新的服务圈功能，此字段可以废弃。
     * 必传 是
     */
//    @NotNull
    private Boolean isPickupOndoor;

    /**
     * 送车上门地址信息。当isPickupOndoor为true时，此字段才有效。
     * 必传 否
     */
    private AddressDTO pickupOndoorAddr;

    /**
     * 是否上门取车。对于对接了多种取还车方式的供应商，isPickoffOndoor的值与getStores接口返回的取车方式一致（字段：isSendForPickOffCar）；如果没有对接多种取还车方式，此字段为false。如果供应商对接了新的服务圈功能，此字段可以废弃。
     * 必传 是
     */
    private Boolean isPickoffOndoor;

    /**
     * 上门取车地址信息。当isPickoffOndoor为true时，此字段才有效。
     * 必传 否
     */
    private AddressDTO pickoffOndoorAddr;

    /**
     * 价格一致码，与供应商从列表页接口返回的价格一致码对应。如果供应商没有对接价格码，此字段的值为空。
     * 必传 否
     */
    private String priceToken;

    /**
     * 套餐代码，与供应商从列表页接口返回的车型的packageId对应。用于对接无忧租场景，非必传。如果为空那么供应商应返回普通租的价格数据。
     * 必传 否
     */
    private String packageId;

    /**
     * 取车门店的服务圈Id list。只有对接了新的服务圈功能才会启用该字段。-1：客人到店取车。如果是其他取车方式则传对应的圈id。
     * 必传 否
     */
    private List<Integer> pickUpStoreServiceCircleIdList;

    /**
     * 还车门店的服务圈Id list。只有对接了新的服务圈功能才会启用该字段。-1：客人到店还车。如果是其他还车方式则传对应的圈id。
     * 必传 否
     */
    private List<Integer> returnStoreServiceCircleIdList;

    /**
     * 价格渠道。10-携程渠道; 40-非携程渠道
     * 必传 否
     */
    private Integer priceChannel;

    /**
     * 供应商价格类型，仅部分供应商需要传递。1-日租价；2-套餐价。
     * 必传 否
     */
    private Integer vendorPriceType;
}
