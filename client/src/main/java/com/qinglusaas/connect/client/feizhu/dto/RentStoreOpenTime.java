package com.qinglusaas.connect.client.feizhu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/12/30 16:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RentStoreOpenTime {

    /**
     * 星期：1~7，详见jdk中Calendar类定义。1-星期天，2-星期一，3-星期二。。。6-星期六
     */
    private Long day;
    /**
     * 当天营业时间，建议格式：hh:mm-hh:mm。如果商家数据实在无法解析成建议格式，则展示商家自定义文本
     */
    private String openTime;
}
