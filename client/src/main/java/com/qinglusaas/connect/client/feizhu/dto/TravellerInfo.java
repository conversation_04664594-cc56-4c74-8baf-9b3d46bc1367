package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/16, Thursday
 **/
@Data
public class TravellerInfo {

    /**
     * 驾驶人信息
     */
    private String name;
    /**
     * 姓名
     */
    private String phone;
    /**
     * 出生日期，格式yyyy-mm-dd
     */
    private String birthday;
    /**
     * 证件类型。0:身份证 1:护照 2:学生证 3:军官证 4:回乡证 5:台胞证 6:港澳通行证 10:警官证 11:士兵证 12:台湾通行证
     */
    private Integer credentialType;
    /**
     * 证件号码
     */
    private String credentialNo;
    private String extra;

}
