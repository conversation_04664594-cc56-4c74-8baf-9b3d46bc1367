package com.qinglusaas.connect.client.feizhu.vo.response;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
public class StoreBaseInfoQueryResponse extends FeiZhuBaseResponse{
    private SearchResponse searchResp;

    @Data
    @AllArgsConstructor
    public static class SearchResponse{
        List<StoreDetailInfo> storeInfoList;
    }

    @Data
    public static class StoreDetailInfo {
        /**
         * 租赁间隔时间，单位小时，整数
         */
        private Integer rentIntervalTime;

        /**
         * 门店类型, 1:门店 2:提车点
         */
        private Integer storeType;

        /**
         * 门店对应的公司Code(针对自营商家不读取此字段)
         */
        private String companyCode;

        /**
         * 供应商父门店code, 仅当storeType不为1时有效
         */
        private String parentStoreCode;

        /**
         * 门店地址
         */
        private String address;

        /**
         * 门店纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String latitude;

        /**
         * 门店是否有效，true为有效，false表示无效，商家确保将所有门店数据同步给平台（有效，无效）
         */
        private Boolean isActive;

        /**
         * 门店所在市的供应商cityCode
         */
        private String cityCode;

        /**
         * 最大租赁时间，单位小时，整数
         */
        private Integer maxLeaseTime;

        /**
         * 最小租赁时间，单位小时，整数
         */
        private Integer minLeaseTime;

        /**
         * 门店营业时间，不可为空
         */
        private List<StoreOpenTime> storeOpenTimeList;

        /**
         * 门店联系电话，11位整数
         */
        private String phone;

        /**
         * 供应商门店Code,保证唯一
         */
        private String storeCode;

        /**
         * 门店联系人
         */
        private String contact;

        /**
         * 门店最短提前预定时间(单位:小时,可传小数如：0.5)
         */
        private String advanceBookingHour;

        /**
         * 门店名称
         */
        private String storeName;

        /**
         * 门店非营业时间,如果为空则表示无非营业时间
         */
        private List<StoreCloseTime> storeCloseTimeList;

        /**
         * 门店经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String longitude;

        /**
         * 异地还车配置
         */
        private List<DiffStoreInfo> diffStoreInfoList;


        // 门店营业时间
        @Data
        public static class StoreOpenTime {
            /**
             * 营业时间段总价格 精确到2位小数;单位:元
             */
            private String chargeFee;

            /**
             * 	1,2,3
             * 	表示在每周的星期几生效,周一到周日依次为1...7，多个使用逗号分隔
             */
            private String effectWeek;

            /**
             * 收费方式  1:收费 2:免费
             */
            private Integer chargeType;

            /**
             * 开始时间,格式为HH:mm
             */
            private String startTime;

            /**
             * 结束时间,格式为HH:mm
             */
            private String endTime;
        }

        // 门店非营业时间
        @Data
        public static class StoreCloseTime {
            /**
             * 停止营业时间开始时间,格式为yyyy-MM-dd HH:mm:ss
             */
            private String startTime;

            /**
             * 停止营业时间结束时间,格式为yyyy-MM-dd HH:mm:ss
             */
            private String endTime;
        }

        // 异地还车配置
        @Data
        public static class DiffStoreInfo {
            /**
             * 异地还车门店Code(当前门店可还车到的异地门店Code)
             */
            private String diffStoreCode;

            /**
             * 车辆归属类型，1-归属到当前门店，2-归属到异地门店
             */
            private Integer diffStoreOwnerType;

            /**
             * 是否激活，true激活，false不激活
             */
            private Boolean isActive;

            /**
             * 异地还车后的租赁间隔，单位小时，整数
             */
            private Integer diffStoreIntervalTime;
        }
    }
}
