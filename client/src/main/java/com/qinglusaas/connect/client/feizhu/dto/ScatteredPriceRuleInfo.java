package com.qinglusaas.connect.client.feizhu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/1/31 21:50
 */
@Data
public class ScatteredPriceRuleInfo {

    /**
     * 零散小时费规则列表
     */
    private List<Data> data = new ArrayList<>();

    /**
     * 零散小时费规则列表
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */

    @NoArgsConstructor
    @AllArgsConstructor
    public static class Data {

        /**
         * 结束分钟
         */
        private Long endTime;
        /**
         * 开始分钟
         */
        private Long startTime;
        /**
         * 收费百分比，20 -> 20%
         */
        private String value;


        public Long getEndTime() {
            return this.endTime;
        }

        public void setEndTime(Long endTime) {
            this.endTime = endTime;
        }

        public Long getStartTime() {
            return this.startTime;
        }

        public void setStartTime(Long startTime) {
            this.startTime = startTime;
        }

        public String getValue() {
            return this.value;
        }

        public void setValue(String value) {
            this.value = value;
        }

    }
}
