package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * 取消定后加购请求
 *
 * <AUTHOR>
 */
@Data
public class AddedServiceCancelParam {
    /**
     * 飞猪主单订单号（租车单订单号）
     */
    private Long originOrderId;
    /**
     * 供应商订单号（提单商家返回的订单号）
     */
    private String vendorOrderId;
    /**
     * 增值服务加购列表
     */
    private List<AddedService> addedServices;
    /**
     * 飞猪增值服务订单号（本次加购单订单号）
     */
    private Long orderId;
    /**
     * 订单退款原因
     */
    private String cancelReason;
    /**
     * 实际取消时间
     */
    private String cancelTime;
}
