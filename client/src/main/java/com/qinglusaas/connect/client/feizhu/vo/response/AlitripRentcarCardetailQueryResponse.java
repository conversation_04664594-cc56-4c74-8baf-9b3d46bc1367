package com.qinglusaas.connect.client.feizhu.vo.response;

import com.qinglusaas.connect.client.feizhu.dto.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 11:32
 */
@Data
public class AlitripRentcarCardetailQueryResponse extends FeiZhuBaseResponse {

    /**
     * 增值服务（国内租车专用）
     */
    private List<AddedService> addedServiceList;

    /**
     * 取消政策（国内租车专用）
     */
    private CancelStrategyStruct cancelStrategyStruct;

    /**
     * 租车须知（国内租车专用）
     */
    private List<DriverStrategy> driverStrategyList;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * （国内租车专用）支付方式，1-仅支持在线付，2-仅支持门店付，3-均支持
     */
    private Long payMode;

    /**
     * 取车材料
     */
    private PickCarmaterials pickCarmaterials;

    /**
     * 车辆租金信息（国内租车专用）
     */
    private PriceInfo priceInfo;

    /**
     * 车型信息
     */
    private RentCar rentCar;

    /**
     * 车型报价列表。每个报价中会有车型id，品牌id，门店id。具体的车型、品牌、门店请从下列列表中通过id查询（国际租车专用）
     */
    private RentCarPrice rentCarPrice;

    /**
     * 更多须知 国内租车专用）
     */
    private List<RentCarTips> rentCarTipsList;

    /**
     * 门店信息
     */
    private List<RentStore> rentStores;

    /**
     * 供应商（品牌商）信息
     */
    private RentSupplier rentSupplier;

    /**
     * 保险结构化信息，内层费项部分需与addedServiceList对应。eg：serviceCode需对应，若没对应则影响算价
     */
    private InsuranceStructInfo insuranceStructInfo;
}
