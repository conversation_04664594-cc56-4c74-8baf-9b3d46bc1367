package com.qinglusaas.connect.client.feizhu.vo.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 飞猪车辆信息
 * <AUTHOR>
 */
@Data
public class StoreVehicleSearchRspInfo {

    @Data
    public static class StoreVehicleQueryResponse extends FeiZhuBaseResponse {

        /**
         *
         */
        private Map<String, List<StoreVehicleSearchRspInfo>> searchResp;
    }

    /**
     * 必传，供应商车型组Code
     */
    private String vehicleModelCode;

    /**
     * 必传，车辆当前里程数，整数，单位km
     */
    private Integer currentMileage;

    /**
     * 必填 校验合格证
     */
    private InspectionCertificate inspectionCertificate;

    /**
     * 必填 行驶证信息
     */
    private VehicleRegistrationCertificate vehicleRegistrationCertificate;

    /**
     * 是否支持异地还车
     */
    private Boolean diffStore;

    /**
     * 供应商门店code
     */
    private String storeCode;

    /**
     * 必填 保险信息
     */
    private List<InsuranceInfo> insuranceInfoList;

    /**
     * 必填 供应商车辆code
     */
    private String vehicleCode;

    /**
     * 必填，车辆状态 1-上架，2-下架
     */
    private Integer status;



    /**
     * 校验合格证
     */
    @Data
    public static class InspectionCertificate {

        /**
         * 年检到期日期，格式yyyy-MM-dd
         * 必填
         */
        private String inspectionExpirationDate;
    }

    /**
     * 行驶证信息
     */
    @Data
    public static class VehicleRegistrationCertificate {

        private String registrationNumber;

        private String engineNumber;

        private String registrationDate;

        /**
         * 各地级市字母代码
         */
        private String ascriptionCity;

        /**
         * 车架号
         */
        private String vin;

        /**
         * 车牌号省的简称
         */
        private String ascriptionCountry;
    }

    @Data
    public static class InsuranceInfo {

        /**
         * 保险单号
         */
        private String insuranceNumber;

        /**
         * 保险到期日期，格式yyyy-MM-dd
         */
        private String insuranceExpirationDate;

        /**
         * 保险类型 1-商业保险单，2-强险保险单
         */
        private Integer insuranceType;

        /**
         * 投保使用的证件号
         */
        private String insuranceIdentityCard;
    }


}

