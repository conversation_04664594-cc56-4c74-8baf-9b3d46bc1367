package com.qinglusaas.connect.client.ctrip.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/7/25, Tuesday
 **/
@Data
public class ReletOrderInfoStandardFeeListDTO {

    /**
     * 费用的数量，只有儿童座椅使用
     */
    private double count;
    /**
     * 费用金额总价
     */
    private BigDecimal feeAmount;
    /**
     * 费用CODE ，供应商CODE转换
     */
    private String feeCode;
    /**
     * 费用是否固定。false 为可选费用
     */
    private Boolean fixed;
    /**
     * 支付状态 0：未支付 1：已支付
     */
    private Integer payStatus;
    /**
     * 费用的数量
     */
    private double quantity;
    /**
     * 费用单价
     */
    private BigDecimal unitAmount;

}
