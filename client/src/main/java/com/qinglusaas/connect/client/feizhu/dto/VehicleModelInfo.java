package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/14 13:37
 */
@Data
public class VehicleModelInfo {
    /**
     * 车型id
     */
    @JsonProperty("vehicle_model_code")
    private String vehicleModelCode;

    /**
     * 里程限制
     */
    @JsonProperty("mileage_limit_info")
    private MileageLimitInfo mileageLimitInfo;

    /**
     * 信用免押
     */
    @JsonProperty("credit_deposit_info")
    private CreditDepositInfo creditDepositInfo;

    /**
     * 删除
     */
    @JsonProperty("deleted")
    private Integer deleted;

    /**
     * 公司代码
     */
    @JsonProperty("company_code")
    private String companyCode;

    /**
     * 门店id
     */
    @JsonProperty("store_code")
    private String storeCode;

    @Data
    public static class MileageLimitInfo {
        /**
         * 是否存在里程限制
         */
        @JsonProperty("exist_mileage_limit")
        private Boolean existMileageLimit;

        /**
         * 限制里程数，单位：km/天
         */
        @JsonProperty("mileage_limit")
        private Long mileageLimit;

        /**
         * 超出里程限制收费标准，单位：元/km
         */
        @JsonProperty("over_mileage_fee")
        private BigDecimal overMileageFee;
    }

    @Data
    public static class CreditDepositInfo {
        /**
         * 是否支持信用免押
         */
        @JsonProperty("support_credit")
        private Boolean supportCredit;

        /**
         * 信用免押车损免押金额
         */
        @JsonProperty("damage_amount")
        private BigDecimal damageAmount;

        /**
         * 信用免押违章免押金额
         */
        @JsonProperty("illegal_amount")
        private BigDecimal illegalAmount;
    }


}
