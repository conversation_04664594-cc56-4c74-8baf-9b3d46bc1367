package com.qinglusaas.connect.client.feizhu.vo.response;

import com.qinglusaas.connect.client.feizhu.dto.RentCar;
import com.qinglusaas.connect.client.feizhu.dto.RentCarPrice;
import com.qinglusaas.connect.client.feizhu.dto.RentStore;
import com.qinglusaas.connect.client.feizhu.dto.RentSupplier;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/2 13:37
 */
@Data
public class AlitripRentcarPricesQueryResponse extends FeiZhuBaseResponse {

    /**
     * 车型报价列表。每个报价中会有车型id，品牌id，门店id。具体的车型、品牌、门店请从下列列表中通过id查询
     */
    private List<RentCarPrice> rentCarPrices;

    /**
     * 车型信息
     */
    private List<RentCar> rentCars;

    /**
     * 门店信息
     */
    private List<RentStore> rentStores;

    /**
     * 供应商（品牌商）信息
     */
    private List<RentSupplier> rentSuppliers;

}
