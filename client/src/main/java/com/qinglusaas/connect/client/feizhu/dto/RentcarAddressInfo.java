package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/11/20 14:57
 */
@Data
public class RentcarAddressInfo {
    /**
     * 位置机场代码
     */
    private String airportCode;
    /**
     * 城市名称
     */
    private String city;
    /**
     * 城市，英文
     */
    private String cityEn;
    /**
     * 城市Code
     */
    private String cityId;
    /**
     * 所在州id，如亚洲：xxx，欧洲：yyy
     */
    private String continentId;
    /**
     * 国家代码
     */
    private String countryCode;
    /**
     * 国家，英文
     */
    private String countryEn;
    /**
     * 是否送车上门
     */
    private Boolean isServiceOnDoor;
    /**
     * 取车位置经纬度。格式：经度,维度
     */
    private String location;
    /**
     * 取车位置名称
     */
    private String name;
    /**
     * 城市所属州代码
     */
    private String stateCode;
    /**
     * 取车门店编码Code
     */
    private String storeCode;
}
