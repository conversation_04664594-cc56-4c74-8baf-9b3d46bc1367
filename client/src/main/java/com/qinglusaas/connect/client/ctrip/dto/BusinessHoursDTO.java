package com.qinglusaas.connect.client.ctrip.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/9/29, Thursday
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BusinessHoursDTO {

    /**
     * 门店常规营业时间信息
     * 是否必填：是
     */
    @NotNull(message = "normalBusinessHoursList must be not null")
    private List<NormalBusinessHoursDTO> normalBusinessHoursList;

    /**
     * 门店暂停营业时间信息
     */
    private List<SpecialBusinessHoursDTO> suspendBusinessHoursList;

}
