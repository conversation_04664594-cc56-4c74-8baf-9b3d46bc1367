package com.qinglusaas.connect.client.ctrip.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/9/12, Tuesday
 **/
@Data
public class CollectVendorVehicleByStoreMappingDTO {

    /**
     * 服务商code
     */
    private String vendorCode;
    /**
     * 服务商门店 code
     */
    private String vendorStoreCode;
    /**
     * 服务商车型 code
     */
    private String vendorVehicleCode;
    /**
     * 携程车型ID
     */
    private Long standardProductId;
    /**
     * 牌照类型 车牌(0：普通 1：京牌 2：沪；3：粤A 4：粤B  5：浙A 6：津)
     */
    private Integer plateType;
    /**
     * 驱动类型 1.混卖；2.四驱；3.两驱； 单选，不传时，默认为1
     */
    private Integer driveType;
    /**
     * 年款集合
     */
    private List<Integer> years;
    /**
     * 排量集合
     */
    private List<String> displacements;
    /**
     * 天窗  1.混卖；2.有天窗；3.无天窗； 单选，不传时，默认为1
     */
    private Integer sunroof;
    /**
     * 是否带快充 1.混卖；2.带快充；3.不带快充； 单选不传时，默认为1，非电车不要传该字段
     */
    private Integer fastCharge;
    /**
     * 雪地胎 1.混卖；2.有雪地胎；3.无雪地胎；单选，不传时，默认为1
     */
    private Integer snowTyre;
    /**
     * 自助取还 1.混卖；2.支持自助取还；3.不支持自助取还； 单选，不传时，默认为1
     */
    private Integer selfService;


}
