package com.qinglusaas.connect.client.ctrip.vo.response;

import com.qinglusaas.connect.client.ctrip.dto.CtripDiffStoreV2DTO;
import com.qinglusaas.connect.client.ctrip.dto.CtripOrderIntervalV2DTO;
import com.qinglusaas.connect.client.ctrip.vo.ResultVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class CollectDiffRuleFromVendorResp extends ResultVO {

    private List<CtripDiffStoreV2DTO> diffStoreRuleList;

    private Long totalCount;

}
