package com.qinglusaas.connect.client.feizhu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelRule {

    /**
     * 收取的取消费用，纯数字，具体单位详见cancelType
     */
    private Long num;
    /**
     * 取车时间前time（单位：小时） 取消用车，收取num费用
     */
    private Long time;
}
