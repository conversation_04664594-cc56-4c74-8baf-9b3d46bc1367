package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:34
 */
@Data
public class CancelStrategy {

    /**
     * 文本描述 取消政策
     */
    private String desc;
    /**
     * 结构化 取消政策：提前xx小时取消免费
     */
    private Long hour;

    /**
     * 取消类型（必填）101：免费取消-取车时间前可免费取消。 102：限时免费取消-取车前X小时可免费取消；取车前X小时-取车时间取消扣订单总金额X%；取车时间后取消扣订单全额。 103：有损取消-该订单付款后取消，需扣订单总金额X%。
     */
    private Long type;
}
