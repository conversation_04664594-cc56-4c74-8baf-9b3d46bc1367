package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/7, Tuesday
 **/
@Data
public class ThirdExtInfo {

    @JsonProperty("addressInfos")
    private String addressInfos;
    @JsonProperty("alipayTradeNo")
    private String alipayTradeNo;
    @JsonProperty("bserviceOnDoorType")
    private Integer bserviceOnDoorType;
    @JsonProperty("serviceOnDoorType")
    private Integer serviceOnDoorType;
    @JsonProperty("trustRent")
    private Integer trustRent;
    @JsonProperty("thirdExt")
    private String thirdExt;

}
