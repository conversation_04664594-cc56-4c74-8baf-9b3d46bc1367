package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:42
 */
@Data
public class RentCarPriceCombo {
    /**
     * 结构化取消政策
     */
    private CancelStrategyStruct cancelStrategyStruct;
    /**
     * 碰撞盗抢险起赔额，当地货币单位（以上一级中currencyLocal为准）
     */
    private String excessAmount;
    /**
     * 额外驾驶人政策
     */
    private ExtraDriverStrategy extraDriverStrategy;
    /**
     * 费用不含列表
     */
    private List<RentPriceFeeItem> feeExcludes;
    /**
     * 费用包含列表
     */
    private List<RentPriceFeeItem> feeIncludes;
    /**
     * 燃油政策，0-满油取还，1-买一箱油，2-送一箱油。具体见FuelPolicyType枚举
     */
    private Long fuleStrategy;
    /**
     * 该套餐是否需要出保，需要出保的情况下 下单时需要用户填写出保必填信息，如身份证
     */
    private Boolean insuranceInfoRequire;
    /**
     * 套餐里程限制
     */
    private MileLimit mileLimit;
    /**
     * 套餐描述
     */
    private String packageDesc;
    /**
     * 套餐名称
     */
    private String packageName;
    /**
     * 套餐名称扩展描述，如：不含油、不含GPS
     */
    private String packageNameExt;
    /**
     * 套餐提供的价格list：在线付价格，到店付价格，混合支付方式价格
     */
    private List<RentCarPriceItem> priceItems;
}
