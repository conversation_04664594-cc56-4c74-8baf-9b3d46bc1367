package com.qinglusaas.connect.client.feizhu.dto;

import java.util.List;

import lombok.Data;

/**
 * 国内租车价格信息
 */
@Data
public class RentCarPriceDomestic {
    /**
     * 商家车型id
     */
    private String carId;

    /**
     * 飞猪B端标准车型id，从飞猪侧获取
     */
    private String stdVehModId;

    /**
     * 品牌商id，此处如果是非平台型商家，则需传渠道号，如果是平台型商家，则需要传子商家Id
     */
    private String supplierId;

    /**
     * 品牌商名称
     */
    private String supplierName;

    /**
     * 取车门店id
     */
    private String pickUpStoreId;

    /**
     * 还车门店id
     */
    private String returnStoreId;

    /**
     * 车辆押金，单位元
     */
    private String carPreDeposit;

    /**
     * （国内租车专用）商家价格标识码，车辆详情接口与订单提交接口会再次传递给商家
     */
    private String outerPriceId;

    /**
     * 违章押金，单位元（如商家传递为空，默认2000元）
     */
    private String legalPreDeposit;

    /**
     * 异地还车费，人民币，单位元
     */
    private String oneWayFeeRMB;

    /**
     * 异地还车费，支付方式：1-在线支付，2-到店支付，预留
     */
    private String oneWayFeePaymode;

    /**
     * 车辆基本租金，人民币，单位元，必填
     */
    private String basicRentFee;

    /**
     * 包含手续费、基本保障服务等的总费用，人民币，单位元，必填
     */
    private String totalRentFee;

    /**
     * 手续费(总费用),单位元
     */
    private String poundageFee;

    /**
     * 总基础服务费,单位元
     */
    private String basicServiceFee;

    /**
     * 总租金金额,单位元
     */
    private String rentFee;

    /**
     * 零散小时费总金额,单位元 eg:38.09
     */
    private String hourFee;

    /**
     * 上门送取车费用总金额（收费上门送取车时必传）,单位元
     */
    private String onDoorFee;

    /**
     * 上门取车费
     */
    private String pickUpOnDoorFee;

    /**
     * 国内租车专用！支持carId+pickUpStoreId维度的标签，标签之间用英文逗号分隔,约束：只允许传以下值tag的数字： 新版标签： TAG_3(3,"信用双免"),TTAG_14(14,"配手机支架"),TAG_17(17,"24小时营业"),TAG_18(18,"倒车雷达"),TAG_20(20,"配纸巾"),TAG_21(21,"配饮用水"),TAG_22(22,"配车充"),TAG_25(25,"儿童座椅"),TAG_26(26,"倒车影像"),TAG_27(27,"配行车记录仪"),TAG_30(30,"一车一洗"),TAG_32(32,"一年内新车"),TAG_33(33,"两年内新车"),TAG_34(34,"三年内新车"),TAG_39(39,"半年内新车"),TAG_40(40,"油量保障"),TAG_41(41,"不限里程"),TAG_105(105,"一口价"),TAG_106(106,"送车损无忧")如有需求新增请联系业务运营。 注意：以下标签将在新版废弃，请注意按照新版标签返回： TAG_1(1,"信用免押·免车辆押金"),TAG_2(2,"信用免押·免违章押金")//如果有3，就必须将1或2去掉，3表示全免；TAG_4(4,"送车上门"),TAG_5(5,"上门取车"),TAG_6(6,"上门取送"),TAG_7(7,"快速取还"),TAG_8(8,"车况佳"),TAG_9(9,"满油取还"),TAG_10(10,"3万公里内新车"),TAG_11(11,"3公里内免费接送"),TAG_12(12,"支持异点还车"),TAG_13(13,"新款车"),TAG_15(15,"老款车"),TAG_16(16,"免费接至门店取车"),TAG_19(19,"服务专业"),TAG_23(23,"取还便捷"),TAG_24(24,"快速取"),TAG_28(28,"5公里内免费接送"),TAG_29(29,"无需等待"),TAG_31(31,"免费上门取送车"),TAG_35(35,"实时结算"),TAG_36(36,"能源免费"),TAG_37(37,"纯电动"),TAG_38(38,"旅行车"),TAG_43(43,"雪地胎")，TAG_58(58,"支持安心租一口价"),TAG_218(218,"可升级车损免赔")
     */
    private String tags;

    /**
     * 可选，临时扩展信息字段，以KV对形式存储，key与value之间使用%3A%3B分隔，多个KV间使用%3B%3A分隔，格式：key1%3A%3Bvalue1%3B%3Akey2%3A%3Bvalue2%3B%3A。目前支持的key有：sellerId（卖家id），sellerNick（卖家昵称），wangwang（卖家旺旺），telInland（卖家国内服务电话），telOutland（卖家境外服务电话），rule（退改规则），service（特色服务），serviceRange（服务范围, 1国内, 2国外, 0 两者）
     */
    private String extInfos;

    /**
     * 库存，（国内租车专用,若无库存请传空）
     */
    private Long stockNum;

    /**
     * 取消类型（必填）101：免费取消-取车时间前可免费取消。 * 102：限时免费取消-取车前X小时可免费取消；取车前X小时-取车时间取消扣订单总金额X%；取车时间后取消扣订单全额。 * 103：有损取消-该订单付款后取消，需扣订单总金额X%。
     */
    private Long cancelType;

    /**
     * 第三方扩展信息
     */
    private String thirdExtInfos;

    /**
     * 取车类型 1:免费上门送取车 2:收费上门送取车 3: 自行前往门店取还车 4:店员免费接您至门店取还车
     */
    private String pickUpCarType;

    /**
     * 取车类型 1:免费上门送取车 2:收费上门送取车 3: 自行前往门店取还车 4:店员免费接您至门店取还车
     */
    private String returnCarType;

    /**
     * 上门还车费
     */
    private String returnOnDoorFee;

    /**
     * 尊享服务费(不参与总价计算)，单位元
     */
    private String honerServiceFee;

    /**
     * 增值服务(仅次卡业务对接使用)
     */
    private List<AddedService> addedServiceList;

    /**
     * 牌照描述
     */
    private String carLicenseDesc;

    /**
     * 牌照类型，0-不限，1-本地牌，2-外地牌
     */
    private String carLicense;


} 