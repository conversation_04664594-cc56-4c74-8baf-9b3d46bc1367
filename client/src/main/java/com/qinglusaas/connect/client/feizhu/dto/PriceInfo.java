package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:23
 */
@Data
public class PriceInfo {
    /**
     * 取车类型 1-免费上门还车 2-收费上门还车 3-自行前往门店取车 4-店员免费接您至门店取车
     */
    private String pickUpCarType;
    /**
     * 还车类型 1-免费上门还车 2-收费上门还车 3-自行前往门店还车 4-门店还车后店员免费送您至取车点
     */
    private String returnCarType;
    /**
     * 基础服务费,单位元
     */
    private String basicServiceFee;
    /**
     * 车辆押金，单位元。只能整数
     */
    private String carPreDeposit;
    /**
     * 违章押金解押超时时间,单位天
     */
    private Long legalDepositTimeOut;
    /**
     * 违章押金，单位元 。只能整数
     */
    private String legalPreDeposit;
    /**
     * 手续费,单位元
     */
    private String poundageFee;
    /**
     * 商家根据取还车时间计算租期，如 2.0、2.5 等
     */
    private String quantity;
    /**
     * 总租金金额，单位元
     */
    private String rentFee;
    /**
     * 标准价车辆租金总价（单价*租期），下单时以此总数值为准，单价*租期仅用于展示
     */
    private String standardPrice;
    /**
     * 标准价车辆租金单价，单位元。只能整数
     */
    private String standardUnitPrice;
    /**
     * 第三方扩展信息 透传
     */
    private String thirdExtInfos;
}
