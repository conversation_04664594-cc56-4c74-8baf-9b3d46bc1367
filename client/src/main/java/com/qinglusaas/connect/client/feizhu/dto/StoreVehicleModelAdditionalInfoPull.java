package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/16 11:45
 */
@Data
public class StoreVehicleModelAdditionalInfoPull {
    /**
     * SaaS门店id
     */
    private String storeCode;

    /**
     * SaaS车型
     */
    private String vehicleModelCode;

    /**
     * 保险信息列表
     */
    private List<InsuranceInfo> insuranceInfoList;

    /**
     * 额外服务信息列表
     */
    private List<ExtraServiceInfo> extraServiceInfoList;

    @Data
    public static class InsuranceInfo{
        /**
         * 是否生效
         */
        private Boolean isActive;

        /**
         * 保险类型，21-基础，22-优享，23-尊享
         */
        private Long insuranceType;

        /**
         * 保险价格
         */
        private Long price;

        /**
         * 第三方人身或财产随时，整数，单位：万
         */
        private Long thirdPartyLiabilityAmount;

        /**
         * 免受折旧金额
         */
        private Long depreciationWaiverAmount;

        /**
         * 折旧比例，百分比
         */
        private Long depreciationFeeRate;
    }

    @Data
    public static class ExtraServiceInfo {
        /**
         * 是否生效
         */
        private Boolean isActive;

        /**
         * 额外服务类型，13-手续费，101-婴儿座椅，102-儿童座椅，103-儿童增高座椅垫，111-防滑链，112-雪地胎
         */
        private Long extraType;

        /**
         * 收费类型，1-收费，0-免费
         */
        private Long chargeType;

        /**
         * 收费金额，只在chargeType为1时生效，单位：元，整数
         */
        private Long price;
    }
}
