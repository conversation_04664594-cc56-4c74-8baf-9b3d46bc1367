package com.qinglusaas.connect.client.ctrip.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/7/25, Tuesday
 **/
@Data
public class RequestPriceDailyListDTO {

    /**
     * 日期
     */
    private String date;
    /**
     * 零散小时费
     */
    private BigDecimal partDailyPrice;
    /**
     * 整日 OR 零散比例 小于等于 1
     */
    private BigDecimal quantity;
    /**
     * 零散比例，回传使用
     */
    private BigDecimal rate;
    /**
     * 整日价格
     */
    private BigDecimal wholeDailyPrice;

}
