package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/1/31 21:48
 */
@Data
public class BasicPriceInfo {

    /**
     * 基础服务费,单位元
     */
    private String basicServiceFee;
    /**
     * 日租金详情列表
     */
    private List<CalendarPrice> calendarPrice;
    /**
     * 手续费,单位元
     */
    private String poundageFee;
    /**
     * 商家价格标
     */
    private String priceMark;
    /**
     * 商家根据取还车时间计算租期，如 2.0、2.5 等
     */
    private String quantity;
    /**
     * 零散小时费规则
     */
    private ScatteredPriceRuleInfo scatteredPriceRuleInfo;
    /**
     * 车辆租金总价（单价*租期），下单时以此总数值为准，单价*租期仅用于展示
     */
    private String standardPrice;
    /**
     * 车辆租金单价，单位元。只能整数
     */
    private String standardUnitPrice;

}
