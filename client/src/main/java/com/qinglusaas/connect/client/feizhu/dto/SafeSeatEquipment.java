package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 16:03
 */
@Data
public class SafeSeatEquipment {

    /**
     * 车行品牌英文名称
     */
    private String brandNameEn;
    /**
     * 儿童座椅设备代码
     */
    private String code;
    /**
     * 国家简码
     */
    private String countryCode;
    /**
     * 当地货币单位（国际标准），如USD（美元），JPY（日元）
     */
    private String currencyLocal;
    /**
     * 全程最多花费
     */
    private String maxCost;
    /**
     * 租赁价格，元
     */
    private String priceCNY;
    /**
     * 租赁价格，当地货币单位
     */
    private String priceLocal;
    /**
     * 座椅描述
     */
    private String seatDesc;
    /**
     * 安全座椅类型（1-婴儿座椅，2-儿童座椅，3-儿童增高垫）
     */
    private Long seatType;
    /**
     * 租赁周期单位：1-天，2-周
     */
    private Long unitCycle;
}
