package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:57
 */
@Data
public class RentStoreBase {

    /**
     * 详细地址
     */
    private String address;
    /**
     * 机场代码
     */
    private String airportCode;
    /**
     * 机场到门店交通情况描述
     */
    private String airportTraffic;
    /**
     * 门店所在城市Code
     */
    private String cityCode;
    /**
     * 所在城市名称
     */
    private String cityName;
    /**
     * 门店代码
     */
    private String code;
    /**
     * 所在洲名称
     */
    private String continentName;
    /**
     * 坐标来源（1-高德，2-百度，3-Google）
     */
    private Long coordinateSource;
    /**
     * 所在国家名称
     */
    private String countryName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 传真
     */
    private String fax;
    /**
     * 门店id
     */
    private String id;
    /**
     * 是否机场店
     */
    private Boolean isAirport;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 门店名称
     */
    private String name;
    /**
     * 结构化营业时间
     */
    private List<RentStoreOpenTime> openTimeStruct;
    /**
     * 非结构化营业时间。建议填写结构化营业时间
     */
    private String openTimes;
    /**
     * 该门店的父级门店的标识编码，如果没有父级门店，留空
     */
    private String parentStoreCode;
    /**
     * 支持的支付方式,0-全部,1-门店付,2-在线付
     */
    private Long payMode;
    /**
     * 门店图片
     */
    private String picture;
    /**
     * 还车指引
     */
    private String returnGuide;
    /**
     * 收费上门取送车收费规则
     */
    private String serviceOnDoorFeeDesc;
    /**
     * 1-免费上门取还车 2-收费上门取还车车 3-自行前往门店取还车4-店员免费接您至门店取车
     */
    private String serviceOnDoorType;
    /**
     * 门店类型1-门店，2-提车点，（国内租车专用）
     */
    private Long storeType;
    /**
     * 品牌商id
     */
    private String supplierId;
    /**
     * 门店标签（特殊服务），及其详细说明
     */
    private List<RentStoreTagService> tagServices;
    /**
     * 已废弃。门店标签
     */
    private String tags;
    /**
     * 取车指引
     */
    private String takeGuide;
    /**
     * 联系电话，支持多个，用逗号分隔
     */
    private String telephones;
    /**
     * 距离（米）
     */
    private Long distance;
}
