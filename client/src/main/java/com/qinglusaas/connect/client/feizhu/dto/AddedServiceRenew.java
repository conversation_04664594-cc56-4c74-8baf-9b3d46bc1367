package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:09
 */
@Data
public class AddedServiceRenew {
    /**
     * 增值服务总价
     */
    private String amount;
    /**
     * 是否必选服务，必选服务不能取消
     */
    private int isFixed;
    /**
     * 是否一口价，0-否，1-是。如果是一口价，即下单时候计算金额不会乘以天数。例如手续费等,价格以增值服务单价为准。
     */
    private Long isOneWayFee;
    /**
     * 不包含增值服务说明
     */
    private List<String> listExcloud;
    /**
     * 包含增值服务说明
     */
    private List<String> listInclude;
    /**
     * 增值服务单价
     */
    private String price;
    /**
     * 增值服务数量
     */
    private String quantity;
    /**
     * 增值服务Code
     */
    private String serviceCode;
    /**
     * 增值服务描述
     */
    private String serviceDesc;
    /**
     * 增值服务名称 基本服务费必填
     */
    private String serviceName;
    /**
     * 1-基础服务费(基本险-必传) 2-尊享服务费（不计免赔险）  3-其他保险  4-其他服务费
     */
    private Long serviceType;

    public static AddedServiceRenew instanceOf(AddedService addedService) {
        AddedServiceRenew addedServiceRenew = new AddedServiceRenew();
        addedServiceRenew.setAmount(addedService.getAmount());
        addedServiceRenew.setIsFixed(addedService.getIsFixed() ? 1 : 0);
        addedServiceRenew.setIsOneWayFee(addedService.getIsOneWayFee());
        addedServiceRenew.setListExcloud(addedService.getListExcloud());
        addedServiceRenew.setListInclude(addedService.getListInclude());
        addedServiceRenew.setPrice(addedService.getPrice());
        addedServiceRenew.setQuantity(addedService.getQuantity());
        addedServiceRenew.setServiceCode(addedService.getServiceCode());
        addedServiceRenew.setServiceDesc(addedService.getServiceDesc());
        addedServiceRenew.setServiceName(addedService.getServiceName());
        addedServiceRenew.setServiceType(addedService.getServiceType());
        return addedServiceRenew;
    }
}
