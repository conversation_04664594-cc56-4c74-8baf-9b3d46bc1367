package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:20
 */
@Data
public class CancelStrategyStruct {

    /**
     * 具体取消收费规则。示例 "cancelRules":[{"num":0,"time":2},{"num":30.0,"time":0},{"num":100,"time":-999999}]表示用车前两小时取消不收违约金。两小时内收取30%的违约金。用车时间后取消收取100%的违约金
     */
    private List<CancelRule> cancelRules;
    /**
     * 取消补充说明
     */
    private String cancelTips;
    /**
     * 取消类型。1-按照订单金额百分比收取取消费用，2-按照固定金额收取取消费用
     */
    private Long cancelType;
    /**
     * 取消用车时，取车前多久时间取消 需要商家进行人工审核，单位：小时。如果不需要人工审核，则该值设置为0，表示取车时间前都可直接取消
     */
    private Long manualCheckTime;
    /**
     * 是否支持取消。true-支持，false-不支持。支持的情况下，具体规则见以下几个参数字段
     */
    private Boolean supportCancel;
}
