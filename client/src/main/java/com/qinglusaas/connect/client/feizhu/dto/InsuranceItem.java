package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * 保险项目
 */
@Data
public class InsuranceItem {
    /**
     * 不同项，包含的值是不一样，如果都有则把多个枚举传入，没有则不传 1:包含玻璃,2:包含车轮
     */
    private List<Integer> subIncludes;
    /**
     * 包含此内容项 不包含项:null||false 包含项:true
     */
    private Boolean include;
    /**
     * 标题
     */
    private String name;
    /**
     * 1：车损保障 2：三者保障 3：免折旧费 4：免停运费 5：无需垫付
     */
    private Integer type;
    /**
     * 折旧费比例 e.g: 车损5000元以下，无需承担因车损导致的折旧费；超出部分需要承担维修总额的30%作为折旧费 30% 输入30
     */
    private Integer percent;
    /**
     * 起赔额或保额上限 起赔额：用户自付的部分，保险公司只在超过这个部分时才赔偿。 起赔额：保险公司承担的最高赔偿金额，超过这个金额的部分需要用户自负。
     */
    private Integer bounds;
}
