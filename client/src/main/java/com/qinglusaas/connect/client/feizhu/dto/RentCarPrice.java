package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:32
 */
@Data
public class RentCarPrice {

    /**
     * 车辆每日基本租金，单位元，（国内租车专用）
     */
    private String basicRentFee;

    /**
     * 包含手续费、基本保障服务的总费用，单位元，（国内租车专用）
     */
    private String totalRentFee;

    /**
     * （国内租车专用）商家价格标识码，车辆详情接口与订单提交接口会再次传递给商家。
     */
    private String outerPriceId;

    /**
     * 总基础服务费,单位元
     */
    private String basicServiceFee;

    /**
     * 总手续费,单位元
     */
    private String poundageFee;

    /**
     * 总租金金额
     */
    private String rentFee;

    /**
     * 上门送取车费用总金额（收费上门送取车时必传）,单位元
     */
    private String onDoorFee;

    /**
     * 零散小时费总金额,单位元
     */
    private String hourFee;

    /**
     * 预定限制：时间、年龄
     */
    private BookLimit bookLimit;
    /**
     * （已废弃，挪到套餐维度）取消政策
     */
    private CancelStrategy cancelStrategy;
    /**
     * 该报价车辆包含的特征列表
     */
    private List<String> carFeatures;
    /**
     * 车型id
     */
    private String carId;
    /**
     * 修改政策
     */
    private String changeStrategy;
    /**
     * 当地货币单位（国际标准），如USD（美元），JPY（日元）
     */
    private String currencyLocal;
    /**
     * 可选，临时扩展信息字段，以KV对形式存储，key与value之间使用%3A%3B分隔，多个KV间使用%3B%3A分隔，格式：key1%3A%3Bvalue1%3B%3Akey2%3A%3Bvalue2%3B%3A。目前支持的key有：sellerId（卖家id），sellerNick（卖家昵称），wangwang（卖家旺旺），telInland（卖家国内服务电话），telOutland（卖家境外服务电话），rule（退改规则），service（特色服务），serviceRange（服务范围, 1国内, 2国外, 0 两者）
     */
    private String extInfos;
    /**
     * （已废弃，挪到套餐维度）燃油政策，0-满油取还，1-买一箱油，2-送一箱油。
     */
    private Long fuleStrategy;
    /**
     * 是否指定车型
     */
    private Boolean isSpecifyCar;
    /**
     * （已废弃，挪到套餐维度）车辆里程限制，公里。null或0代表无里程限制
     */
    private Long mileLimit;
    /**
     * 异地还车费，当地货币
     */
    private String oneWayFeeLocal;
    /**
     * 异地还车费，支付方式：1-在线支付，2-到店支付
     */
    private Long oneWayFeePaymode;
    /**
     * 异地还车费，人民币，单位元
     */
    private String oneWayFeeRMB;
    /**
     * 外部商家自定义的车型id。目前只有惠租车使用
     */
    private String outCarId;
    /**
     * 取车门店id
     */
    private String pickUpStoreId;
    /**
     * 套餐价格列表
     */
    private List<RentCarPriceCombo> priceCombos;
    /**
     * 预定须知
     */
    private String reservationNotes;
    /**
     * 还车门店id
     */
    private String returnStoreId;
    /**
     * 品牌商id
     */
    private String supplierId;
    /**
     * 国内租车专用！支持carId+pickUpStoreId维度的标签，标签之间用英文逗号分隔,约束：只允许传以下值tag的数字： 新版标签： TAG_3(3,"信用双免"),TTAG_14(14,"配手机支架"),TAG_17(17,"24小时营业"),TAG_18(18,"倒车雷达"),TAG_20(20,"配纸巾"),TAG_21(21,"配饮用水"),TAG_22(22,"配车充"),TAG_25(25,"儿童座椅"),TAG_26(26,"倒车影像"),TAG_27(27,"配行车记录仪"),TAG_30(30,"一车一洗"),TAG_32(32,"一年内新车"),TAG_33(33,"两年内新车"),TAG_34(34,"三年内新车"),TAG_39(39,"半年内新车"),TAG_40(40,"油量保障"),TAG_41(41,"不限里程")如有需求新增请联系业务运营。 注意：以下标签将在新版废弃，请注意按照新版标签返回： TAG_1(1,"信用免押·免车辆押金"),TAG_2(2,"信用免押·免违章押金")//如果有3，就必须将1或2去掉，3表示全免；TAG_4(4,"送车上门"),TAG_5(5,"上门取车"),TAG_6(6,"上门取送"),TAG_7(7,"快速取还"),TAG_8(8,"车况佳"),TAG_9(9,"满油取还"),TAG_10(10,"3万公里内新车"),TAG_11(11,"3公里内免费接送"),TAG_12(12,"支持异点还车"),TAG_13(13,"新款车"),TAG_15(15,"老款车"),TAG_16(16,"免费接至门店取车"),TAG_19(19,"服务专业"),TAG_23(23,"取还便捷"),TAG_24(24,"快速取"),TAG_28(28,"5公里内免费接送"),TAG_29(29,"无需等待"),TAG_31(31,"免费上门取送车"),TAG_35(35,"实时结算"),TAG_36(36,"能源免费"),TAG_37(37,"纯电动"),TAG_38(38,"旅行车"),TAG_58(58, "安心租")
     */
    private String tags;
    /**
     * 第三方扩展信息 透传
     */
    private String thirdExtInfos;

    /**
     * 增值服务（国内租车专用）
     */
    private List<AddedService> addedServiceList;

}
