package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7 11:27
 */

@Data
public class StoreVehicleModelCalendarInfo {

    /**
     * 门店id
     */
    private String storeCode;

    /**
     * 车型id
     */
    private String vehicleModelCode;

    /**
     * 车型日历信息
     */
    private List<PriceAndStock> priceAndStockList;

    @Data
    public static class PriceAndStock {
        /**
         * 日期
         */
        private String date;

        /**
         * 当日价格
         */
        private Long vehicleModelPrice;

        /**
         * 剩余车辆数
         */
        private Long canSaleVehicleNumber;
    }

}
