package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:27
 */
@Data
public class RentCar {

    /**
     * 汽车品牌名
     */
    private String autoBrand;
    /**
     * 汽车品牌英文名
     */
    private String autoBrandEn;
    /**
     * 车辆描述
     */
    private String carDesc;
    /**
     * 牌照类型，1-本地牌，2-外地牌, 0-不限
     */
    private Long carLicense;
    /**
     * 牌照描述
     */
    private String carLicenseDesc;
    /**
     * 该车辆是否支持信用免押（国内租车专用），0 不支持，1 免车辆押金，2 免违章押金，3 均支持
     */
    private Long creditSupport;
    /**
     * 车门数
     */
    private Long doorNum;
    /**
     * 燃油类型，枚举值。1-汽油，2-柴油，3-油电混合，4-电动，99-其他类型
     */
    private Long fuelType;
    /**
     * 是否有空调
     */
    private Boolean hasAirConditioner;
    /**
     * 车型id
     */
    private String id;
    /**
     * 车辆名称
     */
    private String name;
    /**
     * 车辆英文名称
     */
    private String nameEn;
    /**
     * 排量类型，1-T，2-L
     */
    private Long outputType;
    /**
     * 排量值
     */
    private String outputVolumn;
    /**
     * 车型年份（国内租车专用）
     */
    private String modelYear;
    /**
     * 车款
     */
    private String carName;
    /**
     * 车辆厢式。2两厢，3三厢，4SUV，5MPV，6跑车，7客车，8，房车，9皮卡，10硬顶敞篷，11软顶敞篷，12掀背，13旅行，14货车
     */
    private String carRiage;
    /**
     * 可容纳行李数描述，如 2大2小
     */
    private String packageDesc;
    /**
     * 车型图片，可有多张，用逗号分隔
     */
    private String pic;
    /**
     * 座位数
     */
    private Long seatNum;
    /**
     * 类似车型
     */
    private String similarCar;
    /**
     * 国际标准车型代码
     */
    private String sipp;
    /**
     * 车辆标签，每个标签不多于6个字，标签之间用逗号分隔
     */
    private String tags;
    /**
     * 变数箱类型，枚举值。1-手动，2-自动，3-手自一体（国内租车新增）
     */
    private Long transmission;
    /**
     * 可选，车辆分类。枚举值。国际租车：1-微型轿车,2-中大型轿车,3-SUV,4-旅行车,5-跑车,6-商务车,7-其他。国内租车：10-经济型,9-舒适型,3-SUV,6-商务车,5-跑车,7-其他.11-豪华型,12房车
     */
    private Long type;
}
