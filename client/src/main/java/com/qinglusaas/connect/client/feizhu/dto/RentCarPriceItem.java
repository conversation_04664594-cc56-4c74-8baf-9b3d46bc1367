package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:50
 */
@Data
public class RentCarPriceItem {

    /**
     * 当地货币单位（国际标准），如USD（美元），JPY（日元）
     */
    private String currencyLocal;
    /**
     * 到店支付价格，单位元
     */
    private String offlinePaymentCNY;
    /**
     * 到店支付价格，当地货币单位
     */
    private String offlinePaymentLocal;
    /**
     * 可在线支付价格，单位元
     */
    private String onlinePaymentCNY;
    /**
     * 可在线支付价格，当地货币单位
     */
    private String onlinePaymentLocal;
    /**
     * 支付模式。1-在线支付（可能有部分价格需要到店付，如赫兹的异地还车费），2-到店线下支付（肯定不会有线上支付的部分）
     */
    private Long payMode;
    /**
     * 价格标识符
     */
    private String priceId;
    /**
     * 价格类型。1-销售价，2-低价
     */
    private Long priceType;
    /**
     * 赫兹专用，价格码。如ABC
     */
    private String rateQualifier;
    /**
     * 套餐总价，单位元。如果有异地还车费，则包含了异地还车费
     */
    private String totalAmountCNY;
    /**
     * 套餐总价，当地货币单位。如果有异地还车费，则包含了异地还车费
     */
    private String totalAmountLocal;
    /**
     * 赫兹专用，DOLLAR、THRIFTY请求的IT code，如IT1005560LXD
     */
    private String tourNumber;
    /**
     * 车辆单天租金，单位元。如果有异地还车费，则包含了异地还车费
     */
    private String unitAmountCNY;
    /**
     * 车辆单天租金，当地货币单位。如果有异地还车费，则包含了异地还车费
     */
    private String unitAmountLocal;

}
