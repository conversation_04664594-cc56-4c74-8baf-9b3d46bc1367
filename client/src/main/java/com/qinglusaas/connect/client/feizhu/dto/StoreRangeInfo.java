package com.qinglusaas.connect.client.feizhu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
public class StoreRangeInfo {
    /**
     * 供应商门店Code,保证唯一
     */
    @JsonProperty("store_code")
    private String storeCode;

    /**
     * 门店服务范围信息
     */
    @JsonProperty("store_service_range_list")
    private List<StoreServiceRange> storeServiceRangeList;

    /**
     * 门店服务范围
     */
    @Data
    public static class StoreServiceRange {
        /**
         * 服务商范围Code,保证一个门店下唯一
         */
        @JsonProperty("range_code")
        private String rangeCode;

        /**
         * 服务商范围名称
         */
        @JsonProperty("range_name")
        private String rangeName;

        /**
         * 是否有效true有效, false无效
         */
        @JsonProperty("is_active")
        private Boolean isActive;

        /**
         * 服务圈类型，1-圆形，2-多边形，3-当前所在城市
         */
        @JsonProperty("range_type")
        private Integer rangeType;

        /**
         * 取还车方式1 自行到店,2 送车上门,3 接至门店
         */
        @JsonProperty("pick_up_type")
        private Integer pickUpType;

        /**
         * 圆型服务范围，仅在rangeType为1时有效
         */
        @JsonProperty("circle_list")
        private List<Circle> circleList;

        /**
         * 自定义封闭图形集合，仅在rangeType为2时有效
         */
        @JsonProperty("polygon_list")
        private List<Polygon> polygonList;

        /**
         * 服务范围信息（最多3组服务信息）
         */
        @JsonProperty("service_info_list")
        private List<ServiceInfo> serviceInfoList;
    }

    /**
     * 圆形服务范围
     */
    @Data
    public static class Circle {
        /**
         * 经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("longitude")
        private String longitude;

        /**
         * 纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("latitude")
        private String latitude;

        /**
         * 半径, 单位米
         */
        @JsonProperty("radius")
        private Integer radius;
    }

    /**
     * 自定义封闭图形集合，仅在rangeType为2时有效
     */
    @Data
    public static class Polygon {
        /**
         * 点位集合，需要首位相连，连成的图形不可以相交
         */
        @JsonProperty("points")
        private List<Point> point;
    }

    /**
     * 点
     */
    @Data
    @AllArgsConstructor
    public static class Point {
        /**
         * 经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("longitude")
        private String longitude;

        /**
         * 纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("latitude")
        private String latitude;
    }

    /**
     * 服务范围信息
     */
    @Data
    public static class ServiceInfo {
        /**
         * 收费方式 0:免费, 1:收费
         */
        @JsonProperty("charge_type")
        private Integer chargeType;

        /**
         * 营业时间段总价格 精确到2位小数;单位:元。如：200.07，表示：200元7分，仅当chargeType为1时有效
         */
        @JsonProperty("charge_fee")
        private String chargeFee;

        /**
         * 	服务范围最短提前预定时间(单位:小时)，可传小时，30分钟传0.5
         */
        @JsonProperty("advance_booking_Hour")
        private String advanceBookingHour;

        /**
         * 服务范围营业开始时间,格式为HH:mm，最大值为23:59分
         */
        @JsonProperty("start_time")
        private String startTime;

        /**
         * 服务范围营业结束时间,格式为HH:mm，最大值为23:59分
         */
        @JsonProperty("end_time")
        private String endTime;
    }
}
