package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:34
 */
@Data
public class BookLimit {

    /**
     * 提前预定小时数
     */
    private Long bookAdvanceHour;
    /**
     * 驾龄要求，xx年以上驾龄
     */
    private Long driveAgeLimit;
    /**
     * 需要额外付费的年龄段，格式：num1-num2
     */
    private String extraPayAge;
    /**
     * 不限制的年龄段，格式：num1-num2
     */
    private String unLimitAge;
    /**
     * 不允许预定的年龄段，格式：num1-num2
     */
    private String unSupportAge;
}
