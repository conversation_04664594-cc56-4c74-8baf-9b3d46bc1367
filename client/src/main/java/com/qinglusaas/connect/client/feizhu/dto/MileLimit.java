package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/30 15:48
 */
@Data
public class MileLimit {

    /**
     * 超里程收费标准 描述。
     */
    private String excessLimitDesc;
    /**
     * 是否有里程限制，true：有
     */
    private Boolean hasLimit;
    /**
     * 具体限制的里程数，单位：公里
     */
    private Long limitNum;
    /**
     * 限制里程单位，公里：km，英里：mi
     */
    private String limitUnit;
}
