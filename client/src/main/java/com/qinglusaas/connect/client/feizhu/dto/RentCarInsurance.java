package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

/**
 * 租车保险
 */
@Data
public class RentCarInsurance {
    /**
     * 优惠后保险每日单价，单位元
     */
    private String dailyUnitPrice;
    /**
     * 商家增值服务编码
     */
    private String serviceCode;
    /**
     * 保险每日单价,人民币，单位元
     */
    private String originDailyUnitPrice;
    /**
     * 保险名称
     */
    private String insuranceName;
    /**
     * 类型 1-基础服务(基本险-必传) 2-优享服务（不计免赔险） 3-尊享服务, 99-其他保险
     */
    private Long insuranceType;
    /**
     * 此保险包含的项
     */
    private List<InsuranceItem> insuranceItems;
    /**
     * 是否必选，必选不能取消选择,false-非必选，true-必选
     */
    private Boolean required;
    /**
     * 飞猪商家增值服务标准编码 11：车辆租金 12：零散小时费 13：手续费 14：商家优惠，传负值 21：基础服务 22：优享服务 23：尊享服务 24：其他保险服务费 31：送车上门服务费 32：上门还车服务费 33：异地还车费 34：夜间服务费 101：婴儿座椅（0~15个月） 102：儿童座椅（9个月~6岁） 103：儿童增高座垫（6~12岁） 111：防滑链 112：雪地胎 201：一口价活动 OTHER：其他费用
     */
    private String sid;
}
