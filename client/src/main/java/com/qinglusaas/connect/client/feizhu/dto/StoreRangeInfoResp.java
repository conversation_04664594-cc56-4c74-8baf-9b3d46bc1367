package com.qinglusaas.connect.client.feizhu.dto;

import lombok.Data;

import java.util.List;

@Data
public class StoreRangeInfoResp {
    /**
     * 供应商门店Code,保证唯一
     */
    private String storeCode;

    /**
     * 门店服务范围信息
     */
    private List<StoreServiceRange> storeServiceRangeList;

    /**
     * 门店服务范围
     */
    @Data
    public static class StoreServiceRange {
        /**
         * 服务商范围Code,保证一个门店下唯一
         */
        private String rangeCode;

        /**
         * 服务商范围名称
         */
        private String rangeName;

        /**
         * 是否有效true有效, false无效
         */
        private Boolean isActive;

        /**
         * 服务圈类型，1-圆形，2-多边形，3-当前所在城市
         */
        private Integer rangeType;

        /**
         * 取还车方式1 自行到店,2 送车上门,3 接至门店
         */
        private Integer pickUpType;

        /**
         * 圆型服务范围，仅在rangeType为1时有效
         */
        private List<Circle> circleList;

        /**
         * 自定义封闭图形集合，仅在rangeType为2时有效
         */
        private List<Polygon> polygonList;

        /**
         * 服务范围信息（最多3组服务信息）
         */
        private List<ServiceInfo> serviceInfoList;
    }

    /**
     * 圆形服务范围
     */
    @Data
    public static class Circle {
        /**
         * 经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String longitude;

        /**
         * 纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String latitude;

        /**
         * 半径, 单位米
         */
        private Integer radius;
    }

    /**
     * 自定义封闭图形集合，仅在rangeType为2时有效
     */
    @Data
    public static class Polygon {
        /**
         * 点位集合，需要首位相连，连成的图形不可以相交
         */
        private List<Point> point;
    }

    /**
     * 点
     */
    @Data
    public static class Point {
        /**
         * 经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String longitude;

        /**
         * 纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        private String latitude;
    }

    /**
     * 服务范围信息
     */
    @Data
    public static class ServiceInfo {
        /**
         * 收费方式 0:免费, 1:收费
         */
        private Integer chargeType;

        /**
         * 营业时间段总价格 精确到2位小数;单位:元。如：200.07，表示：200元7分，仅当chargeType为1时有效
         */
        private String chargeFee;

        /**
         * 	服务范围最短提前预定时间(单位:小时)，可传小时，30分钟传0.5
         */
        private String advanceBookingHour;

        /**
         * 服务范围营业开始时间,格式为HH:mm，最大值为23:59分
         */
        private String startTime;

        /**
         * 服务范围营业结束时间,格式为HH:mm，最大值为23:59分
         */
        private String endTime;
    }
}
