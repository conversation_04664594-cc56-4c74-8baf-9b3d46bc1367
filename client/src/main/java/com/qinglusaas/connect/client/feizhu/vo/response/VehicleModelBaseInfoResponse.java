package com.qinglusaas.connect.client.feizhu.vo.response;

import com.qinglusaas.connect.client.feizhu.dto.VehicleModelInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 11:59
 * 平台获取门店车型基础信息
 */
@Data
public class VehicleModelBaseInfoResponse extends FeiZhuBaseResponse{
    /**
     * 返回信息
     */
    private SearchResp searchResp;

    @Data
    public static class SearchResp {
        /**
         * 车型基础信息列表
         */
        private List<VehicleModelInfoResp> storeVehicleModelInfoList;
    }
}
