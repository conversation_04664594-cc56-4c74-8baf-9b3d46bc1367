deployment.env=dev
quarkus.http.host=0.0.0.0

quarkus.micrometer.export.otlp.enabled=true
quarkus.micrometer.export.otlp.url=http://otel-collector.dev.svc.cluster.local:4317/v1/metrics
quarkus.micrometer.export.otlp.resourceAttributes=service.name=${quarkus.application.name},service.version=1.0.0,deployment.environment=${deployment.env},service.namespace=${deployment.env}
quarkus.opentelemetry.enabled=true
quarkus.opentelemetry.logs.enabled=true
quarkus.opentelemetry.logs.exporter.otlp.endpoint=http://otel-collector.dev.svc.cluster.local:4317
quarkus.opentelemetry.logs.resource-attributes=service.name=${quarkus.application.name},service.version=1.0.0,deployment.environment=${deployment.env},service.namespace=${deployment.env}
quarkus.opentelemetry.tracer.enabled=true
quarkus.opentelemetry.tracer.exporter.otlp.endpoint=http://otel-collector.dev.svc.cluster.local:4317
quarkus.opentelemetry.tracer.resource-attributes=service.name=${quarkus.application.name},service.version=1.0.0,deployment.environment=${deployment.env},service.namespace=${deployment.env}

quarkus.logging-manager.ui.always-include=true
quarkus.jib.jvm-arguments=-agentlib:jdwp=transport=dt_socket\\,server=y\\,suspend=n\\,address=*:5005
# Logging
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.category."FEIZHU".level=DEBUG
quarkus.log.level=DEBUG
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=4096
quarkus.micrometer.binder.http-client.enabled=true
quarkus.micrometer.binder.http-server.enabled=true
quarkus.micrometer.binder.jvm=true
quarkus.micrometer.binder.vertx.enabled=true
quarkus.micrometer.binder.netty.enabled=true
quarkus.micrometer.binder.redis.enabled=true
quarkus.micrometer.binder.stork.enabled=true
quarkus.micrometer.export.prometheus.enabled=true
quarkus.micrometer.binder.http-server.max-uri-tags=500
quarkus.http.limits.max-connections=100
# default
quarkus.datasource.username=qinglu
quarkus.datasource.password=m3rTQ6XNA043uFSf
quarkus.datasource.jdbc.url=*********************************************************************
quarkus.hibernate-orm.log.bind-parameters=true
quarkus.hibernate-orm.log.sql=true
# common database
quarkus.datasource.common.username=qinglu
quarkus.datasource.common.password=m3rTQ6XNA043uFSf
quarkus.datasource.common.jdbc.url=*********************************************************************
quarkus.hibernate-orm.common.log.bind-parameters=true
quarkus.hibernate-orm.common.log.sql=true
# merchant database
quarkus.datasource.merchant.username=qinglu
quarkus.datasource.merchant.password=m3rTQ6XNA043uFSf
quarkus.datasource.merchant.jdbc.url=***********************************************************************
quarkus.hibernate-orm.merchant.log.bind-parameters=true
quarkus.hibernate-orm.merchant.log.sql=true
# store database
quarkus.datasource.store.username=qinglu
quarkus.datasource.store.password=m3rTQ6XNA043uFSf
quarkus.datasource.store.jdbc.url=********************************************************************
quarkus.hibernate-orm.store.log.bind-parameters=true
quarkus.hibernate-orm.store.log.sql=true
# trade database
quarkus.datasource.trade.username=qinglu
quarkus.datasource.trade.password=m3rTQ6XNA043uFSf
quarkus.datasource.trade.jdbc.url=********************************************************************
quarkus.hibernate-orm.trade.log.bind-parameters=true
quarkus.hibernate-orm.trade.log.sql=true
# vehicle database
quarkus.datasource.vehicle.username=qinglu
quarkus.datasource.vehicle.password=m3rTQ6XNA043uFSf
quarkus.datasource.vehicle.jdbc.url=**********************************************************************
quarkus.hibernate-orm.vehicle.log.bind-parameters=true
quarkus.hibernate-orm.vehicle.log.sql=true
# Redis
quarkus.redis.hosts=redis://r-uf6kyhn0dm2j5au3vp.redis.rds.aliyuncs.com:6379
quarkus.redis.password=hello@111

quarkus.redisson.single-server-config.address=redis://r-uf6kyhn0dm2j5au3vppd.redis.rds.aliyuncs.com:6379
quarkus.redisson.single-server-config.password=hello@111
quarkus.redisson.single-server-config.database=0
quarkus.redisson.threads=8
quarkus.redisson.netty-threads=16

# api host
# feizhu-pre
quarkus.rest-client.feizhu-api.url=https://eco.taobao.com
quarkus.rest-client.ctrip.api.url=http://ctrip-proxy.qinglusaas-dev.com/dev
quarkus.rest-client.saas.api.url=http://qinglu-server-dev:8080
quarkus.rest-client.hello.api.url=http://fat-hello-openapi.hellobike.com
quarkus.rest-client.etc.api.url=https://test.etc.hrhaoche.com/openapi/
# sign
qinglu.sign.app-key=qinglu
etc.sign.app-secret=qIc!EngH2c*1A&Cb
hello.sign.app-secret=fa82fab7ff8c4ad2aa99d5e0ea83a73c
hello.sign.app-key=caoweicode-wnkWzgt6
qinglu.sign.app-secret=lrtQyRKLmrdlfFAQ
raptor.sign.app-key=raptor
raptor.sign.app-secret=afD7sQBzrIadLAAx
# logging debug switch
raptor.logging.tracer.debug=true
raptor.logging.file.debug=true
raptor.logging.debug=true
quarkus.swagger-ui.always-include=true
# thread
quarkus.thread-pool.core-threads=2
quarkus.thread-pool.max-threads=50
quarkus.thread-pool.queue-size=10000
# JDBC
quarkus.datasource.jdbc.initial-size=5
quarkus.datasource.jdbc.min-size=5
quarkus.datasource.jdbc.max-size=50
quarkus.datasource.jdbc.idle-removal-interval=5M
quarkus.datasource.jdbc.connection-lifetime=1H
quarkus.datasource.jdbc.check-valid-connection-sql=SELECT 1
# \u7B7E\u540D
quarkus.datasource.common.jdbc.initial-size=5
quarkus.datasource.common.jdbc.min-size=5
quarkus.datasource.common.jdbc.max-size=50
quarkus.datasource.common.jdbc.idle-removal-interval=5M
quarkus.datasource.common.jdbc.connection-lifetime=1H
quarkus.datasource.common.jdbc.check-valid-connection-sql=SELECT 1
# \u5546\u5BB6
quarkus.datasource.merchant.jdbc.initial-size=5
quarkus.datasource.merchant.jdbc.min-size=5
quarkus.datasource.merchant.jdbc.max-size=50
quarkus.datasource.merchant.jdbc.idle-removal-interval=5M
quarkus.datasource.merchant.jdbc.connection-lifetime=1H
quarkus.datasource.merchant.jdbc.check-valid-connection-sql=SELECT 1
# \u95E8\u5E97
quarkus.datasource.store.jdbc.initial-size=5
quarkus.datasource.store.jdbc.min-size=5
quarkus.datasource.store.jdbc.max-size=50
quarkus.datasource.store.jdbc.idle-removal-interval=5M
quarkus.datasource.store.jdbc.connection-lifetime=1H
quarkus.datasource.store.jdbc.check-valid-connection-sql=SELECT 1
# \u4EA4\u6613
quarkus.datasource.trade.jdbc.initial-size=5
quarkus.datasource.trade.jdbc.min-size=5
quarkus.datasource.trade.jdbc.max-size=50
quarkus.datasource.trade.jdbc.idle-removal-interval=5M
quarkus.datasource.trade.jdbc.connection-lifetime=1H
quarkus.datasource.trade.jdbc.check-valid-connection-sql=SELECT 1
# \u8F66\u578B
quarkus.datasource.vehicle.jdbc.initial-size=5
quarkus.datasource.vehicle.jdbc.min-size=5
quarkus.datasource.vehicle.jdbc.max-size=50
quarkus.datasource.vehicle.jdbc.idle-removal-interval=5M
quarkus.datasource.vehicle.jdbc.connection-lifetime=1H
quarkus.datasource.vehicle.jdbc.check-valid-connection-sql=SELECT 1
# HTTP
quarkus.http.io-threads=2
quarkus.http.worker-threads=16
quarkus.http.read-timeout=1M

quarkus.cache.caffeine."api-conn".initial-capacity=100
quarkus.cache.caffeine."api-conn".expire-after-write=5S
quarkus.cache.caffeine."api-conn".maximum-size=5000
quarkus.cache.caffeine."api-conn".metrics-enabled=true

quarkus.cache.caffeine."api-conn-app-key-channel-vendor-code".initial-capacity=100
quarkus.cache.caffeine."api-conn-app-key-channel-vendor-code".expire-after-write=5S
quarkus.cache.caffeine."api-conn-app-key-channel-vendor-code".maximum-size=5000
quarkus.cache.caffeine."api-conn-app-key-channel-vendor-code".metrics-enabled=true

# 根据环境配置业务消息topic后缀
topic.suffix=${deployment.env}

# URL代理配置 - dev
url.proxy.domain-mapping.enabled=true
# 开发环境的域名映射
url.proxy.domain-mapping.origin.url=https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com
url.proxy.domain-mapping.proxy.url=http://img-p.t.voomogo.cn
