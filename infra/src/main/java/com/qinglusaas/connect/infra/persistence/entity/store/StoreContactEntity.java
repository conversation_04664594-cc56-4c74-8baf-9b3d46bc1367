package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 门店联系人
 *
 * @TableName store_contact
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "store_contact")
@Where(clause = "deleted = 0")
public class StoreContactEntity extends AbstractEntity implements Serializable {

//    /**
//     * 门店ID
//     */
//    @NotNull(message = "门店不能为空")
//    @Column(name = "store_id")
//    private Long storeId;

    /**
     * 门店ID
     */
    @ManyToOne
    @JoinColumn(name = "store_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreInfoEntity storeInfoEntity;

    /**
     * 联系人姓名
     */
    @NotBlank(message = "[联系人姓名]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @Length(max = 20, message = "编码长度不能超过20")
    @Column(name = "link_name")
    private String linkName;

    /**
     * 手机国家区号
     */
    @NotNull(message = "[手机国家区号]不能为空")
    @Size(max = 4, message = "编码长度不能超过4")
    @Length(max = 4, message = "编码长度不能超过4")
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 手机号码
     */
    @NotNull(message = "[手机号码]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @Length(max = 15, message = "编码长度不能超过15")
    @Column(name = "mobile")
    private String mobile;

    /**
     * 电话区号
     */
    @NotNull(message = "[电话区号]不能为空")
    @Size(max = 5, message = "编码长度不能超过5")
    @Length(max = 5, message = "编码长度不能超过5")
    @Column(name = "tel_area")
    private String telArea;

    /**
     * 电话号码
     */
    @NotNull(message = "[电话号码]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @Length(max = 20, message = "编码长度不能超过20")
    @Column(name = "tel")
    private String tel;

    /**
     * 分机号
     */
    @NotNull(message = "[分机号]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @Length(max = 10, message = "编码长度不能超过10")
    @Column(name = "tel_ext")
    private String telExt;

    /**
     * 邮箱
     */
    @NotNull(message = "[邮箱]不能为空")
    @Size(max = 60, message = "编码长度不能超过60")
    @Length(max = 60, message = "编码长度不能超过60")
    @Column(name = "email")
    private String email;

    /**
     * 联系人类型 0:常用联系人;1:备用联系人
     */
    @NotNull(message = "[联系人类型 0:常用联系人;1:备用联系人]不能为空")
    @Column(name = "contact_type")
    private Integer contactType;

    /**
     * 操作人ID
     */
    @NotNull(message = "[操作人ID]不能为空")
    @Column(name = "op_user_id")
    private Integer opUserId;

}
