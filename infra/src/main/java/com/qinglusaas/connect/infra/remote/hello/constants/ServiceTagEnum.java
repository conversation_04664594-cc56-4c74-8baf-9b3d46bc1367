package com.qinglusaas.connect.infra.remote.hello.constants;

import com.qinglusaas.connect.client.hello.dto.ServiceTagDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/28 17:25
 */
public interface ServiceTagEnum {

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum VehicleTag {
        REVERSING_CAMERA("1", "倒车影像"),
        REVERSING_RADAR("2", "倒车雷达"),
        NEW_SIX_MONTHS("3", "半年内新车"),
        NEW_ONE_YEAR("4", "一年内新车"),
        NEW_TWO_YEAR("5", "两年内新车"),
        NEW_THREE_YEAR("6", "三年内新车"),
        CAR_RECORDERS("7", "配行车记录仪"),
        PHONE_HOLDER("8", "手机支架"),
        UNLIMITED_MILEAGE("9", "不限里程"),
        ETC("10", "ETC"),
        CAR_FRIDGE("11", "车载冰箱"),
        AIRPORT_LOUNGE("12", "赠机场休息室"),
        HUMIDIFIER("13", "加湿器"),
        LEATHER_SEATS("14", "真皮座椅"),
        OXYGEN_GENERATORS("15", "制氧机"),
        SNOW_TYRES("16", "雪地胎"),
        UMBRELLAS("28", "雨伞");

        private String code;

        private String name;

        public ServiceTagDTO getServiceTagDTO() {
            return new ServiceTagDTO(this.getCode(), this.getName());
        }

        public static Map<String, ServiceTagEnum.VehicleTag> getNameMapping() {
            return Arrays.stream(VehicleTag.values()).collect(Collectors.toMap(VehicleTag::getName, o -> o));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum StoreTag {
        CONFIRM_NOW("17", "立即确认"),
        NATIONAL_CHAIN("18", "全国连锁"),
        FREE_CANCELLATION("19", "免费取消"),
        ALL_DAY("20", "24小时营业"),
        WASH("29", "一车一洗"),
        DISINFECTION("30", "消毒后交车");

        private String code;

        private String name;

        public ServiceTagDTO getServiceTagDTO() {
            return new ServiceTagDTO(this.getCode(), this.getName());
        }
    }
}
