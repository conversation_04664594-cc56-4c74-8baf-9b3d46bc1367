package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 车型分组
 *
 * @TableName vehicle_model_group
 */
@Table(name = "vehicle_model_group")
@Data
@Entity
@Where(clause = "deleted = 0")
public class VehicleModelGroupEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 分组名称(eg:舒适型/经济型)
     */
    private String name;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 是否删除 1:已删除;  0:未删除
     */
    private Integer deleted;

}