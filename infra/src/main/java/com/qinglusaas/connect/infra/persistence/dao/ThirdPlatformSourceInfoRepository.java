package com.qinglusaas.connect.infra.persistence.dao;

import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.common.ThirdPlatformSourceInfoEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import javax.transaction.Transactional;
import java.util.Optional;

@ApplicationScoped
public class ThirdPlatformSourceInfoRepository extends AbstractPanacheRepository<ThirdPlatformSourceInfoEntity, Long> {

    public Optional<ThirdPlatformSourceInfoEntity> findByMerchantIdAndChannelIdAndThirdId(Long merchantId, Long channelId, String thirdId,Byte type) {
        return find("merchantId = :merchantId and channelId = :channelId and thirdId = :thirdId and type = :type",
            Parameters.with("merchantId", merchantId).and("channelId", channelId).and("thirdId", thirdId)
                .and("type", type)).firstResultOptional();
    }

}
