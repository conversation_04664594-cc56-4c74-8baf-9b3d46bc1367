package com.qinglusaas.connect.infra.persistence.entity.trade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/10/5 14:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_info")
public class OrderInfoEntity extends AbstractEntity implements Serializable {

    @Column(name = "vehicle_id", nullable = false)
    private long vehicleId;

    @Column(name = "vehicle_name", nullable = false, length = 50)
    private String vehicleName;

    @Column(name = "pickup_date", nullable = false)
    private Timestamp pickupDate;

    @Column(name = "return_date", nullable = false)
    private Timestamp returnDate;

    @Column(name = "pickup_store_id", nullable = false)
    private long pickupStoreId;

    @Column(name = "return_store_id", nullable = false)
    private long returnStoreId;

    @Column(name = "pickup_city_code", nullable = false)
    private int pickupCityCode;

    @Column(name = "return_city_code", nullable = false)
    private int returnCityCode;

    @Column(name = "pickup_addr", nullable = false, length = 50)
    private String pickupAddr;

    @Column(name = "return_addr", nullable = false, length = 50)
    private String returnAddr;

    @Column(name = "is_pickup_ondoor", nullable = false)
    private byte isPickupOndoor;

    @Column(name = "pickup_ondoor_address", nullable = false, length = 100)
    private String pickupOndoorAddress;

    @Column(name = "pay_amount", nullable = false)
    private long payAmount;

    @Column(name = "receivable_amount", nullable = false)
    private long receivableAmount;

    @Column(name = "pay_mode", nullable = false)
    private byte payMode;

    @Column(name = "free_deposit_degree", nullable = false)
    private int freeDepositDegree;

    @Column(name = "partner_user_id", nullable = false, length = 35)
    private String partnerUserId;

    @Column(name = "partner_user_name", nullable = false, length = 50)
    private String partnerUserName;

    @Column(name = "source_order_id", nullable = false, length = 35)
    private String sourceOrderId;

    @Column(name = "order_source", nullable = false)
    private byte orderSource;

    @Column(name = "order_status", nullable = false)
    private byte orderStatus;

    @Column(name = "pay_status", nullable = false)
    private byte payStatus;

    @Column(name = "op_user_id", nullable = false)
    private int opUserId;

    @Column(name = "order_time", nullable = false)
    private long orderTime;

    @Column(name = "order_op_time", nullable = false)
    private long orderOpTime;
}
