package com.qinglusaas.connect.infra.remote.saas.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 返回统一类型.
 * @see com.ql.dto.ApiResultResp
 *
 * <AUTHOR>
 * @date 2022/9/18 15:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasResponse<T> {

    public final static String SUCCESS_CODE = "0";
    private String code;
    private String message;

    private T data;

    public SaasResponse success(T t) {
        this.code = SUCCESS_CODE;
        this.message = "success";
        this.data = t;
        return this;
    }

    public SaasResponse failed(String code, String message) {
        this.code = code;
        this.message = message;
        return this;
    }

    public SaasResponse failed(String code) {
        failed(code, "");
        return this;
    }

    public boolean isOk() {
        return SUCCESS_CODE.equals(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
