package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.ErrorCode;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.BaseResponseDTO;
import lombok.Data;

@Data
public class StorePolicyChangeNotifyResponse extends TaobaoResultResp {

    @JsonProperty("alitrip_rentcar_commodity_store_rule_change_notify_response")
    private BaseResponseDTO responseDTO;

    @Override
    public BaseResponseDTO responseDTO() {
        return responseDTO;
    }
}
