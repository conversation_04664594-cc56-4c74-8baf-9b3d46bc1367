package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.FeizhuVehicleEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class FeizhuVehicleRepository extends AbstractPanacheRepository<FeizhuVehicleEntity, Long> {


    public List<FeizhuVehicleEntity> findAllByBrandName(String brandName) {
        return find("brandName = :brandName", Parameters.with("brandName", brandName)).list();
    }

    public List<FeizhuVehicleEntity> findAllByBrandNames(Iterable<String> brandNames) {
        return find("brandName in :brandNames", Parameters.with("brandNames", brandNames)).list();
    }

    public List<FeizhuVehicleEntity> findAllByFeizhuIds(Iterable<String> feizhuIds) {
        return find("feizhuId in :feizhuIds", Parameters.with("feizhuIds", feizhuIds)).list();
    }

    public Optional<FeizhuVehicleEntity> findByFeizhuId(String feizhuId) {
        return find("feizhuId = :feizhuId", Parameters.with("feizhuId", feizhuId)).singleResultOptional();
    }
}
