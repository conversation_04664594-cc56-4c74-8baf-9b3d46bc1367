package com.qinglusaas.connect.infra.startup;

import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.common.AreaEntity;
import com.qinglusaas.connect.infra.persistence.entity.merchant.MerchantInfoEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.StoreInfoEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.CtripVehicleSubSeryEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.FeizhuVehicleEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.HelloVehicleEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBindEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBrandEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleInfoEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleModelEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleSeryEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.ThirdVehicleIdRelationEntity;
import io.quarkus.runtime.Startup;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/8/10, Thursday
 **/
@Startup
@ApplicationScoped
@Slf4j
public class PreloadStartup {

    @PostConstruct
    public void init() {
        try {
            // 一个简单的查询以预热Hibernate
            ApiConnEntity.findAll().page(0, 1).firstResultOptional();
            AreaEntity.findAll().page(0, 1).firstResultOptional();
            VehicleInfoEntity.findAll().page(0, 1).firstResultOptional();
            VehicleModelEntity.findAll().page(0, 1).firstResultOptional();
            VehicleBindEntity.findAll().page(0, 1).firstResultOptional();
            VehicleBrandEntity.findAll().page(0, 1).firstResultOptional();
            VehicleSeryEntity.findAll().page(0, 1).firstResultOptional();
            StoreInfoEntity.findAll().page(0, 1).firstResultOptional();
            FeizhuVehicleEntity.findAll().page(0, 1).firstResultOptional();
            HelloVehicleEntity.findAll().page(0, 1).firstResultOptional();
            CtripVehicleSubSeryEntity.findAll().page(0, 1).firstResultOptional();
            MerchantInfoEntity.findAll().page(0, 1).firstResultOptional();
            ThirdIdRelationEntity.findAll().page(0, 1).firstResultOptional();
            ThirdVehicleIdRelationEntity.findAll().page(0, 1).firstResultOptional();
            log.info("PreloadStartup init success");
        } catch (Exception e) {
            // Handle exception
            log.error("PreloadStartup init error", e);
        }
    }

}
