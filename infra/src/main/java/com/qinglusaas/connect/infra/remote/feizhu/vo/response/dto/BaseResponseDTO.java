package com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BaseResponseDTO {
    @JsonProperty("message_code")
    private String messageCode;
    private String message;
    @JsonProperty("request_id")
    private String requestId;

    public boolean isSuccess() {
        return "0".equals(this.messageCode);
    }
}
