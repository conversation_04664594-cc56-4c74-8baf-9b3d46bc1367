package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.client.feizhu.dto.VehicleModelInfo;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:15
 * 商家推送门店车型基础信息
 */
@Data
public class StoreVehicleModelBaseInfoReq extends TaobaoBaseReq {

    /**
     * 车型基础信息列表
     */
    @JsonProperty("store_vehicle_model_info_list")
    private List<VehicleModelInfo> storeVehicleModelInfoList;


    public StoreVehicleModelBaseInfoReq() {
        super();
        super.setMethod(APIMethod.BASE_VEHICLE_MODEL_NOTIFY.getMethod());
    }
}
