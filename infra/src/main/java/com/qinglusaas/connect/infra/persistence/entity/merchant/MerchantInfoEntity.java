package com.qinglusaas.connect.infra.persistence.entity.merchant;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Where;

/**
 * 商家信息
 * @TableName merchant_info
 */
@Table(name="merchant_info")
@Entity
@Data
@Where(clause = "deleted = 0")
public class MerchantInfoEntity extends PanacheEntityBase implements Serializable {
    /**
     * 
     */
    @Id
    private Long id;

    /**
     * 商家名称
     */
    private String name;

    /**
     * 商家简称（品牌） 如：神州租车
     */
    @Column(name = "name_short")
    private String nameShort;

    /**
     * 公司类型
     */
    @Column(name = "merchant_type")
    private Integer merchantType;

    /**
     * 注册地址
     */
    private String address;

    /**
     * 统一社会信用代码
     */
    private String uscc;

    /**
     * 营业执照有效期开始
     */
    @Column(name = "validity_start")
    private String validityStart;

    /**
     * 营业执照有效期结束   9999-99-99：长期有效
     */
    @Column(name = "validity_end")
    private String validityEnd;

    /**
     * 负责人手手机国家区号
     */
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 负责人手机号
     */
    private String phone;

    /**
     * 负责人姓名
     */
    @Column(name = "link_name")
    private String linkName;

    /**
     * 法人姓名
     */
    @Column(name = "legal_name")
    private String legalName;

    /**
     * 法人手机号
     */
    @Column(name = "legal_phone")
    private String legalPhone;

    /**
     * 法人身份证号
     */
    @Column(name = "legal_id_no")
    private String legalIdNo;

    /**
     * 删除 0:正常; 1:删除；2: 暂停;
     */
    private Integer deleted;

    /**
     * 测试用户 1:测试；0: 非测试
     */
    @Column(name = "is_test")
    private Integer isTest;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    @Column(name = "extra")
    private String extra;

    private static final long serialVersionUID = 1L;
}