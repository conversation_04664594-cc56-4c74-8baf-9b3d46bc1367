package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import java.util.List;
import java.util.Optional;
import javax.enterprise.context.ApplicationScoped;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.FeizhuVehicleEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.FeizhuVehicleSubSeryEntity;

import io.quarkus.panache.common.Parameters;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class FeizhuVehicleSubSeryRepository extends AbstractPanacheRepository<FeizhuVehicleSubSeryEntity, Long> {


    public List<FeizhuVehicleSubSeryEntity> findAllByBrandName(String brandName) {
        return find("brandName = :brandName", Parameters.with("brandName", brandName)).list();
    }

    public List<FeizhuVehicleSubSeryEntity> findAllByBrandNames(Iterable<String> brandNames) {
        return find("brandName in :brandNames", Parameters.with("brandNames", brandNames)).list();
    }

    public List<FeizhuVehicleSubSeryEntity> findAllByFeizhuIds(Iterable<String> feizhuIds) {
        return find("feizhuId in :feizhuIds", Parameters.with("feizhuIds", feizhuIds)).list();
    }

    public Optional<FeizhuVehicleSubSeryEntity> findByFeizhuId(String feizhuId) {
        return find("feizhuId = :feizhuId", Parameters.with("feizhuId", feizhuId)).singleResultOptional();
    }
}
