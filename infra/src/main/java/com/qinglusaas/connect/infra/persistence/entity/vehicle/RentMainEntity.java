package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import javax.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 租金基本信息
 */
@Data
@Entity
@Table(name = "rent_main")
@Accessors(chain = true)
public class RentMainEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "vehicle_model_id", nullable = false)
  private Long vehicleModelId;

  @Column(name = "store_id", nullable = false)
  private Long storeId;

  @Column(name = "mileage_limit", nullable = false)
  private Byte mileageLimit;

  @Column(name = "mileage", nullable = false)
  private Short mileage;

  @Column(name = "mileage_rent", nullable = false)
  private Short mileageRent;

  @Column(name = "rent_deposit", nullable = false)
  private Integer rentDeposit;

  @Column(name = "illegal_deposit", nullable = false)
  private Integer illegalDeposit;

  @Column(name = "status", nullable = false)
  private Integer status;

  @Column(name = "deleted", nullable = false)
  private Byte deleted;

  @Column(name = "last_ver", nullable = false)
  private Integer lastVer;

  @Column(name = "op_user_id", nullable = false)
  private Long opUserId;

  @Column(name = "create_time", nullable = false)
  private Long createTime;

  @Column(name = "op_time", nullable = false)
  private Long opTime;
}