package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleSeryEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class VehicleSeryRepository extends AbstractPanacheRepository<VehicleSeryEntity, Long> {

    public Optional<VehicleSeryEntity> findByIdOptional(Long id) {
        return findByIdOptional(id, 0);
    }

    public Optional<VehicleSeryEntity> findByIdOptional(long id, int deleted) {
        return find("id = :id and deleted = :deleted",
                Parameters.with("id", id).and("deleted", deleted)
        ).singleResultOptional();
    }

    public List<VehicleSeryEntity> findAllByIds(Iterable<Long> ids) {
        return findAllByIds(ids, 0);
    }

    public List<VehicleSeryEntity> findAllByIds(Iterable<Long> ids, int deleted) {
        return find("id in :ids and deleted = :deleted",
                Parameters.with("ids", ids).and("deleted", deleted)
        ).list();
    }
}
