package com.qinglusaas.connect.infra.remote.feizhu.client;

import com.qinglusaas.connect.infra.otlp.annotation.SpanClientName;
import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestQuery;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@RegisterRestClient(configKey = "feizhu-api")
@RegisterClientHeaders()
public interface FeizhuClient {

    /**
     * 淘宝奇门接口
     */
    @POST
    @Path("/router/rest")
    @SpanClientName("[feizhu]订单状态更新")
    String rentCarStatusUpdate(@RestQuery Map<String, String> req);


    /**
     * 淘宝OpenAPI。
     * 和奇门的path相同，但是是另外一套套签名参数体系
     * @param signMap path参数
     * @param body 业务参数
     */
    @POST
    @Path("/router/rest")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    @SpanClientName("[feizhu]淘宝通用接口")
    String postTaobaoApi(@RestQuery Map<String, String> signMap, String body);



    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 500) {
            return new RuntimeException("The remote service responded with HTTP 500");
        } else if (response.getStatus() == 404) {
            return new RuntimeException("Not Found 404");
        }
        return null;
    }

}
