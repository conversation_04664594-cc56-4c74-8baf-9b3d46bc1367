package com.qinglusaas.connect.infra.persistence.dao;

import com.qinglusaas.connect.infra.constant.EntityFieldsMapperEnum;
import com.qinglusaas.connect.infra.persistence.entity.store.StoreInfoEntity;
import com.qinglusaas.connect.infra.persistence.hibernate.util.SimpleQueryBuilder;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/8/30, Tuesday
 **/
@ApplicationScoped
public class StoreInfoRepository extends AbstractPanacheRepository<StoreInfoEntity, Long> {

    /**
     * 查询所有有效的门店信息
     *
     * @param merchantId 商家ID
     * @param cities     城市列表
     * @return 门店对象
     */
    public List<StoreInfoEntity> findAllStoreInfoByMerchantIdAndCityIds(Long merchantId, List<Long> cities) {
        SimpleQueryBuilder queryBuilder = new SimpleQueryBuilder();
        Map<String, Object> params = new LinkedHashMap<>();
        queryBuilder.addQueryStatement("select storeInfos from StoreInfoEntity as storeInfos");
        configFilterFindStoreInfoQueryBuilder(queryBuilder, merchantId,
                EntityFieldsMapperEnum.TEST_FLAG_NO_TEST.valueOfInteger(), cities, params);
        return find(queryBuilder.toQueryString(), params).list();
    }

    /**
     * 查询条件封装
     *
     * @param queryBuilder 查询构造器
     * @param merchantId   商家ID
     * @param isTest       是否为测试门店
     * @param cites        城市类型
     * @param params       参数键值
     */
    private void configFilterFindStoreInfoQueryBuilder(
            SimpleQueryBuilder queryBuilder,
            Long merchantId,
            Integer isTest,
            List<Long> cites,
            Map<String, Object> params) {
        queryBuilder.updateQueryStatementConditional(
                isNotEmpty(cites),
                "",
                "storeInfos.cityId in (:cites)",
                () -> params.put("cites", cites));
        queryBuilder.updateQueryStatementConditional(true, "", "storeInfos.merchantId = :merchantId",
                () -> params.put("merchantId", merchantId));
        queryBuilder.updateQueryStatementConditional(true, "", "storeInfos.isTest = :isTest",
                () -> params.put("isTest", isTest));
    }

//    /**
//     * 通过渠道ID获取门店城市关联数据列表
//     *
//     * @param merchantId 渠道ID
//     * @return
//     */
//    public List<StoreInfoCityRef> findAllStoreCityByMerchantId(Long merchantId) {
//        String sql = "SELECT " +
//                "a.id as areaId, " +
//                "s.city_code as cityCode, " +
//                "a.name as cityName " +
//                "FROM area as a, " +
//                "(SELECT s.city_code FROM store_info as s where s.merchant_id = :merchantId and is_test = :isTest group by s.city_code) as s " +
//                "WHERE s.city_code = a.code";
//        List<Object[]> result = getEntityManager().createNativeQuery(sql).setParameter("merchantId", merchantId)
//                .setParameter("isTest", EntityConstants.TEST_FLAG_NO_TEST.valueOfInteger()).getResultList();
//        return result.stream().map(objects -> {
//            Long areaId = ((BigInteger) objects[0]).longValue();
//            Integer cityCode = (Integer) objects[1];
//            String cityName = (String) objects[2];
//            return StoreInfoCityRef.builder().areaId(areaId).cityCode(cityCode).cityName(cityName).build();
//        }).collect(Collectors.toList());
//    }

    /**
     * 通过城市ID集合获取门店服务列表
     *
     * @param cityCodes 城市ID集合
     * @return
     */
    public List<Integer> findAllStoreWithServicePickupByCityCode(List<Integer> cityCodes) {
        List<Object> resultList = getEntityManager().createNamedQuery("find_store_info_with_service_pickup_by_city_code")
                .setParameter("cityCode", cityCodes)
                .getResultList();
        if (!resultList.isEmpty()) {
            return resultList.stream().map(result -> {
                return (Integer) result;
            }).collect(Collectors.toList());
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    public List<StoreInfoEntity> findAllWithMerchantIdAndPoint(Long merchantId, Double longitude, Double latitude, int distance) {
        return find("merchantId = :merchantId and st_distance_sphere(point(:longitude, :latitude), gis) < :distance",
                Parameters.with("merchantId", merchantId).and("longitude", longitude).and("latitude", latitude)
                        .and("distance", distance))
                .list();
    }
}
