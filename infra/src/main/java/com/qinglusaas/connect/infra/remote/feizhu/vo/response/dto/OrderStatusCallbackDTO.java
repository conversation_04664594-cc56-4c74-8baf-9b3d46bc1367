package com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/13, Monday
 **/
@Data
public class OrderStatusCallbackDTO {

    @JsonProperty("message_code")
    private String messageCode;
    private String message;
    @JsonProperty("request_id")
    private String requestId;

    // has is success
    public boolean isSuccess() {
        return "0".equals(this.messageCode);
    }

}
