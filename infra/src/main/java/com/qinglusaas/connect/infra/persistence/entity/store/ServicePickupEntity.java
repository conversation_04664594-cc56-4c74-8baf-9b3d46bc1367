package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.geolatte.geom.Polygon;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 门店上门取送车服务
 *
 * @TableName service_pickup
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "service_pickup")
@Where(clause = "deleted = 0")
public class ServicePickupEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店ID
     */
    @ManyToOne
    @JoinColumn(name = "store_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreInfoEntity storeInfoEntity;

    /**
     * 服务圈名称
     */
    @NotBlank(message = "[服务圈名称]不能为空")
    @Size(max = 30, message = "编码长度不能超过30")
    @Length(max = 30, message = "编码长度不能超过30")
    @Column(name = "name")
    private String name;

    /**
     * 围栏类型  1：免费接送   2：上门取送车 默认
     */
    @NotNull(message = "[围栏类型  1：免费接送   2：上门取送车")
    @Column(name = "pickup_type")
    private Integer pickupType;

    /**
     * 最小提前预定小时数，如 0、1、2、3等
     */
    @Column(name = "min_advance_booking_time")
    private Double minAdvanceBookingTime;

    /**
     * 服务费类型 0:免费(免费接送服务) 1:收费(上门送取车服务)
     */
    @NotNull(message = "[服务费类型 0:免费(免费接送服务) 1:收费(上门送取车服务)]不能为空")
    @Column(name = "fee_type")
    private Integer feeType;

    /**
     * 取送车服务收费,精确到分
     */
    @NotNull(message = "[取送车服务收费,精确到分]不能为空")
    @Column(name = "fee")
    private Integer fee;

    /**
     * 经纬度信息
     */
    @NotNull(message = "[经纬度信息]不能为空")
    @Column(name = "gis")
    private Polygon gis;

    /**
     * 围栏颜色
     */
    @NotBlank(message = "[围栏颜色]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @Length(max = 10, message = "编码长度不能超过10")
    @Column(name = "color")
    private String color;

    /**
     * 开始时间
     */
    @NotNull(message = "[开始时间]不能为空")
    @Column(name = "business_from")
    private Integer businessFrom;

    /**
     * 结束时间
     */
    @NotNull(message = "[结束时间]不能为空")
    @Column(name = "business_to")
    private Integer businessTo;

    /**
     * 是否启用 1：启用 0：不启用
     */
    @NotNull(message = "[是否启用 1：启用 0：不启用]不能为空")
    @Column(name = "enabled")
    private Integer enabled;

}