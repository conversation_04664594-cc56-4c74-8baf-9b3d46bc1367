package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.VehicleGroupDataDTO;
import lombok.Data;

import java.util.List;

/**
 * 商家车型信息推送
 * <AUTHOR>
 */
@Data
public class CompanyVehicleModelNotifyReq extends TaobaoBaseReq {

    @JsonProperty("vehicle_group_list")
    private List<VehicleGroupDataDTO> vehicleGroupList;

    public CompanyVehicleModelNotifyReq() {
        super();
        this.setMethod(APIMethod.UPDATE_MERCHANT_MODEL.getMethod());
    }
}
