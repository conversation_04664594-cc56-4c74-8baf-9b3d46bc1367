package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.ErrorCode;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@Data
public class ResultResp {

    /**
     * 平台颁发的每次请求访问的唯一标识
     */
    @JsonProperty("request_id")
    public String requestId;
    /**
     * 请求访问失败时返回的根节点
     */
    @JsonProperty("error_response")
    public ErrorResponse errorResponse;
    /**
     * 请求失败返回的错误码
     */
    public String code;
    /**
     * 请求失败返回的错误信息
     */
    public String msg;
    /**
     * 请求失败返回的子错误码
     */
    @JsonProperty("sub_code")
    public String subCode;
    /**
     * 请求失败返回的子错误信息
     */
    @JsonProperty("sub_msg")
    public String subMsg;

    /**
     * 判断请求是否成功 0表示成功
     *
     * @return
     */
    public boolean isSuccess() {
        return ErrorCode.SUCCESS.equals(this.code);
    }

}
