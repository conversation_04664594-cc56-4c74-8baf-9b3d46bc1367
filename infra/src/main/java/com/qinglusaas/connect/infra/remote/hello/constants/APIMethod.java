package com.qinglusaas.connect.infra.remote.hello.constants;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
public enum APIMethod {

    // 登录
    LOGIN("hello.open.login"),
    // 同步城市数据
    SYNC_CITIES("third.syncCities"),
    // 同步门店数据
    SYNC_STORES("third.syncStores"),
    // 同步车型数据
    SYNC_VEHICLES("third.syncVehicles"),
    // 状态回调
    CALLBACK_ORDER_STATUS("callback.orderStatus"),
    // 退款回调
    CALLBACK_REFUND("callback.refund"),
    // 扣款回调
    CALLBACK_DEDUCT("callback.deduct"),
    // 查询车型
    QUERY_VEHICLE_MODEL_LIST("queryVehicleModelList");

    private final String code;

    APIMethod(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
