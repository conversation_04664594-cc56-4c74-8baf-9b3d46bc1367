package com.qinglusaas.connect.infra.remote.ctrip.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/31, Monday
 **/
@Data
public class BackFeeDTO {

    /**
     * 退费类型。由于该节点目前仅用于退油费，所有type固定为3
     */
    private Integer type;

    /**
     * 退费金额
     * 必填
     */
    private BigDecimal amount;

    /**
     * 退费原因
     * 必填
     */
    private String reason;

}
