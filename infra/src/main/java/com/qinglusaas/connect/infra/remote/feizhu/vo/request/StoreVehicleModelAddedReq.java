package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.client.feizhu.dto.StoreVehicleModelAdditionalInfo;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7 15:14
 * 商家推送门店车型附加服务
 */
@Data
public class StoreVehicleModelAddedReq extends TaobaoBaseReq {

    /**
     * 车型基础信息
     */
    @JsonProperty("store_vehicle_model_additional_info_list")
    private List<StoreVehicleModelAdditionalInfo> storeVehicleModelAdditionalList;

    public StoreVehicleModelAddedReq() {
        super();
        super.setMethod(APIMethod.ADDED_SERVICE_NOTIFY.getMethod());
    }
}
