//package com.qinglusaas.connect.infra.persistence.hibernate.entity;
//
//import com.qinglusaas.connect.infra.persistence.hibernate.audit.CreationUser;
//import com.qinglusaas.connect.infra.persistence.hibernate.audit.UpdateUser;
//import lombok.Data;
//
//import javax.persistence.Column;
//import javax.persistence.MappedSuperclass;
//
///**
// * <AUTHOR> robintse
// * @mailto : <EMAIL>
// * @created : 2022/9/2, Friday
// **/
//@MappedSuperclass
//@Data
//public abstract class AbstractAuditableBakEntity extends AbstractEntity {
//
//    @CreationUser
//    @Column(name = "created_by", updatable = false)
//    private String createdBy;
//
//    @UpdateUser
//    @Column(name = "last_modified_by", insertable = false)
//    private String lastModifiedBy;
//
//    /**
//     * 数据更新人ID
//     */
//    @Column(name = "op_user_id", insertable = false)
//    private Integer op_user_id;
//
//}
