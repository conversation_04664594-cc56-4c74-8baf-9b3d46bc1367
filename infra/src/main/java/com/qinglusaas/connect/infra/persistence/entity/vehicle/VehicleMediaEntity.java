package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * 车型多媒体信息
 * @TableName vehicle_media
 */
@Table(name="vehicle_media")
@Entity
@Data
public class VehicleMediaEntity extends PanacheEntityBase implements Serializable {
    /**
     * 
     */
    @Id
    private Long id;

    /**
     * 车型ID
     */
    @Column(name = "vehicle_model_id")
    private Long vehicleModelId;

    /**
     * 多媒体文件路径
     */
    @Column(name = "media_path")
    private String mediaPath;

    /**
     * 文件类型 1:缩略图;2: 整车图;3:细节图;4:车辆视频
     */
    @Column(name = "media_type")
    private Integer mediaType;

    /**
     * 删除 0:否; 1:是
     */
    private Integer deleted;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    private Integer lastVer;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    private static final long serialVersionUID = 1L;
}