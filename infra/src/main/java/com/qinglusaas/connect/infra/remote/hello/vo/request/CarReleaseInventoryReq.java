package com.qinglusaas.connect.infra.remote.hello.vo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车辆库存释放请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarReleaseInventoryReq extends BaseReq {
    /**
     * 门店ID
     */
    private String siteId;
    
    /**
     * 商户ID
     */
    private String merchantId;
    
    /**
     * 占用ID列表
     */
    private List<Long> occupyIdList;

    /**
     * 临时库存占用
     */
    private Boolean temp;
} 