package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.BaseResponseDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/13 15:10
 */
@Data
public class DepositAndMileageResponse extends TaobaoResultResp {
    @JsonProperty("alitrip_rentcar_commodity_storevehmodel_base_change_notify_response")
    private BaseResponseDTO responseDTO;

    @Override
    public BaseResponseDTO responseDTO() {
        return responseDTO;
    }
}
