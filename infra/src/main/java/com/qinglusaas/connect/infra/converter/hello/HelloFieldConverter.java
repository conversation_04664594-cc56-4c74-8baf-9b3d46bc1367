package com.qinglusaas.connect.infra.converter.hello;

import com.qinglusaas.connect.infra.converter.BizFieldConverter;

import javax.inject.Singleton;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/11, Tuesday
 **/
@Singleton
public class HelloFieldConverter implements BizFieldConverter {

    @Override
    public Integer storeTypeCovert(Integer originType) {
        // hello 1-实体店；2-服务点;
        // 业务  0:实体门店;1:服务网点
        switch (originType) {
            case 1:
                return 2;
            case 0:
            default:
                return 1;
        }
    }

    @Override
    public Integer storeStatusConvert(Integer status) {
        // hello 门店状态 1:启用，2:禁用
        // 业务 门店状态 0:已关闭; 1:营业中
        switch (status) {
            case 1:
                return 1;
            case 0:
            default:
                return 2;
        }
    }

    @Override
    public Integer stationTypeConvert(Integer stationType) {
        return null;
    }

    @Override
    public Integer feeTypeConvert(Integer feeType) {
        return null;
    }

    @Override
    public Integer vehicleStatusConvert(String ctripCarStatus) {
        return null;
    }

    @Override
    public Integer vehicleAuditStatusConvert(String ctripCarStatus) {
        return null;
    }

    @Override
    public Integer vehicleSaleStatusConvert(String ctripSaleStatus) {
        return null;
    }

    public String allopatryReturnEnabled(Integer allopatryReturnEnabled) {
        switch (allopatryReturnEnabled) {
            case 1:
                return "Y";
            case 0:
            default:
                return "N";
        }
    }

    /**
     * 1, "京牌"
     * 2, "沪牌"
     * 3, "粤A牌"
     * 4, "深牌"
     * 5, "浙A牌"
     * 6, "甘牌"
     * 7, "粤B牌"
     * 8, "非京牌"
     * 9, "非深牌"
     * 10, "非甘牌"
     * 11, "其他"
     */
    public Integer licenceTypeConvert(String licenceType) {
        if (licenceType.startsWith("京")) {
            return 1;
        } else if (licenceType.startsWith("沪")) {
            return 2;
        } else if (licenceType.startsWith("粤A")) {
            return 3;
        } else if (licenceType.startsWith("深")) {
            return 4;
        } else if (licenceType.startsWith("浙A") || licenceType.startsWith("杭牌") || licenceType.startsWith("浙M")) {
            return 5;
        } else if (licenceType.startsWith("甘")) {
            return 6;
        } else if (licenceType.startsWith("粤B")) {
            return 7;
        } else {
            return 11;
        }
    }


}
