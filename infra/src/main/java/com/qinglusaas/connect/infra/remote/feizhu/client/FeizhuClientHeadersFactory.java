package com.qinglusaas.connect.infra.remote.feizhu.client;

import org.eclipse.microprofile.rest.client.ext.ClientHeadersFactory;

import javax.ws.rs.core.MultivaluedMap;

/**
 * <AUTHOR>
 */
public class FeizhuClientHeadersFactory implements ClientHeadersFactory  {

    @Override
    public MultivaluedMap<String, String> update(MultivaluedMap<String, String> multivaluedMap,
                                                 MultivaluedMap<String, String> multivaluedMap1) {
        multivaluedMap1.putSingle("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        return null;
    }
}
