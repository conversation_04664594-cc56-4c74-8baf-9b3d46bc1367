package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class StoreVehicleChangeNotifyReq extends TaobaoBaseReq {


    /**
     * 门店车辆信息
     */
    @JsonProperty("store_vehicle_info_list")
    private List<StoreVehicleNotifyReq> storeVehicleInfoList;

    public StoreVehicleChangeNotifyReq() {
        super();
        super.setMethod(APIMethod.UPDATE_MERCHANT_VEHICLE.getMethod());
    }

}
