package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * 
 * @TableName license_type
 */
@Table(name="license_type")
@Data
@Entity
public class LicenseTypeEntity extends PanacheEntityBase implements Serializable {
    /**
     * 
     */
    @Id
    private Long id;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 牌照类型名称;eg:沪A
     */
    @Column(name = "license_type_name")
    private String licenseTypeName;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private Long cityId;

    /**
     * 是否预设 0:否 1:是
     */
    private Integer preset;

    /**
     * 0:正常;1:删除
     */
    private Integer deleted;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    private Integer lastVer;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    private static final long serialVersionUID = 1L;
}