package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.client.feizhu.dto.CityInfo;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

@Data
public class CityChangeNotifyReq extends TaobaoBaseReq {

    /**
     * 渠道号
     */
    @JsonProperty("provider_id")
    private String providerId;

    /**
     * 城市信息集合
     */
    @JsonProperty("city_info_list")
    private List<CityInfo> cityInfoList;

    public CityChangeNotifyReq() {
        super();
        this.setMethod(APIMethod.CITY_CHANGE_NOTIFY.getMethod());
    }
}
