package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.ErrorCode;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.BaseResponseDTO;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/13, Monday
 **/
@Data
public class StoreChangeNotifyResponse extends TaobaoResultResp {

    @JsonProperty("alitrip_rentcar_commodity_store_change_notify_response")
    private BaseResponseDTO responseDTO;

    @Override
    public BaseResponseDTO responseDTO() {
        return responseDTO;
    }
}
