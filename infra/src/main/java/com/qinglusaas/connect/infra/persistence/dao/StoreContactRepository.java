package com.qinglusaas.connect.infra.persistence.dao;

import com.qinglusaas.connect.infra.persistence.entity.store.StoreContactEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/10/10, Monday
 **/
@ApplicationScoped
public class StoreContactRepository extends AbstractPanacheRepository<StoreContactEntity, Long> {

    public List<StoreContactEntity> findByStoreIds(Iterable<Long> storeInfoIds) {
        return find("storeInfoEntity.id in :storeInfoIds and deleted = :deleted",
                Parameters.with("storeInfoIds", storeInfoIds).and("deleted", 0)
        ).list();
    }
}
