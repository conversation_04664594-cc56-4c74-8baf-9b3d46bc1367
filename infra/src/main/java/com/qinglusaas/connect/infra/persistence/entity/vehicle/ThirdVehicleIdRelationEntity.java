package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;

/**
 * 第三方车辆ID关联表实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "third_vehicle_id_relation")
@Where(clause = "deleted = 0")
public class ThirdVehicleIdRelationEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商户ID
     */
    @Column(name = "merchant_id", nullable = false)
    private Long merchantId;

    /**
     * 门店ID
     */
    @Column(name = "store_id", nullable = false)
    private Long storeId;

    /**
     * 类型
     */
    @Column(name = "type", nullable = false)
    private Byte type;

    /**
     * 渠道ID
     */
    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    /**
     * 第三方ID
     */
    @Column(name = "third_id", nullable = false, length = 50)
    private String thirdId;

    /**
     * 内部ID
     */
    @Column(name = "saas_id", nullable = false)
    private Long saasId;

    /**
     * 删除标志
     */
    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time", nullable = false)
    private Long opTime;

    private static final long serialVersionUID = 1L;

    @PrePersist
    protected void onCreate() {
        this.createTime = Calendar.getInstance().getTimeInMillis();
        this.opTime = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.opTime = Calendar.getInstance().getTimeInMillis();
    }

} 