package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.ErrorCode;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.BaseResponseDTO;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.dto.ErrorResponseDTO;

/**
 * <AUTHOR>
 */
public abstract class TaobaoResultResp {

    public abstract BaseResponseDTO responseDTO();

    @JsonProperty("error_response")
    private ErrorResponseDTO errorResponse;

    public boolean isSuccess() {
        BaseResponseDTO responseDTO = responseDTO();
        return responseDTO != null && null != responseDTO.getMessageCode() && ErrorCode.SUCCESS.equals(responseDTO.getMessageCode());
    }
}
