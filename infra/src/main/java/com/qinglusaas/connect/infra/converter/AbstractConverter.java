package com.qinglusaas.connect.infra.converter;

import com.google.common.base.Converter;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/9/27 15:01
 */
public abstract class AbstractConverter<A, B> extends Converter<A, B> {

    @Override
    protected B doForward(A a) {
        throw new UnsupportedOperationException();
    }

    @Override
    protected A doBackward(B b) {
        throw new UnsupportedOperationException();
    }

}
