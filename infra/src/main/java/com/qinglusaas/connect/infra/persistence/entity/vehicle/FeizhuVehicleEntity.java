package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 飞猪车系表
 *
 * @TableName feizhu_vehicle
 */
@Table(name = "feizhu_vehicle")
@Entity
@Data
public class FeizhuVehicleEntity extends PanacheEntityBase implements Serializable {
    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * feizhu id
     */
    @Column(name = "feizhu_id")
    private String feizhuId;

    /**
     * 车辆品牌
     */
    @Column(name = "brand_name")
    private String brandName;

    /**
     * 车系名称
     */
    @Column(name = "vehicle_sery")
    private String vehicleSery;

    /**
     * 展示名称
     */
    @Column(name = "show_name")
    private String showName;

    /**
     * 排量
     */
    private String displacement;

    /**
     * 排量类型: 1t; 2l
     */
    @Column(name = "displacement_type")
    private Integer displacementType;

    /**
     * 档位类型  1手动;2自动
     */
    @Column(name = "transmission_type")
    private Integer transmissionType;

    /**
     * 门数
     */
    @Column(name = "door_num")
    private Integer doorNum;

    /**
     * 座位数
     */
    @Column(name = "seat_num")
    private Integer seatNum;

    /**
     * 年款
     */
    private String year;

    /**
     * 是否混动:0否 1是
     */
    @Column(name = "is_hybrid")
    private Integer isHybrid;

    /**
     * 是否进口:0否 1是
     */
    @Column(name = "is_import")
    private Integer isImport;

    /**
     * 是否敞篷:0否 1是
     */
    @Column(name = "is_convertible")
    private Integer isConvertible;

    /**
     * 车辆类型:0-其他 3-SUV 5-跑车 9-舒适型 10-经济型 6-商务车 11-豪华型 12-房车 13-皮卡 14-小巴士
     */
    @Column(name = "vehicle_type")
    private Integer vehicleType;

    /**
     * 车辆类型
     */
    @Column(name = "car_tag")
    private String carTag;
}