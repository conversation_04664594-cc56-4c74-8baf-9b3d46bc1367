package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

@Data
public class StoreChangeNotifyReq extends TaobaoBaseReq {
    /**
     * 渠道号
     */
    @JsonProperty("provider_id")
    private String providerId;

    /**
     * 额外字段
     */
    @JsonProperty("extra")
    private String extra;

    /**
     * 门店信息列表
     */
    @JsonProperty("store_info_list")
    private List<StoreDetailInfo> storeInfoList;

    public StoreChangeNotifyReq() {
        super();
        super.setMethod(APIMethod.STORE_CHANGE_NOTIFY.getMethod());
    }

    @Data
    public static class StoreDetailInfo {
        /**
         * 租赁间隔时间，单位小时，整数
         */
        @JsonProperty("rent_interval_time")
        private Integer rentIntervalTime;

        /**
         * 门店类型, 1:门店 2:提车点
         */
        @JsonProperty("store_type")
        private Integer storeType;

        /**
         * 门店对应的公司Code(针对自营商家不读取此字段)
         */
        @JsonProperty("company_code")
        private String companyCode;

        /**
         * 供应商父门店code, 仅当storeType不为1时有效
         */
        @JsonProperty("parent_store_code")
        private String parentStoreCode;

        /**
         * 门店地址
         */
        @JsonProperty("address")
        private String address;

        /**
         * 门店纬度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("latitude")
        private String latitude;

        /**
         * 门店是否有效，true为有效，false表示无效，商家确保将所有门店数据同步给平台（有效，无效）
         */
        @JsonProperty("is_active")
        private Boolean isActive;

        /**
         * 门店所在市的供应商cityCode
         */
        @JsonProperty("city_code")
        private String cityCode;

        /**
         * 最大租赁时间，单位小时，整数
         */
        @JsonProperty("max_lease_time")
        private Integer maxLeaseTime;

        /**
         * 最小租赁时间，单位小时，整数
         */
        @JsonProperty("min_lease_time")
        private Integer minLeaseTime;

        /**
         * 门店营业时间，不可为空
         */
        @JsonProperty("store_open_time_list")
        private List<StoreOpenTime> storeOpenTimeList;

        /**
         * 门店联系电话，11位整数
         */
        @JsonProperty("phone")
        private String phone;

        /**
         * 供应商门店Code,保证唯一
         */
        @JsonProperty("store_code")
        private String storeCode;

        /**
         * 门店联系人
         */
        @JsonProperty("contact")
        private String contact;

        /**
         * 门店最短提前预定时间(单位:小时,可传小数如：0.5)
         */
        @JsonProperty("advance_booking_hour")
        private String advanceBookingHour;

        /**
         * 门店名称
         */
        @JsonProperty("store_name")
        private String storeName;

        /**
         * 门店非营业时间,如果为空则表示无非营业时间
         */
        @JsonProperty("store_close_time_list")
        private List<StoreCloseTime> storeCloseTimeList;

        /**
         * 门店经度，依照高德经纬度格式，精确到小数点后5位即可
         */
        @JsonProperty("longitude")
        private String longitude;

        /**
         * 异地还车配置
         */
        @JsonProperty("diff_store_info_list")
        private List<DiffStoreInfo> diffStoreInfoList;


        // 门店营业时间
        @Data
        public static class StoreOpenTime {
            /**
             * 营业时间段总价格 精确到2位小数;单位:元
             */
            @JsonProperty("charge_fee")
            private String chargeFee;

            /**
             * 表示在每周的星期几生效,周一到周日依次为1...7
             */
            @JsonProperty("effect_week")
            private List<String> effectWeek;

            /**
             * 收费方式  1:收费 2:免费
             */
            @JsonProperty("charge_type")
            private Integer chargeType;

            /**
             * 开始时间,格式为HH:mm
             */
            @JsonProperty("start_time")
            private String startTime;

            /**
             * 结束时间,格式为HH:mm
             */
            @JsonProperty("end_time")
            private String endTime;
        }

        // 门店非营业时间
        @Data
        public static class StoreCloseTime {
            /**
             * 停止营业时间开始时间,格式为yyyy-MM-dd HH:mm:ss
             */
            @JsonProperty("start_time")
            private String startTime;

            /**
             * 停止营业时间结束时间,格式为yyyy-MM-dd HH:mm:ss
             */
            @JsonProperty("end_time")
            private String endTime;
        }

        // 异地还车配置
        @Data
        public static class DiffStoreInfo {
            /**
             * 异地还车门店Code(当前门店可还车到的异地门店Code)
             */
            @JsonProperty("diff_store_code")
            private String diffStoreCode;

            /**
             * 车辆归属类型，1-归属到当前门店，2-归属到异地门店
             */
            @JsonProperty("diff_store_owner_type")
            private Integer diffStoreOwnerType;

            /**
             * 是否激活，true激活，false不激活
             */
            @JsonProperty("is_active")
            private Boolean isActive;

            /**
             * 异地还车后的租赁间隔，单位小时，整数
             */
            @JsonProperty("diff_store_interval_time")
            private Integer diffStoreIntervalTime;
        }
    }
}
