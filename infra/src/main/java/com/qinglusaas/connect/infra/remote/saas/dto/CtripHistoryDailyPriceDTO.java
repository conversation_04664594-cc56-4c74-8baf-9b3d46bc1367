package com.qinglusaas.connect.infra.remote.saas.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @auther musi
 * @date 2023/7/25 17:26
 */
@Data
public class CtripHistoryDailyPriceDTO implements Serializable {

    /**
     * 日期
     */
    private String date;

    /**
     * 整日价格，分
     */
    private Integer wholeDailyPrice;

    /**
     * 零散小时费，分
     */
    private Integer partDailyPrice;

    /**
     * 整日OR零散小时
     */
    private BigDecimal quantity;

    /**
     * 零散比例，回传使用
     */
    private BigDecimal rate;
}
