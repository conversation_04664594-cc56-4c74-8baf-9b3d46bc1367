package com.qinglusaas.connect.infra.converter.feizhu;

import com.qinglusaas.connect.infra.converter.AbstractConverter;
import com.qinglusaas.connect.infra.remote.saas.contants.BizErrorCode;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/2, Thursday
 **/
public class BizCodeErrorConverter extends AbstractConverter<BizErrorCode, com.qinglusaas.connect.client.feizhu.error.BizErrorCode> {

    @Override
    protected com.qinglusaas.connect.client.feizhu.error.BizErrorCode doForward(BizErrorCode bizErrorCode) {
        switch (bizErrorCode) {
            case STOCK_OUT:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.STOCK_OUT;
            case PRICE_CHANGE:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.PRICE_CHANGE;
            case CREATE_ORDER_FAIL:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.CREATE_ORDER_FAIL;
            case DRIVER_EXISTED_ORDER:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.DRIVER_EXISTED_ORDER;
            case DRIVER_SECURITY_VALIDATE_FAIL:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.DRIVER_SECURITY_VALIDATE_FAIL;
            default:
                return com.qinglusaas.connect.client.feizhu.error.BizErrorCode.UNKNOWN;
        }

    }
}
