package com.qinglusaas.connect.infra.exception;

import com.qinglusaas.connect.client.common.error.ClientName;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

import static java.util.Collections.singletonList;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/8/26, Friday
 **/
@Data
public class BizException extends RuntimeException {

    private final String code;
    private final List<String> messages;
    private String subCode;
    private String subMsg;
    private final ClientName clientName;

    public BizException(ClientName clientName, String code, String message) {
        super(message);
        this.code = code;
        this.messages = new LinkedList<>(singletonList(message));
        this.clientName = clientName;
    }

    public BizException(ClientName clientName, String code, String message, String subCode, String subMsg) {
        super(message);
        this.code = code;
        this.messages = new LinkedList<>(singletonList(message));
        this.clientName = clientName;
        this.subCode = subCode;
        this.subMsg = subMsg;
    }

    public BizException(ClientName clientName, String code, List<String> messages) {
        super(String.join(", ", messages));
        this.code = code;
        this.messages = messages;
        this.clientName = clientName;
    }
}
