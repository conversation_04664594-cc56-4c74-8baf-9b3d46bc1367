package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.client.feizhu.dto.StoreRangeInfo;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

@Data
public class StoreRangeChangeNotifyReq extends TaobaoBaseReq {

    @JsonProperty("store_service_list")
    private List<StoreRangeInfo> storeServiceList;

    public StoreRangeChangeNotifyReq() {
        super();
        super.setMethod(APIMethod.STORE_RANGE_CHANGE_NOTIFY.getMethod());
    }
}
