package com.qinglusaas.connect.infra.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/1/10, Tuesday
 **/
@Getter
@AllArgsConstructor
public enum ApiConnEntityStatusEnum {

    NONE(-1, "初始"),
    APPLYING(0, "申请中"),
    APPLY_FAILED(1, "申请失败"),
    CLOSED_AUTHORIZATION(2, "关闭授权"),
    OPEN_AUTHORIZATION(3, "开启授权");

    private Integer status;
    private String desc;

    @JsonValue
    public Integer getStatus() {
        return status;
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ApiConnEntityStatusEnum fromCode(Integer status) {
        for (ApiConnEntityStatusEnum r : ApiConnEntityStatusEnum.values()) {
            if (r.status.equals(status)) {
                return r;
            }
        }
        return NONE;
    }

    @Override
    public String toString() {
        return status.toString();
    }


}
