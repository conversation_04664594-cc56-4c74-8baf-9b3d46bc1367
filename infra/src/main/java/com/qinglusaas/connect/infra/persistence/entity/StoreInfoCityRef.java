package com.qinglusaas.connect.infra.persistence.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/21, Wednesday
 **/
//@NamedNativeQueries({
//        @NamedNativeQuery(name = "find_store_info_with_area_by_id",
//                query =
//                        "SELECT " +
//                                "a.name as cityName, " +
//                                "s.city_code as cityCode, " +
//                                "a.id as areaId " +
//                                "FROM area as a, " +
//                                "(SELECT s.city_code FROM store_info as s where s.merchant_id = :merchantId and deleted = 0 and is_test=0 group by s.city_code) as s " +
//                                "WHERE s.city_code = a.code",
//                resultSetMapping = "store_info_with_area_by_id"
//        )
//})
//@SqlResultSetMapping(
//        name = "store_info_with_area_by_id",
//        entities = {
//                @EntityResult(
//                        entityClass = StoreInfoCityRef.class,
//                        fields = {
//                                @FieldResult(name = "areaId", column = "areaId"),
//                                @FieldResult(name = "cityCode", column = "cityCode"),
//                                @FieldResult(name = "cityName", column = "cityName"),
//                        }
//                )
//        }
//)
//@Entity
@Data
@Builder
public class StoreInfoCityRef {

    private Long areaId;
    private Integer cityCode;
    private String cityName;

}
