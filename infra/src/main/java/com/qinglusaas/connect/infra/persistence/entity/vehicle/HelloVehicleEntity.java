package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 哈罗车款库表
 *
 * @TableName hello_vehicle
 */
@Table(name = "hello_vehicle")
@Data
@Entity
public class HelloVehicleEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 哈罗车款code
     */
    private String code;

    /**
     * 车辆品牌
     */
    private String brand;

    /**
     * 车系名称
     */
    @Column(name = "vehicle_sery")
    private String vehicleSery;

    /**
     * 车款名称
     */
    @Column(name = "vehicle_model_name")
    private String vehicleModelName;

    /**
     * 车辆年款
     */
    @Column(name = "vehicle_year_style")
    private String vehicleYearStyle;

    /**
     * 排量
     */
    private String displacement;

    /**
     * 变速箱
     */
    private String gearbox;

    /**
     * 燃油形式
     */
    @Column(name = "fuel_form")
    private String fuelForm;

    /**
     * 座位数
     */
    @Column(name = "seat_num")
    private Integer seatNum;

    /**
     * 车门数
     */
    private Integer doors;

}