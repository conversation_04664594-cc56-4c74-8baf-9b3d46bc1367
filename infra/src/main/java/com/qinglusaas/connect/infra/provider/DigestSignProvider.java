package com.qinglusaas.connect.infra.provider;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.enterprise.context.ApplicationScoped;

import org.apache.commons.codec.digest.DigestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.qinglusaas.connect.infra.remote.etc.util.EtcSignUtils;
import com.qinglusaas.connect.infra.util.FeizhuSpiUtils;
import com.qinglusaas.connect.infra.web.constant.SignatureConstant;
import com.qinglusaas.connect.infra.web.exception.SignatureException;
import com.qinglusaas.connect.infra.web.provider.SignProvider;
import com.qinglusaas.connect.infra.web.security.bean.SignContext;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/13, Tuesday
 **/
@ApplicationScoped
public class DigestSignProvider implements SignProvider {

    private static final ObjectMapper om = new ObjectMapper();

    static {
        om.disable(SerializationFeature.INDENT_OUTPUT);
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Override
    public SignContext verifySaasSignature(String appKey, String appSecret, String timestamp, String sign) {
        String genSign = DigestUtils.md5Hex(appKey + appSecret + timestamp);
        SignContext signContext = SignContext.builder().appKey(appKey).appSecret(appSecret).timestamp(timestamp).build();
        // verify sign has ture;
        signContext.setVerified(genSign.equals(sign));
        return signContext;
    }

    /**
     * 提供给携程平台使用
     *
     * @param appKey
     * @param appSecret
     * @param timestamp
     * @param sign
     * @return
     */
    @Override
    public SignContext verifyCtripSignature(String appKey, String appSecret, String timestamp, String sign) {
        // data (payload) | encryptionKey (appKey + appSecret + timestamp) = signature
        String genSign = DigestUtils.md5Hex(appKey + appSecret + timestamp);
//        String encrypt = AESUtil.encrypt(payload, encryptionKey.toString(), encryptionIv);
        SignContext signContext = SignContext.builder().appKey(appKey).appSecret(appSecret).timestamp(timestamp).build();
        // verify sign has ture;
        signContext.setVerified(genSign.equals(sign));
        return signContext;
    }

    /**
     * 提供给哈喽平台使用
     *
     * @param map
     * @param appSecret
     * @param sign
     * @return
     */
    public SignContext verifyHelloSignature(Map<String, Object> map, String appSecret, String sign) {
        String timestamp = (String) map.get(SignatureConstant.TIMESTAMP);
        String appKey = (String) map.get(SignatureConstant.APP_KEY);
        // data (payload) | encryptionKey (appKey + appSecret + timestamp) = signature
        String genSign = signHelloRequest(map, appSecret);
        SignContext signContext = SignContext.builder().appKey(appKey).appSecret(appSecret).timestamp(timestamp).build();
        // verify sign has ture;
        signContext.setVerified(genSign.equals(sign));
        return signContext;
    }

    /**
     * 提供给飞猪平台使用
     *
     * @param formMap
     * @param body
     * @param appSecret
     * @param charset
     * @return
     */
    public SignContext verifyFeizhuSignature(Map<String, String> formMap, String body, String appKey, String appSecret, String timestamp, String charset) {
        boolean result = FeizhuSpiUtils.checkSignInternal(formMap, body, appSecret, charset);
        SignContext signContext = SignContext.builder().appKey(appKey).appSecret(appSecret).timestamp(timestamp).build();
        // verify sign has ture;
        signContext.setVerified(result);
        return signContext;
    }

    @Override
    public SignContext verifyEtcSignature(Map<String, Object> map, String appSecret, String signature) {
        String merchantCode = (String) map.get(SignatureConstant.MERCHANT_CODE);
        String timestamp = (String) map.get(SignatureConstant.TIMESTAMP);
        String data = (String) map.get(SignatureConstant.DATA);
        String sign = EtcSignUtils.sign(merchantCode, appSecret, timestamp, data);
        SignContext signContext = SignContext.builder().appSecret(appSecret).timestamp(timestamp).build();
        signContext.setVerified(sign.equals(signature));
        return signContext;
    }

    /**
     * 提供给摩捷平台使用
     *
     * @param map
     * @param appSecret
     * @param sign
     * @return
     */
    @Override
    public SignContext verifyMojSignature(Map<String, Object> map, String appSecret, String sign) {
        String timestamp = (String) map.get(SignatureConstant.TIMESTAMP);
        String appKey = (String) map.get(SignatureConstant.APP_KEY);
        
        try {
            String genSign = signMojRequest(map, appSecret);
            SignContext signContext = SignContext.builder().appKey(appKey).appSecret(appSecret).timestamp(timestamp).build();
            // verify sign has ture;
            signContext.setVerified(genSign.equals(sign));
            return signContext;
        } catch (IOException e) {
            throw new SignatureException(e);
        }
    }

    public static String signHelloRequest(Map<String, Object> params, String secret) {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            if (null != key && key.trim().length() > 0) {
                Object value = params.get(key);
                if (null != value) {
                    query.append(key.trim());
                    try {
                        if (value instanceof List || value instanceof Map) {
                            String json = om.writeValueAsString(value);
                            query.append(json.trim());
                        } else {
                            query.append(String.valueOf(value).trim());
                        }
                    } catch (JsonProcessingException e) {
                        throw new SignatureException(e);
                    }

                }
            }
        }
        query.append(secret);
        return DigestUtils.md5Hex(query.toString());
    }

    /**
     * 摩捷签名算法
     * 使用<code>secret</code>对paramValues按以下算法进行签名： <br/>
     * uppercase(hex(sha1(secretkey1value1key2value2...secret))
     *
     * @param paramValues 参数列表
     * @param secret
     * @return
     */
    public static String signMojRequest(Map<String, Object> paramValues, String secret) throws IOException {
        List<String> ignoreParamNames = Arrays.asList(SignatureConstant.SIGN);
        return signMojRequest(paramValues, ignoreParamNames, secret);
    }

    /**
     * 对paramValues进行签名，其中ignoreParamNames这些参数不参与签名
     * @param paramValues
     * @param ignoreParamNames
     * @param secret
     * @return
     */
    public static String signMojRequest(Map<String, Object> paramValues, List<String> ignoreParamNames, String secret) throws IOException {
        StringBuilder sb = new StringBuilder();
        List<String> paramNames = new ArrayList<String>(paramValues.size());
        paramNames.addAll(paramValues.keySet());
        if(ignoreParamNames != null && ignoreParamNames.size() > 0){
            for (String ignoreParamName : ignoreParamNames) {
                paramNames.remove(ignoreParamName);
            }
        }
        Collections.sort(paramNames);

        sb.append(secret);
        for (String paramName : paramNames) {
            sb.append(paramName).append(paramValues.get(paramName));
        }
        sb.append(secret);
        byte[] sha1Digest = getSHA1Digest(sb.toString());
        return byte2hex(sha1Digest);
    }

    private static byte[] getSHA1Digest(String data) throws IOException {
        byte[] bytes = null;
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            bytes = md.digest(data.getBytes("UTF-8"));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse.getMessage());
        }
        return bytes;
    }

    /**
     * 二进制转十六进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

}
