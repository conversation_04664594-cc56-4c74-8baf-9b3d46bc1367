package com.qinglusaas.connect.infra.converter.saas;

import com.qinglusaas.connect.client.hello.constants.CreditSupport;
import com.qinglusaas.connect.client.saas.constants.FreeDepositDegree;

import javax.inject.Singleton;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/12/8, Thursday
 **/
@Singleton
public class SaasFieldConverter {

    public Integer helloPickUpDropOffTypeCovert(Integer pickUpDropOffType) {
        // hello 1:免费接送 ，2:上门取还 3 :门店自取
        // 业务   1：到店取车，2：上门送车，3：免费接送
        switch (pickUpDropOffType) {
            case 1:
                return 3;
            case 2:
                return 2;
            default:
                return 1;
        }
    }

    public FreeDepositDegree helloFreeDepositDegreeCovert(CreditSupport creditSupport) {
        // hello 0:不支持,1:免车辆押金,2:免违章押金,3:双免
        // 业务   1：不支持；2：免租车押；3：全免押金；4：免违章押金
        if (null == creditSupport) return FreeDepositDegree.NO_SUPPORT;
        switch (creditSupport.getCode()) {
            case 1:
                return FreeDepositDegree.FREE_RENT_DEPOSIT;
            case 2:
                return FreeDepositDegree.FREE_VIOLATE_DEPOSIT;
            case 3:
                return FreeDepositDegree.FULL_FREE_DEPOSIT;
            default:
                return FreeDepositDegree.NO_SUPPORT;
        }
    }

    public Integer mojPickUpDropOffTypeCovert(Integer pickUpDropOffType) {
        // moj 1:免费接送 ，2:上门取还 3 :门店自取
        // 业务   1：到店取车，2：上门送车，3：免费接送
        switch (pickUpDropOffType) {
            case 1:
                return 3;
            case 2:
                return 2;
            default:
                return 1;
        }
    }

    public FreeDepositDegree mojFreeDepositDegreeCovert(com.qinglusaas.connect.client.moj.constants.CreditSupport creditSupport) {
        // moj 0:不支持,1:免车辆押金,2:免违章押金,3:双免
        // 业务   1：不支持；2：免租车押；3：全免押金；4：免违章押金
        if (null == creditSupport) return FreeDepositDegree.NO_SUPPORT;
        switch (creditSupport.getCode()) {
            case 1:
                return FreeDepositDegree.FREE_RENT_DEPOSIT;
            case 2:
                return FreeDepositDegree.FREE_VIOLATE_DEPOSIT;
            case 3:
                return FreeDepositDegree.FULL_FREE_DEPOSIT;
            default:
                return FreeDepositDegree.NO_SUPPORT;
        }
    }

}
