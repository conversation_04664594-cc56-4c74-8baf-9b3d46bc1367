package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 门店夜间服务费
 *
 * @TableName night_service
 */
@Table(name = "night_service")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Where(clause = "deleted = 0")
public class NightServiceEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 营业时间主键ID
     */
    @ManyToOne
    @JoinColumn(name = "business_time_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private BusinessTimeEntity businessTimeEntity;

    /**
     * 营业周期 周一-周日: 1111111;周一:1000000;周日:0000001;周六:0000011
     */
    @Column(name = "business_period")
    private String businessPeriod;

    /**
     * 开始时间
     */
    @Column(name = "business_from")
    private Integer businessFrom;

    /**
     * 结束时间
     */
    @Column(name = "business_to")
    private Integer businessTo;

    /**
     * 收费金额,精确到分
     */
    private Integer fee;

    /**
     * 收费类型 目前默认每次 1：每次
     */
    @Column(name = "fee_type")
    private Integer feeType;

    /**
     * 排序
     */
    @Column(name = "sort_no")
    private Integer sortNo;


}