package com.qinglusaas.connect.infra.remote.hello.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存占用类型枚举
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum OccupyTypeEnum {

    /**
     * 订单占用类型
     */
    ORDER_TYPE_10(10, "订单占用类型10"),
    ORDER_TYPE_11(11, "订单占用类型11"),
    ORDER_TYPE_12(12, "订单占用类型12"),
    
    /**
     * 临时占用类型 - 需要设置temp=true
     */
    TEMP_OCCUPY_TYPE(29, "临时占用类型");

    private final Integer code;
    private final String desc;

    /**
     * 判断是否为订单占用类型
     */
    public static boolean isOrderOccupyType(Integer occupyType) {
        return occupyType != null && 
               (occupyType.equals(ORDER_TYPE_10.getCode()) || 
                occupyType.equals(ORDER_TYPE_11.getCode()) || 
                occupyType.equals(ORDER_TYPE_12.getCode()));
    }

    /**
     * 判断是否为临时占用类型（需要设置temp=true）
     */
    public static boolean isTempOccupyType(Integer occupyType) {
        return occupyType != null && occupyType.equals(TEMP_OCCUPY_TYPE.getCode());
    }

    /**
     * 根据code获取枚举
     */
    public static OccupyTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OccupyTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
