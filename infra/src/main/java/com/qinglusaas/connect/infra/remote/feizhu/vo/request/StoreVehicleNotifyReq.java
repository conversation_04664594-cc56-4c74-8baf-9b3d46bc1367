package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;

/**
 * 飞猪车辆信息
 * <AUTHOR>
 */
@Data
public class StoreVehicleNotifyReq {


    /**
     * 必传，供应商车型组Code
     */
    @JsonProperty(value = "vehicle_model_code", access = READ_ONLY)
    private String vehicleModelCode;

    /**
     * 必传，车辆当前里程数，整数，单位km
     */
    @JsonProperty(value = "current_mileage", access = READ_ONLY)
    private Integer currentMileage;

    /**
     * 必填 校验合格证
     */
    @JsonProperty(value = "inspection_certificate", access = READ_ONLY)
    private InspectionCertificate inspectionCertificate;

    /**
     * 必填 行驶证信息
     */
    @JsonProperty(value = "vehicle_registration_certificate", access = READ_ONLY)
    private VehicleRegistrationCertificate vehicleRegistrationCertificate;

    /**
     * 是否支持异地还车
     */
    @JsonProperty(value = "diff_store", access = READ_ONLY)
    private Boolean diffStore;

    /**
     * 供应商门店code
     */
    @JsonProperty(value = "store_code", access = READ_ONLY)
    private String storeCode;

    /**
     * 必填 保险信息
     */
    @JsonProperty(value = "insurance_info_list", access = READ_ONLY)
    private List<InsuranceInfo> insuranceInfoList;

    /**
     * 必填 供应商车辆code
     */
    @JsonProperty(value = "vehicle_code", access = READ_ONLY)
    private String vehicleCode;

    /**
     * 必填，车辆状态 1-上架，2-下架
     */
    @JsonProperty("status")
    private Integer status;



    /**
     * 校验合格证
     */
    @Data
    public static class InspectionCertificate {

        /**
         * 年检到期日期，格式yyyy-MM-dd
         * 必填
         */
        @JsonProperty(value = "inspection_expiration_date", access = READ_ONLY)
        private String inspectionExpirationDate;
    }

    /**
     * 行驶证信息
     */
    @Data
    public static class VehicleRegistrationCertificate {
        @JsonProperty(value = "registration_number", access = READ_ONLY)
        private String registrationNumber;

        @JsonProperty(value = "engine_number", access = READ_ONLY)
        private String engineNumber;

        @JsonProperty(value = "registration_date", access = READ_ONLY)
        private String registrationDate;

        /**
         * 各地级市字母代码
         */
        @JsonProperty(value = "ascription_city", access = READ_ONLY)
        private String ascriptionCity;

        /**
         * 车架号
         */
        private String vin;

        /**
         * 车牌号省的简称
         */
        @JsonProperty(value = "ascription_country", access = READ_ONLY)
        private String ascriptionCountry;
    }

    @Data
    public static class InsuranceInfo {

        /**
         * 保险单号
         */
        @JsonProperty(value = "insurance_number", access = READ_ONLY)
        private String insuranceNumber;

        /**
         * 保险到期日期，格式yyyy-MM-dd
         */
        @JsonProperty(value = "insurance_expiration_date", access = READ_ONLY)
        private String insuranceExpirationDate;

        /**
         * 保险类型 1-商业保险单，2-强险保险单
         */
        @JsonProperty(value = "insurance_type", access = READ_ONLY)
        private Integer insuranceType;

        /**
         * 投保使用的证件号
         */
        @JsonProperty(value = "insurance_identity_card", access = READ_ONLY)
        private String insuranceIdentityCard;
    }
}
