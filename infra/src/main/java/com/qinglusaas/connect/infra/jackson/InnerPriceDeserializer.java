package com.qinglusaas.connect.infra.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.qinglusaas.connect.client.common.dto.InnerPrice;

import java.io.IOException;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/12/7, Wednesday
 **/
public class InnerPriceDeserializer extends JsonDeserializer<InnerPrice> {

    @Override
    public InnerPrice deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
        return new InnerPrice(p.getValueAsInt());
    }

}
