package com.qinglusaas.connect.infra.persistence.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商家&渠道绑定表
 *
 * @TableName api_conn
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "api_conn")
public class ApiConnEntity extends AbstractEntity implements Serializable {

    /**
     * 商家ID
     */
    @NotNull(message = "[商家ID]不能为空")
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 渠道ID
     */
    @NotNull(message = "[渠道ID]不能为空")
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 携程渠道sharedKey
     */
    @Column(name = "channel_shared_key")
    private String channelSharedKey;

    /**
     * 携程渠道vendorCode
     */
    @Column(name = "channel_vendor_code")
    private String channelVendorCode;


    /**
     * 渠道appkey
     */
    @Column(name = "appkey")
    private String appKey;

    /**
     * 渠道appsecret
     */
    @Column(name = "appsecret")
    private String appSecret;

    /**
     * 渠道联调url
     */
    @Column(name = "channel_url")
    private String channelUrl;

    /**
     * 渠道登记手机号码
     */
    @Column(name = "channel_reg_phone")
    private String channelRegPhone;

    /**
     * 渠道登记邮箱
     */
    @Column(name = "channel_reg_email")
    private String channelRegEmail;

    /**
     * 渠道账号
     */
    @Column(name = "channel_reg_user")
    private String channelRegUser;

    /**
     * 渠道密码
     */
    @Column(name = "channel_reg_pass")
    private String channelRegPass;

    /**
     * 渠道接口名称: 未清楚用途
     */
    @Column(name = "channel_reg_interface")
    private String channelRegInterface;

    /**
     * 初次创建的userID以及更新者的userID, 未知用途
     */
    @Column(name = "channel_create_user_id")
    private String channelCreateUserId;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 渠道扩展信息
     */
    @Column(name = "extra")
    private String extra;

}
