package com.qinglusaas.connect.infra.persistence.hibernate.audit;

import io.quarkus.security.identity.SecurityIdentity;

import javax.enterprise.inject.Instance;
import javax.enterprise.inject.spi.CDI;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/9/7, Wednesday
 **/
public abstract class AbstractGeneration {

    public String getLoginUser() {
        Instance<SecurityIdentity> instance = CDI.current().select(SecurityIdentity.class);
        if (null != instance) {
            SecurityIdentity securityIdentity = instance.get();
            if (null != securityIdentity.getPrincipal()) {
                return securityIdentity.getPrincipal().getName();
            }
        }
        return null;
    }

}
