package com.qinglusaas.connect.infra.persistence.entity.trade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/10/5 14:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_detail")
public class OrderDetailEntity extends AbstractEntity implements Serializable {

    @Column(name = "order_id", nullable = false)
    private long orderId;

    @Column(name = "store_id", nullable = false)
    private long storeId;

    @Column(name = "service_id", nullable = false)
    private long serviceId;

    @Column(name = "name", nullable = false, length = 30)
    private String name;

    @Column(name = "quantity", nullable = false)
    private short quantity;

    @Column(name = "amount", nullable = false)
    private long amount;

    @Column(name = "pay_mode", nullable = false)
    private byte payMode;

    @Column(name = "order_type", nullable = false)
    private byte orderType;

    @Column(name = "order_status", nullable = false)
    private byte orderStatus;

    @Column(name = "pay_status", nullable = false)
    private byte payStatus;

    @Column(name = "source_order_id", nullable = false, length = 35)
    private String sourceOrderId;

    @Column(name = "extra", nullable = false, length = 2000)
    private String extra;

    @Column(name = "op_user_id", nullable = false)
    private int opUserId;

    @Column(name = "order_time", nullable = false)
    private long orderTime;

    @Column(name = "order_op_time", nullable = false)
    private long orderOpTime;
}
