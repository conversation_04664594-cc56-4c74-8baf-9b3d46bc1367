package com.qinglusaas.connect.infra.remote.feizhu.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2023/1/8 20:29
 */
public interface FeiZhuVehicleEnum {

  @Getter
  @AllArgsConstructor(access = AccessLevel.PRIVATE)
  enum Tag {
    TAG_3(3, "信用双免"),
    TAG_14(14, "配手机支架"),
    TAG_17(17, "24小时营业"),
    TAG_18(18, "倒车雷达"),
    TAG_20(20, "配纸巾"),
    TAG_21(21, "配饮用水"),
    TAG_22(22, "配车充"),
    TAG_25(25, "儿童座椅"),
    TAG_26(26, "倒车影像"),
    TAG_27(27, "配行车记录仪"),
    TAG_30(30, "一车一洗"),
    TAG_32(32, "一年内新车"),
    TAG_33(33, "两年内新车"),
    TAG_34(34, "三年内新车"),
    TAG_39(39, "半年内新车"),
    TAG_40(40, "油量保障"),
    TAG_41(41, "不限里程"),
    TAG_218(218, "可升级车损免赔"),
    TAG_107(107, "小伤剐蹭免赔");

    private final int code;
    private final String name;
  }

  public interface ExtendedFeiZhuVehicleEnum extends FeiZhuVehicleEnum {

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum ExtendedTag {
      TAG_58(58, "安心租");

      private final int code;
      private final String name;
    }
  }
}
