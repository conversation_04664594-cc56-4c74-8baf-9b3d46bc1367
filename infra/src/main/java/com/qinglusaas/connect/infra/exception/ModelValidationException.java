package com.qinglusaas.connect.infra.exception;

import com.qinglusaas.connect.client.common.error.ClientName;
import com.qinglusaas.connect.client.saas.error.ErrorCode;

import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : s<PERSON><PERSON>@dian.so
 * @created : 2022/8/26, Friday
 **/
public class ModelValidationException extends BizException {

    public ModelValidationException(List<String> messages) {
        super(ClientName.SAAS, ErrorCode.MODEL_VALID.getCode(), messages);
    }

}
