package com.qinglusaas.connect.infra.persistence.dao;

import com.qinglusaas.connect.infra.persistence.entity.store.StoreGuideEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/10/10, Monday
 **/
@ApplicationScoped
public class StoreGuideRepository extends AbstractPanacheRepository<StoreGuideEntity, Long> {

    /**
     * 获取门店指引
     *
     * @param storeInfoIds 门店集合
     * @return 门店指引
     */
    public List<StoreGuideEntity> findAllByStoreInfoIds(Iterable<Long> storeInfoIds) {
        return find("from StoreGuideEntity storeGuideEntity where storeGuideEntity.storeInfoEntity.id in :storeInfoIds",
                Parameters.with("storeInfoIds", storeInfoIds)).list();
    }


}
