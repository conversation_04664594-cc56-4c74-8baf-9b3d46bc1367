package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Calendar;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/9/25 22:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "vehicle_bind")
@Where(clause = "deleted = 0")
public class VehicleBindEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    @Column(name = "vehicle_model_id", nullable = false)
    private long vehicleModelId;

    @Column(name = "channel_id", nullable = false)
    private long channelId;

    @Column(name = "merchant_id", nullable = false)
    private long merchantId;

    @Column(name = "bind_channel_vehicle_id", nullable = false, length = 40)
    private String bindChannelVehicleId;

    @Column(name = "bind_channel_vehicle_sery_id", nullable = false, length = 40)
    private String bindChannelVehicleSeryId;

    @Column(name = "synced", nullable = false)
    private int synced;

    @Column(name = "deleted", nullable = false)
    private int deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}
