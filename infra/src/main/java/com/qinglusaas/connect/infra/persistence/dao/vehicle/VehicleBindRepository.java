package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBindEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/9/25 22:44
 */
@ApplicationScoped
public class VehicleBindRepository extends AbstractPanacheRepository<VehicleBindEntity, Long> {

    public List<VehicleBindEntity> findAllByVehicleModelIdsAndChannelId(Iterable<Long> vehicleModelIds, Long channelId) {
        return find("vehicleModelId in :vehicleModelIds and channelId = :channelId and deleted = :deleted",
                Parameters.with("vehicleModelIds", vehicleModelIds).and("channelId", channelId).and("deleted", 0)
        ).list();
    }

    public List<VehicleBindEntity> findAllByBindChannelVehicleIdsAndChannelId(Iterable<String> bindChannelVehicleIds, Long channelId) {
        return find("bindChannelVehicleId in :bindChannelVehicleIds and channelId = :channelId and deleted = :deleted",
                Parameters.with("bindChannelVehicleIds", bindChannelVehicleIds).and("channelId", channelId)
                        .and("deleted", 0)
        ).list();
    }

    public List<VehicleBindEntity> findAllByBindChannelVehicleIdAndChannelId(String bindChannelVehicleId, Long channelId) {
        return find("bindChannelVehicleId = :bindChannelVehicleId and channelId = :channelId and deleted = :deleted",
                Parameters.with("bindChannelVehicleId", bindChannelVehicleId).and("channelId", channelId)
                        .and("deleted", 0)
        ).list();
    }

    public List<VehicleBindEntity> findAllByBindChannelVehicleSeryIdAndChannelId(String bindChannelVehicleSeryId, Long channelId) {
        return find("bindChannelVehicleSeryId = :bindChannelVehicleSeryId and channelId = :channelId and deleted = :deleted",
                Parameters.with("bindChannelVehicleSeryId", bindChannelVehicleSeryId).and("channelId", channelId)
                        .and("deleted", 0)
        ).list();
    }

    public Optional<VehicleBindEntity> findByBindChannelVehicleIdAndChannel(String bindChannelVehicleId, Long channelId) {
        return find("bindChannelVehicleId = :bindChannelVehicleId and channelId = :channelId and deleted = :deleted",
                Parameters.with("bindChannelVehicleId", bindChannelVehicleId).and("channelId", channelId)
                        .and("deleted", 0)
        ).singleResultOptional();
    }

    public Optional<VehicleBindEntity> findByVehicleModelIdAndChannel(Long vehicleModelId, Long channelId) {
        return find("vehicleModelId = :vehicleModelId and channelId = :channelId and deleted = :deleted",
                Parameters.with("vehicleModelId", vehicleModelId).and("channelId", channelId)
                        .and("deleted", 0)
        ).singleResultOptional();
    }

    public Optional<VehicleBindEntity> findByBindChannelVehicleIdAndChannelAndVehicleId(String bindChannelVehicleId, Long channelId, Long vehicleModelId) {
        return find("bindChannelVehicleId = :bindChannelVehicleId and channelId = :channelId and  vehicleModelId =: vehicleModelId and deleted = :deleted",
                Parameters.with("bindChannelVehicleId", bindChannelVehicleId)
                        .and("channelId", channelId)
                        .and("vehicleModelId", vehicleModelId)
                        .and("deleted", 0)
        ).singleResultOptional();
    }

    public List<VehicleBindEntity> findAllByMerchantIdAndChannelId(Long merchantId, Long channelId) {
        return find("merchantId = :merchantId and channelId = :channelId and deleted = :deleted",
            Parameters.with("merchantId", merchantId).and("channelId", channelId)
                .and("deleted", 0)
        ).list();
    }
}
