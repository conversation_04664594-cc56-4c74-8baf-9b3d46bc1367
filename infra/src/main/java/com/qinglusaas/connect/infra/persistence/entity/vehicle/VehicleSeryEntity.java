package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车系
 *
 * @TableName vehicle_sery
 */
@Table(name = "vehicle_sery")
@Data
@Entity
@Where(clause = "deleted = 0")
public class VehicleSeryEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 车系名称 eg:宝马3系
     */
    @Column(name = "sery_name")
    private String seryName;

    /**
     * 品牌ID
     */
    @Column(name = "brand_id")
    private Long brandId;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 是否预设 0:否 1:是
     */
    private Integer preset;

    /**
     * 0:正常;1:删除
     */
    private Integer deleted;

    /**
     * 数据更新人ID
     */
    @NotNull(message = "op_user_id 不能为空")
    @Column(name = "op_user_id", insertable = false)
    private Integer opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    /**
     * 获取车型品牌信息
     *
     * @param vehicleSeryEntityList
     * @return
     */
    public static List<VehicleBrandEntity> getVehicleBrandEntityList(List<VehicleSeryEntity> vehicleSeryEntityList) {
        List<Long> brandIds = vehicleSeryEntityList.stream().map(c -> c.getBrandId()).collect(Collectors.toList());
        return VehicleBrandEntity.list(VehicleBrandEntity_.ID + " in ?1", brandIds);
    }

}