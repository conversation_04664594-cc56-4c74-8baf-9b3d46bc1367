package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.HelloSubSeryEntity;

import javax.enterprise.context.ApplicationScoped;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class HelloSubSeryRepository extends AbstractPanacheRepository<HelloSubSeryEntity, Long> {


    /**
     * 根据helloId查询
     */
    public Optional<HelloSubSeryEntity> findByHelloId(String helloId) {
        return find("helloId", helloId).firstResultOptional();
    }
}
