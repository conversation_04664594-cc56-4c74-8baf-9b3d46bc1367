package com.qinglusaas.connect.infra.remote.hello.exception;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/24, Thursday
 **/
public class GetAccessTokenException extends RuntimeException {

    public GetAccessTokenException() {
    }

    public GetAccessTokenException(String message) {
        super(message);
    }

    public GetAccessTokenException(String message, Throwable cause) {
        super(message, cause);
    }

    public GetAccessTokenException(Throwable cause) {
        super(cause);
    }

    public GetAccessTokenException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
