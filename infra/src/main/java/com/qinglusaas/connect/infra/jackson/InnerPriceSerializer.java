package com.qinglusaas.connect.infra.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.qinglusaas.connect.client.common.dto.InnerPrice;

import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/12/7, Wednesday
 **/
public class InnerPriceSerializer extends JsonSerializer<InnerPrice> {

    @Override
    public void serialize(InnerPrice value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeNumber(Optional.ofNullable(value.getPrice()).orElse(0));
    }

}