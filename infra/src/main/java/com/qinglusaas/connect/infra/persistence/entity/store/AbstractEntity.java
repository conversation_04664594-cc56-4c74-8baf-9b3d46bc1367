package com.qinglusaas.connect.infra.persistence.entity.store;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Calendar;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/2, Friday
 **/
@MappedSuperclass
@Data
public abstract class AbstractEntity extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    public Integer lastVer;

    /**
     * 删除 0:否; 1:是
     */
    @NotNull(message = "[删除 0:否; 1:是]不能为空")
    @Column(name = "deleted")
    private Integer deleted;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
        this.deleted = 0;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}
