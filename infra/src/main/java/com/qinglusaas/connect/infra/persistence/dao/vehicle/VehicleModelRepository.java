package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleModelEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class VehicleModelRepository extends AbstractPanacheRepository<VehicleModelEntity, Long> {

    public List<VehicleModelEntity> findAllByIds(Iterable<Long> ids) {
        return findAllByIds(ids, 1, 0);
    }

    public List<VehicleModelEntity> findAllByIds(Iterable<Long> ids, int status, int test) {
        return find("id in :ids and status = :status and isTest = :test",
                Parameters.with("ids", ids).and("status", status).and("test", test)
        ).list();
    }

    public Optional<VehicleModelEntity> findByIdOptional(Long id) {
        return findByIdOptional(id, 1, 0);
    }

    public Optional<VehicleModelEntity> findByIdOptional(long id, int status, int test) {
        return find("id = :id and status = :status and isTest = :test",
                Parameters.with("id", id).and("status", status).and("test", test)
        ).singleResultOptional();
    }

    public List<VehicleModelEntity> findAllByMerchantId(long merchantId) {
        return findAllByMerchantId(merchantId, 1, 0);
    }

    public List<VehicleModelEntity> findAllByMerchantId(long merchantId, int status, int test) {
        return find("merchantId = :merchantId and status = :status and isTest = :test",
                Parameters.with("merchantId", merchantId).and("status", status).and("test", test)
        ).list();
    }
}
