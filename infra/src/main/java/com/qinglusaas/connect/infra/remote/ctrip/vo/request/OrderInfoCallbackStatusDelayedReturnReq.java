package com.qinglusaas.connect.infra.remote.ctrip.vo.request;

import lombok.Data;

import java.time.LocalDate;

/**
 * 延迟换车状态
 * vendorOrderStatus 为 4
 * 使用场景：订单续租，无法在预计还车时间还车，需要给到新的预计还车时间
 *
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/31, Monday
 **/
@Data
public class OrderInfoCallbackStatusDelayedReturnReq extends OrderInfoCallbackReq {

    /**
     * 预计还车时间
     * 带时区： 2018-10-20T16:00:00.000+08:00
     * 必填
     */
    private LocalDate expectedReturnTime;

}
