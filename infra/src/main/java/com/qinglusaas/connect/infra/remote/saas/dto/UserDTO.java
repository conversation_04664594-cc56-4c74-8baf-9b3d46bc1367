package com.qinglusaas.connect.infra.remote.saas.dto;

import com.qinglusaas.connect.infra.remote.saas.contants.UserEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/10/3 13:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    /**
     * 用户名
     */
    private String name;

    /**
     * mobile
     */
    private String mobile;

    /**
     * 证件类型
     * 1：身份证；2：护照；7：回乡证；8：台胞证
     */
    private UserEnum.IdTypeEnum idType;

    /**
     * ID号
     */
    private String idNo;
}
