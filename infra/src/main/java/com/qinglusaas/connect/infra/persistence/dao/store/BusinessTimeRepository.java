package com.qinglusaas.connect.infra.persistence.dao.store;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.store.BusinessTimeEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class BusinessTimeRepository extends AbstractPanacheRepository<BusinessTimeEntity, Long> {

    public List<BusinessTimeEntity> findByStoreIds(Iterable<Long> storeInfoIds) {
        return find("storeInfoEntity.id in :storeInfoIds and deleted = :deleted",
                Parameters.with("storeInfoIds", storeInfoIds).and("deleted", 0)
        ).list();
    }
}
