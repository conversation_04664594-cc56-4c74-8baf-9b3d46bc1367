package com.qinglusaas.connect.infra.remote.hello.client;

import com.qinglusaas.connect.infra.otlp.annotation.SpanClientName;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.*;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.QueryVehicleModelListResultResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.ResultResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreModifyNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreOnlineNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreOfflineNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.ServiceCircleNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.VehicleAddNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.request.*;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.OrderListResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.OrderDetailResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.OrderRenewListResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.OrderRenewDetailResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.CarOccupyInventoryResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.CarReleaseInventoryResp;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@RegisterRestClient(configKey = "hello.api")
public interface HelloClient {

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]openapi")
    ResultResp openAPI(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]queryVehicleModelList")
    QueryVehicleModelListResultResp queryVehicleModelList(@RequestBody BaseReq req);

    @POST
    @Path("/login")
    @SpanClientName("[saas-hello]登录")
    ResultResp<String> loginAPI(@RequestBody LoginReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆变更同步")
    ResultResp<BaseResultDTO> modifyVehicle(@RequestBody BaseReq req);


    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]新增车辆同步")
    VehicleAddNotifyResp.VehicleAddNotifyResult addVehicle(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店变更同步")
    StoreModifyNotifyResp.StoreModifyNotifyResult storeModify(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]获取商品列表")
    HelloGoodsInfoList.HelloGoodsInfoListResult goodsList(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]修改保险")
    InsuranceModifyNotifyResp.InsuranceModifyNotifyResult insuranceModify(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]修改附加服务")
    AddedServiceModifyNotifyResp.AddedServiceModifyNotifyResult addedServiceModify(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店上线通知")
    StoreOnlineNotifyResp.StoreOnlineNotifyResult storeOnline(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]新建门店")
    StoreCreateNotifyResp.StoreCreateNotifyResult storeCreate(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店下线通知")
    StoreOfflineNotifyResp.StoreOfflineNotifyResult storeOffline(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]服务范围保存")
    ServiceCircleNotifyResp.ServiceCircleNotifyResult serviceAreaSave(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]订单列表查询")
    OrderListResp.OrderListResult orderList(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]订单详情查询")
    OrderDetailResp.OrderDetailResult orderDetail(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]续租单列表查询")
    OrderRenewListResp.OrderRenewListResult orderRenewList(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]续租单详情查询")
    OrderRenewDetailResp.OrderRenewDetailResult orderRenewDetail(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店详情查询")
    HelloStoreInfoResp.HelloStoreInfoResult storeInfoDetail(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店关闭")
    StoreCloseNotifyResp.StoreCloseNotifyResult storeClose(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆库存占用")
    CarOccupyInventoryResp.CarOccupyInventoryResult carOccupyInventory(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆库存释放")
    CarReleaseInventoryResp.CarReleaseInventoryResult carReleaseInventory(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]查询车辆库存占用")
    QueryCarOccupyInventoryResp.QueryCarOccupyInventoryResult queryOccupyInventory(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]修改商品价格")
    StoreModifyNotifyResp.StoreModifyNotifyResult priceModify(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]添加保险")
    InsuranceCreateNotifyResp.InsuranceCreateNotifyResult insuranceCreate(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]添加增值服务")
    AddedServiceCreateNotifyResp.AddedServiceCreateNotifyResult addedServiceCreate(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]商品下架")
    ResultResp<Boolean> goodsAbandon(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]门店列表")
    HelloStoreListResp.HelloStoreListResult storeList(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆下线")
    VehicleOfflineResp.VehicleOfflineResult vehicleOffline(@RequestBody BaseReq req);
    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆上线")
    VehicleOnlineResp.VehicleOnlineResult vehicleOnline(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆调拨与校验")
    VehicleShuntingPreResp.VehicleShuntingPreResult vehicleShuntingPre(@RequestBody BaseReq req);
    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆调拨")
    VehicleShuntingResp.VehicleShuntingResult vehicleShunting(@RequestBody BaseReq req);
    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆列表")
    HelloVehicleInfoListResp.HelloVehicleInfoListResult vehicleList(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆详情")
    HelloVehicleInfoDTO.HelloVehicleInfoResult vehicleDetail(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]查询门店服务区")
    StoreServiceAreaQueryResp.StoreServiceAreaQueryResult storeServiceAreaQuery(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]车辆排车")
    VehicleSettleResp.VehicleSettleResult vehicleSettle(@RequestBody BaseReq req);

    @POST
    @Path("/openapi")
    @SpanClientName("[saas-hello]查询订单库存占用")
    OrderInventoryOccupyQueryResp.OrderInventoryOccupyQueryResult orderInventoryOccupyQuery(@RequestBody BaseReq req);
}