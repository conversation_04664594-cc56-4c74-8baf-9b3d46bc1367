//package com.qinglusaas.connect.infra.persistence.hibernate.entity;
//
//import io.quarkus.hibernate.orm.panache.PanacheEntity;
//import lombok.Data;
//
//import javax.persistence.Column;
//import javax.persistence.MappedSuperclass;
//import javax.persistence.PrePersist;
//import javax.persistence.PreUpdate;
//import java.util.Calendar;
//
///**
// * <AUTHOR> robintse
// * @mailto : <EMAIL>
// * @created : 2022/9/2, Friday
// **/
//@MappedSuperclass
//@Data
//public abstract class AbstractEntity extends PanacheEntity {
//
//    /**
//     * 创建时间
//     */
//    @Column(name = "create_time", nullable = false, updatable = false)
//    public Long createdAt;
//
//    /**
//     * 操作时间
//     */
//    @Column(name = "op_time", nullable = false)
//    public Long updatedAt;
//
//    /**
//     * 版本号
//     */
//    @Column(name = "last_ver")
//    public Integer lastVer;
//
//    @PrePersist
//    protected void onCreate() {
//        this.createdAt = Calendar.getInstance().getTimeInMillis();
//        this.updatedAt = Calendar.getInstance().getTimeInMillis();
//    }
//
//    @PreUpdate
//    protected void onUpdate() {
//        this.updatedAt = Calendar.getInstance().getTimeInMillis();
//    }
//
//}
