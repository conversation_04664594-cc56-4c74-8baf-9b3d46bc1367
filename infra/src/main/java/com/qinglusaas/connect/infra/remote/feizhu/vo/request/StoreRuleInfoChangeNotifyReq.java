package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qinglusaas.connect.infra.remote.feizhu.constants.APIMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 文档地址https://open.taobao.com/api.htm?docId=70051&docType=2
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class StoreRuleInfoChangeNotifyReq extends TaobaoBaseReq {

    /**
     * 额外字段
     */
    @JsonProperty("extra")
    private String extra;

    @JsonProperty("store_rule_info_list")
    private List<PutStoreRuleInfo> storeRuleInfoList;


    public StoreRuleInfoChangeNotifyReq() {
        super();
        super.setMethod(APIMethod.STORE_RULE_CHANGE_NOTIFY.getMethod());
    }

    @Data
    public static class PutStoreRuleInfo {
        @JsonProperty("cancel_rule_list")
        private List<ProductCancelRule> cancelRuleList;
        @JsonProperty("forbidden_drive_list")
        private List<ForbiddenDrive> forbiddenDriveList;
        @JsonProperty("invoice_rule")
        private ProductInvoiceRule invoiceRule;
        @JsonProperty("road_assist_list")
        private List<RoadAssist> roadAssistList;
        @JsonProperty("pickup_materials")
        private PickupMaterials pickupMaterials;
        @JsonProperty("store_code")
        private String storeCode;
        @JsonProperty("rule_code")
        private String ruleCode;
        @JsonProperty("energy_service_fee")
        private EnergyServiceFee energyServiceFee;
        @JsonProperty("deposit_deduction_policy")
        private DepositDeductionPolicy depositDeductionPolicy;
        @JsonProperty("scattered_hours_list")
        private List<ScatteredHours> scatteredHoursList;
    }

    @Data
    public static class ScatteredHours {
        @JsonProperty("end_hour")
        private Integer endHour;
        @JsonProperty("charge_fee")
        private Integer chargeFee;
        @JsonProperty("start_hour")
        private Integer startHour;
    }

    @Data
    public static class DepositDeductionPolicy {
        @JsonProperty("vehicle_item_loss_info_list")
        private List<VehicleItemLossInfo> vehicleItemLossInfoList;
        @JsonProperty("violation_dealing")
        private ViolationDealing violationDealing;
        @JsonProperty("vehicle_damage_info_list")
        private List<VehicleDamageInfo> vehicleDamageInfoList;
    }

    @Data
    public static class VehicleItemLossInfo {
        @JsonProperty("vehicle_item_loss_price_info_list")
        private List<VehiclePriceInfo> vehicleItemLossPriceInfoList;
        @JsonProperty("type")
        private Integer type;
    }

    @Data
    public static class ViolationDealing {
        @JsonProperty("illegal_deduct_rule")
        private IllegalDeductRule illegalDeductRule;
        @JsonProperty("drive_license_lending")
        private DriveLicenseLending driveLicenseLending;
        @JsonProperty("notify_detail")
        private NotifyDetail notifyDetail;
    }

    @Data
    public static class NotifyDetail {
        @JsonProperty("audit_time")
        private Integer auditTime;
        @JsonProperty("user_deal_time")
        private Integer userDealTime;
        @JsonProperty("notify_user_time")
        private Integer notifyUserTime;
    }

    @Data
    public static class DriveLicenseLending {
        @JsonProperty("diff_city_lending_availability_time")
        private Integer diffCityLendingAvailabilityTime;
        @JsonProperty("same_city_lending_availability_time")
        private Integer sameCityLendingAvailabilityTime;
        @JsonProperty("lending_drive_license_deposit")
        private Integer lendingDriveLicenseDeposit;
        @JsonProperty("lending_advance_booking_time")
        private Integer lendingAdvanceBookingTime;
    }

    @Data
    public static class IllegalDeductRule {
        @JsonProperty("late_processing_penalty")
        private Integer lateProcessingPenalty;
    }

    @Data
    public static class VehicleDamageInfo {

        @JsonProperty("vehicle_damage_price_info_list")
        private List<VehiclePriceInfo> vehicleDamagePriceInfoList;
        @JsonProperty("type")
        private Integer type;
    }

    @Data
    public static class VehiclePriceInfo {
        @JsonProperty("price")
        private Integer price;
        @JsonProperty("type")
        private Integer type;
    }

    @Data
    public static class EnergyServiceFee {
        @JsonProperty("oil_service_fee")
        private String oilServiceFee;
        @JsonProperty("battery_service_fee")
        private String batteryServiceFee;
    }

    @Data
    public static class PickupMaterials {

        @JsonProperty("id_valid_month")
        private Integer idValidMonth;
        @JsonProperty("id_type")
        private String idType;
        @JsonProperty("deposit_pay_type")
        private String depositPayType;
        @JsonProperty("credit_card_valid_month")
        private Integer creditCardValidMonth;
        @JsonProperty("min_driver_age")
        private Integer minDriverAge;
        @JsonProperty("min_drive_experience")
        private Integer minDriveExperience;
        @JsonProperty("max_driver_age")
        private Integer maxDriverAge;
        @JsonProperty("drive_license_expiry_date")
        private Integer driveLicenseExpiryDate;
    }

    @Data
    public static class RoadAssist {
        @JsonProperty("service_charge")
        private Integer serviceCharge;
        @JsonProperty("assist_type")
        private Integer assistType;
        @JsonProperty("service_range")
        private Integer serviceRange;

    }

    @Data
    public static class ProductInvoiceRule {
        @JsonProperty("invoice_content")
        private Integer invoiceContent;
        @JsonProperty("invoice_freight_type")
        private Integer invoiceFreightType;
        @JsonProperty("invoice_type")
        private Integer invoiceType;
        @JsonProperty("freight_price")
        private String freightPrice;

    }

    @Data
    public static class ForbiddenDrive {
        @JsonProperty("effect_start_time")
        private String effectStartTime;
        @JsonProperty("forbidden_type")
        private Integer forbiddenType;
        @JsonProperty("forbidden_city_code")
        private String forbiddenCityCode;
        @JsonProperty("effect_end_time")
        private String effectEndTime;
        @JsonProperty("effect_type")
        private Integer effectType;
        @JsonProperty("status")
        private Integer status;
    }

    @Data
    public static class ProductCancelRule {

        @JsonProperty("date_type")
        private Integer dateType;

        @JsonProperty("cancel_charge_type")
        private Integer cancelChargeType;

        @JsonProperty("start_time")
        private String startTime;

        @JsonProperty("end_time")
        private String endTime;

        @JsonProperty("noshow_charge_pt")
        private Integer noshowChargePt;

        @JsonProperty("cancel_charge")
        private CancelCharge cancelCharge;
    }

    @Data
    public static class CancelCharge {
        @JsonProperty("pick_up_charge_list")
        private List<PickUpCharge> pickUpChargeList;
        @JsonProperty("pick_up_free_hours")
        private Integer pickUpFreeHours;
    }

    @Data
    public static class PickUpCharge {
        @JsonProperty("pick_up_charge_begin_hours")
        private Integer pickUpChargeBeginHours;
        @JsonProperty("pick_up_charge_fee_percent")
        private Integer pickUpChargeFeePercent;
    }
}
