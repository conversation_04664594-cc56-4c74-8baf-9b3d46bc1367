package com.qinglusaas.connect.infra.metrics;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/13, Monday
 **/
public interface OrderMetrics {

    // 创建订单请求数
    String CREATE_ORDER_COUNT = "create_order_count";
    String CREATE_ORDER_COUNT_DESC = "创建普通订单请求数";

    // 创建订单成功请求数
    String CREATE_ORDER_SUCCESS_COUNT = "create_order_success_count";
    String CREATE_ORDER_SUCCESS_COUNT_DESC = "创建订单成功请求数";

    // 校验/创建订单库存不足请求数
    String CREATE_ORDER_FAIL_UNDER_STOCK_COUNT = "create_order_fail_under_stock_count";
    String CREATE_ORDER_FAIL_UNDER_STOCK_COUNT_DESC = "校验/创建订单库存不足请求数";

    // 校验/创建订单价格变动请求数
    String CREATE_ORDER_FAIL_PRICE_CHANGED_COUNT = "create_order_fail_price_changed_count";
    String CREATE_ORDER_FAIL_PRICE_CHANGED_COUNT_DESC = "校验/创建订单价格变动请求数";

    // 创建续租订单请求数
    String CREATE_RENEW_ORDER_COUNT = "create_renew_order_count";
    String CREATE_RENEW_ORDER_COUNT_DESC = "创建续租订单请求数";

    // 创建续租订单成功请求数
    String CREATE_RENEW_ORDER_SUCCESS_COUNT = "create_renew_order_success_count";
    String CREATE_RENEW_ORDER_SUCCESS_COUNT_DESC = "创建续租订单成功请求数";

    // 校验/创建续租订单库存不足请求数
    String CREATE_RENEW_ORDER_FAIL_UNDER_STOCK_COUNT = "create_renew_order_fail_under_stock_count";
    String CREATE_RENEW_ORDER_FAIL_UNDER_STOCK_COUNT_DESC = "校验/创建续租订单库存不足请求数";

    // 校验/创建续租订单价格变动请求数
    String CREATE_RENEW_ORDER_FAIL_PRICE_CHANGED_COUNT = "create_renew_order_fail_price_changed_count";
    String CREATE_RENEW_ORDER_FAIL_PRICE_CHANGED_COUNT_DESC = "校验/创建续租订单价格变动请求数";

    // 取消订单数请求数
    String CANCEL_ORDER_COUNT = "cancel_order_count";
    String CANCEL_ORDER_COUNT_DESC = "取消订单请求数";

    // 取消订单成功数请求数
    String CANCEL_ORDER_SUCCESS_COUNT = "cancel_order_success_count";
    String CANCEL_ORDER_SUCCESS_COUNT_DESC = "取消订单成功请求数";

    // 取消续租订单请求数
    String CANCEL_RENEW_ORDER_COUNT = "cancel_renew_order_count";
    String CANCEL_RENEW_ORDER_COUNT_DESC = "取消续租订单请求数";

    // 取消续租订单成功数请求数
    String CANCEL_RENEW_ORDER_SUCCESS_COUNT = "cancel_renew_order_success_count";
    String CANCEL_RENEW_ORDER_SUCCESS_COUNT_DESC = "取消续租订单成功请求数";


}
