package com.qinglusaas.connect.infra.remote.saas.dto;

import lombok.Data;

/**
 * 取消条约
 *
 * <AUTHOR>
 * @Date 2022/12/2 22:01
 */
@Data
public class CancelRuleDTO {

    /**
     * 取消规则ID
     */
    private Long id;

    /**
     * 取消规则ID
     */
    private Long cancelRuleId;

    /**
     * 取消时间类型  1：免费  2：按订单总金额比例  3：自定义
     */
    private Byte timeType;

    /**
     * x小时前取消
     */
    private Double beforeHour;

    /**
     * 收取订单总金额比例xx%
     */
    private Integer beforeHourPer;
}
