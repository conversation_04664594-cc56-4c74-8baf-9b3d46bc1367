package com.qinglusaas.connect.infra.persistence.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 第三方平台原始数据
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "third_platform_source_info")
public class ThirdPlatformSourceInfoEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    /**
     * 商家ID
     */
    @NotNull(message = "[商家ID]不能为空")
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 渠道ID
     */
    @NotNull(message = "[渠道ID]不能为空")
    @Column(name = "channel_id")
    private Long channelId;


    /**
     * 类型（0：门店 1：车型）
     */
    @Column(name = "type")
    @NotNull(message = "[类型ID]不能为空")
    private Byte type;


    @Column(name = "json_data")
    @NotNull(message = "[原始数据]不能为空")
    private String jsonData;

    @Column(name = "third_id")
    @NotNull(message = "[第三方id]不能为空")
    private String thirdId;
}
