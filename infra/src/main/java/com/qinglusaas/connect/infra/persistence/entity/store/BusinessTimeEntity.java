package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 门店夜间服务费
 *
 * @TableName business_time
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "business_time")
@Where(clause = "deleted = 0")
public class BusinessTimeEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店ID
     */
    @ManyToOne
    @JoinColumn(name = "store_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreInfoEntity storeInfoEntity;

    /**
     * 门店夜间服务费
     */
    @OneToMany(mappedBy = "businessTimeEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<NightServiceEntity> nightServiceEntities;

    /**
     * 营业周期 周一-周日: 1111111;周一:1000000;周日:0000001;周六:0000011
     */
    @NotNull(message = "[营业周期 周一-周日: 1111111;周一:1000000;周日:0000001;周六:0000011]不能为空")
    @Column(name = "business_period")
    private String businessPeriod;

    /**
     * 开始时间
     */
    @NotNull(message = "[开始时间]不能为空")
    @Column(name = "business_from")
    private Integer businessFrom;

    /**
     * 结束时间
     */
    @NotNull(message = "[结束时间]不能为空")
    @Column(name = "business_to")
    private Integer businessTo;

}
