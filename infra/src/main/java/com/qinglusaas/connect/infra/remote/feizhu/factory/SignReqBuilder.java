package com.qinglusaas.connect.infra.remote.feizhu.factory;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity_;
import com.qinglusaas.connect.infra.remote.feizhu.constants.HeaderParams;
import com.qinglusaas.connect.infra.remote.feizhu.vo.request.BaseReq;
import com.qinglusaas.connect.infra.util.FeizhuSpiUtils;
import com.qinglusaas.connect.infra.web.exception.SignatureException;

import javax.enterprise.context.ApplicationScoped;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@ApplicationScoped
public class SignReqBuilder {

    public BaseReq signReq(String method, Long channelId, Long merchantId, BaseReq baseReq) {
        ApiConnEntity apiConnEntity = (ApiConnEntity) ApiConnEntity.list(ApiConnEntity_.CHANNEL_ID + " = ?1 and " + ApiConnEntity_.MERCHANT_ID + " = ?2", channelId, merchantId)
                .stream().findFirst().orElse(null);
        if (null != apiConnEntity) {
            baseReq.init(method, apiConnEntity.getAppKey(), apiConnEntity.getExtra());
            Map<String, String> map = convertObjectToMapUsingObjectMapper(baseReq);
            map.remove(HeaderParams.SIGN);
            String sign = FeizhuSpiUtils.sign(map, null, apiConnEntity.getAppSecret(), "UTF-8");
            if (Objects.nonNull(sign)) {
                baseReq.setSign(sign);
            } else {
                throw new SignatureException();
            }
        } else {
            throw new SignatureException();
        }
        return baseReq;
    }



    public static Map<String, String> convertObjectToMapUsingObjectMapper(BaseReq baseReq) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        TypeReference<HashMap<String, String>> typeRef
                = new TypeReference<HashMap<String, String>>() {
        };
        Map<String, String> map = objectMapper.convertValue(baseReq, typeRef);
        return map;
    }

    public static Map<String, String> convertObjectToMapUsingObjectMapperV2(BaseReq baseReq) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        TypeReference<HashMap<String, Object>> typeRef
                = new TypeReference<HashMap<String, Object>>() {
        };
        Map<String, Object> map = objectMapper.convertValue(baseReq, typeRef);

        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                result.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return result;
    }

}
