package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 哈啰车型子系列实体
 */

@Table(name = "hello_sub_sery")
@Data
@Entity
public class HelloSubSeryEntity extends PanacheEntityBase implements Serializable  {
  /**
   * 主键ID
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", nullable = false)
  private Long id;

  /**
   * 哈啰ID
   */
  @Column(name = "hello_id", nullable = false, length = 64)
  private String helloId;

  /**
   * 品牌id
   */
  @Column(name = "brand_id", nullable = false, length = 64)
  private String brandId;

  /**
   * 品牌
   */
  @Column(name = "brand_name", nullable = false, length = 64)
  private String brandName;

  /**
   * 年款
   */
  @Column(name = "model_year", nullable = false)
  private Integer modelYear;

  /**
   * 车型年款名称
   */
  @Column(name = "model_name", nullable = false, length = 128)
  private String modelName;

  /**
   * 车系ID
   */
  @Column(name = "series_id", nullable = false, length = 64)
  private String seriesId;

  /**
   * 车系名称
   */
  @Column(name = "series_name", nullable = false, length = 128)
  private String seriesName;

  /**
   * 排量
   */
  @Column(name = "displacement", nullable = false, length = 32)
  private String displacement;

  /**
   * 排挡类型
   */
  @Column(name = "transmission_type", nullable = false)
  private Integer transmissionType;

  /**
   * 燃油类型
   */
  @Column(name = "fuel_type", nullable = false)
  private Integer fuelType;

  /**
   * 座位数
   */
  @Column(name = "seat_num", nullable = false)
  private Integer seatNum;

  /**
   * 车门数
   */
  @Column(name = "door_num", nullable = false)
  private Integer doorNum;

  /**
   * 状态
   */
  @Column(name = "status", nullable = false)
  private Integer status;

  /**
   * 厢型
   */
  @Column(name = "van_type", nullable = false)
  private Integer vanType;
}