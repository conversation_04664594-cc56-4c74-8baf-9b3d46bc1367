package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 门店取还车指引
 *
 * @TableName store_guide
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "store_guide")
@Where(clause = "deleted = 0")
public class StoreGuideEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店ID
     */
    @ManyToOne
    @JoinColumn(name = "store_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreInfoEntity storeInfoEntity;

    /**
     * 图片
     */
    @OneToMany(mappedBy = "storeGuideEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Where(clause = "deleted = 0")
    private List<GuidePicEntity> guidePicEntities;

    /**
     * 门店指引 0:取车指引;1:还车指引
     */
    @NotNull(message = "[门店指引 0:取车指引;1:还车指引]不能为空")
    @Column(name = "guide_type")
    private Integer guideType;

    /**
     * 描述内容
     */
    @NotBlank(message = "[描述内容]不能为空")
    @Size(max = 500, message = "编码长度不能超过500")
    @Length(max = 500, message = "编码长度不能超过500")
    @Column(name = "guide_desc")
    private String guideDesc;

    /**
     * 指引步骤
     */
    @NotNull(message = "[指引步骤]不能为空")
    @Column(name = "step")
    private Integer step;

}
