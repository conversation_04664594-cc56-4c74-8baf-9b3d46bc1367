package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Calendar;

/**
 * 携程子车款库表
 *
 * @TableName ctrip_vehicle_sub_sery
 */
@Table(name = "ctrip_vehicle_sub_sery")
@Data
@Entity
public class CtripVehicleSubSeryEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 车款名称
     */
    private String name;

    /**
     * 车系ID
     */
    @Column(name = "sery_id")
    private Long seryId;

    @Transient
    private String seryName;

    /**
     * 车品牌ID
     */
    @Column(name = "brand_id")
    private Long brandId;

    @Transient
    private String brandName;

    /**
     * 车门数
     */
    private String doors;

    /**
     * 乘坐人数
     */
    private String passengers;

    /**
     * 排量
     */
    private String displacement;

    /**
     * 变速器
     */
    private String transmission;

    /**
     * 变速箱
     */
    private String gearbox;

    /**
     * 年款
     */
    private String years;

    /**
     * 车厢/车型结构
     */
    private String carriage;

    /**
     * 车辆级别 eg:微型车
     */
    @Column(name = "vehicle_level")
    private String vehicleLevel;

    /**
     * 燃油形式 柴油/汽油/混动/电动/其他
     */
    @Column(name = "fuel_form")
    private String fuelForm;

    /**
     * 燃油形式补充
     */
    @Column(name = "fuel_form_detail")
    private String fuelFormDetail;

    /**
     * 燃油型号 eg: 92号
     */
    @Column(name = "fuel_num")
    private String fuelNum;

    /**
     * 车价格(万元)
     */
    private Double price;

    /**
     * 驱动类型 eg:前置前驱
     */
    @Column(name = "drive_type")
    private String driveType;

    /**
     * 车长
     */
    private Integer length;

    /**
     * 车宽
     */
    private Integer width;

    /**
     * 车高
     */
    private Integer height;

    /**
     * 轴距
     */
    private Integer wheelbase;

    /**
     * 行李箱
     */
    private Integer luggage;

    /**
     * 天窗
     */
    private String sunroof;

    /**
     * 空调
     */
    private String aircontition;

    /**
     * 屏幕
     */
    private String screen;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}