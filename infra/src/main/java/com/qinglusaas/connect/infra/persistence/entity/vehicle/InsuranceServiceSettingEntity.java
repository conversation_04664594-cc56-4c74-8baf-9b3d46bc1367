package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/10/7 21:52
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "insurance_service_setting")
public class InsuranceServiceSettingEntity extends AbstractEntity implements Serializable {

    @Column(name = "store_id", nullable = false)
    private long storeId;

    @Column(name = "merchant_id", nullable = false)
    private long merchantId;

    @Column(name = "preset", nullable = false)
    private byte preset;

    @Column(name = "name", nullable = false, length = 20)
    private String name;

    @Column(name = "damage_insurance", nullable = false)
    private byte damageInsurance;

    @Column(name = "damage_insurance_amount", nullable = false)
    private int damageInsuranceAmount;

    @Column(name = "glass", nullable = false)
    private byte glass;

    @Column(name = "tire", nullable = false)
    private byte tire;

    @Column(name = "third_party_insurance", nullable = false)
    private int thirdPartyInsurance;

    @Column(name = "outage_fee", nullable = false)
    private byte outageFee;

    @Column(name = "depreciation", nullable = false)
    private byte depreciation;

    @Column(name = "depreciation_fee", nullable = false)
    private int depreciationFee;

    @Column(name = "repair_fee_ratio", nullable = false, precision = 1)
    private BigDecimal repairFeeRatio;

    @Column(name = "status", nullable = false)
    private byte status;

    @Column(name = "op_user_id", nullable = false)
    private int opUserId;
}
