package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车辆表
 *
 * @TableName vehicle_info
 */
@Table(name = "vehicle_info")
@Data
@Entity
@Where(clause = "deleted = 0")
public class VehicleInfoEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 车型ID
     */
    @Column(name = "vehicle_model_id")
    private Long vehicleModelId;

    /**
     * 车型名称
     */
    @Column(name = "vehicle_model_name")
    private String vehicleModelName;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 门店名称
     */
    @Column(name = "store_name")
    private String storeName;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 车辆来源 1:自有;2:长期借调;3:临时借调;4:挂靠;5:托管;6:融资租赁
     */
    @Column(name = "vehicle_source")
    private Integer vehicleSource;

    /**
     * 车辆状态 1:待上线;2:未租赁;3:租赁中;4:维修中;5:保养中;6:调车中;7:事故中
     */
    @Column(name = "vehicle_status")
    private Integer vehicleStatus;

    /**
     * 车辆颜色id
     */
    @Column(name = "vehicle_color_id")
    private Long vehicleColorId;

    /**
     * 当前里程
     */
    private Integer mileage;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 车架号
     */
    @Column(name = "frame_num")
    private String frameNum;

    /**
     * 发动机号
     */
    @Column(name = "engine_num")
    private String engineNum;

    /**
     * 注册日期
     */
    @Column(name = "reg_date")
    private String regDate;

    /**
     * 使用性质 1:非营运;2:租赁;3:旅游客运;4:预约出租客运;5:营运转非;6:其他
     */
    @Column(name = "usage_nature")
    private Integer usageNature;

    /**
     * 年检有效期
     */
    @Column(name = "yearly_inspection_period")
    private String yearlyInspectionPeriod;

    /**
     * 删除 0:正常;1:删除
     */
    private Integer deleted;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    private Integer lastVer;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    @Column(name = "platform_sold")
    private Integer platformSold;

    @PrePersist
    protected void onCreate() {
        this.createTime = Calendar.getInstance().getTimeInMillis();
        this.opTime = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.opTime = Calendar.getInstance().getTimeInMillis();
    }

    /**
     * 查询车辆
     *
     * @param parameters
     * @return
     */
    public static List<VehicleInfoEntity> getVehicleInfos(Map<String, Object> parameters) {
        if (parameters == null) {
            return VehicleInfoEntity.listAll();
        }
        Map<String, Object> nonNullParams = parameters.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (nonNullParams.isEmpty()) {
            return VehicleInfoEntity.listAll();
        }
        String query = nonNullParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=:" + entry.getKey())
                .collect(Collectors.joining(" and "));

        return VehicleInfoEntity.find(query, nonNullParams).list();
    }
}