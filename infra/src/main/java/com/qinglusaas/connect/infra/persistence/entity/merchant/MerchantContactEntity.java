package com.qinglusaas.connect.infra.persistence.entity.merchant;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 商家联系人
 *
 * @TableName merchant_contact
 */
@Table(name = "merchant_contact")
@Entity
@Data
public class MerchantContactEntity extends PanacheEntityBase implements Serializable {
    /**
     *
     */
    @Id
    private Long id;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 联系描述 如财务联系人，运营联系人
     */
    @Column(name = "link_desc")
    private String linkDesc;

    /**
     * 联系人
     */
    @Column(name = "link_name")
    private String linkName;

    /**
     * 联系电话
     */
    @Column(name = "link_phone")
    private String linkPhone;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    private static final long serialVersionUID = 1L;
}