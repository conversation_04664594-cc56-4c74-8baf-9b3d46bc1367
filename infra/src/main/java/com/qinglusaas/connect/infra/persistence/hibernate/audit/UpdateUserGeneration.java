package com.qinglusaas.connect.infra.persistence.hibernate.audit;

import org.hibernate.HibernateException;
import org.hibernate.tuple.AnnotationValueGeneration;
import org.hibernate.tuple.GenerationTiming;
import org.hibernate.tuple.ValueGenerator;

import javax.enterprise.context.control.ActivateRequestContext;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/2, Friday
 **/
public class UpdateUserGeneration extends AbstractGeneration implements AnnotationValueGeneration<UpdateUser> {

    private static final long serialVersionUID = -4668805161430584880L;

    private ValueGenerator<?> generator;

    @Override
    @ActivateRequestContext
    public void initialize(UpdateUser annotation, Class<?> propertyType) {
        if (String.class == propertyType) {
            generator = (session, obj) -> {
                return getLoginUser();
            };
        } else {
            throw new HibernateException("Unsupported property type for generator annotation @UpdateUser");
        }
    }

    @Override
    public GenerationTiming getGenerationTiming() {
        return GenerationTiming.ALWAYS;
    }

    @Override
    public ValueGenerator<?> getValueGenerator() {
        return generator;
    }

    @Override
    public boolean referenceColumnInSql() {
        return false;
    }

    @Override
    public String getDatabaseGeneratedReferencedColumnValue() {
        return null;
    }

}
