package com.qinglusaas.connect.infra.remote.feizhu.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.qinglusaas.connect.client.common.constants.ChannelEnum;
import com.qinglusaas.connect.client.feizhu.dto.StoreVehicleModelAdditionalInfo;
import com.qinglusaas.connect.client.hello.constants.StatusEnum;
import com.qinglusaas.connect.infra.logger.LogName;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity_;
import com.qinglusaas.connect.infra.remote.feizhu.vo.request.*;
import com.qinglusaas.connect.infra.remote.feizhu.vo.response.*;
import com.taobao.api.internal.util.StringUtils;
import io.quarkus.arc.log.LoggerName;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.enterprise.context.ApplicationScoped;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@ApplicationScoped
public class FeiZhuClientWrapper {
    private static final TimeZone TZ_GMT8 = TimeZone.getTimeZone("GMT+8");

    private final ObjectMapper objectMapper = new ObjectMapper();
    @LoggerName(LogName.FEIZHU)
    Logger logger;
    @RestClient
    FeizhuClient feiZhuClient;
    @ConfigProperty(name = "deployment.env", defaultValue = "prod")
    public String env;


    public VehicleModelNotifyResponse vehicleModelNotify(CompanyVehicleModelNotifyReq pushMerchantModelReq, Long merchantId) {
        String result = this.postTaobaoClient(merchantId, pushMerchantModelReq);
        String msg = "feizhu 推送商家车型 {0}, resp:{1}, req:{2}";
        try {
            VehicleModelNotifyResponse resultResp = objectMapper.reader().readValue(result, VehicleModelNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(pushMerchantModelReq));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(pushMerchantModelReq));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, pushMerchantModelReq, e);
            return null;
        }
    }

    public VehicleNotifyResponse vehicleChangeNotify(
        StoreVehicleChangeNotifyReq storeVehicleChangeNotifyReq, Long merchantId) {
        String result = this.postTaobaoClient(merchantId, storeVehicleChangeNotifyReq);
        String msg = "feizhu 推送商家门店车辆 {0}, resp:{1}, req:{2}";
        try {
            VehicleNotifyResponse resultResp = objectMapper.reader().readValue(result, VehicleNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(storeVehicleChangeNotifyReq));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(storeVehicleChangeNotifyReq));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, storeVehicleChangeNotifyReq, e);
            return null;
        }
    }

    public StoreChangeNotifyResponse storeChangeNotify(Long merchantId, StoreChangeNotifyReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送门店 {0}, resp:{1}, req:{2}";
        try {
            StoreChangeNotifyResponse resultResp = objectMapper.reader().readValue(result, StoreChangeNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }


    public StoreRangeChangeNotifyResponse storeRangeChangeNotify(Long merchantId, StoreRangeChangeNotifyReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送服务圈 {0}, resp:{1}, req:{2}";
        try {
            StoreRangeChangeNotifyResponse resultResp = objectMapper.reader().readValue(result, StoreRangeChangeNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }


    public StorePolicyChangeNotifyResponse storeRuleChangeNotify(Long merchantId, StoreRuleInfoChangeNotifyReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送门店规则 {0}, resp:{1}, req:{2}";
        try {
            StorePolicyChangeNotifyResponse resultResp =
                objectMapper.reader().readValue(result, StorePolicyChangeNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }

    /**
     * 推送附加服务（改单条数据推送）
     * @param merchantId
     * @param req
     * @return
     */
    public AddedAndInsuranceResponse addedAndInsurance(Long merchantId, StoreVehicleModelAddedReq req) {
        String msg = "feizhu 推送附加服务和保险 {0}, 商家{1}";
        if (req.getStoreVehicleModelAdditionalList().isEmpty()) {
            logger.infov(msg,  "无推送数据，返回", merchantId);
            return null;
        }
        List<StoreVehicleModelAdditionalInfo> storeVehicleModelAdditionalList = req.getStoreVehicleModelAdditionalList().stream().collect(Collectors.toList());
        for (StoreVehicleModelAdditionalInfo storeVehicleModelAdditionalInfo : storeVehicleModelAdditionalList) {
            req.setStoreVehicleModelAdditionalList(Collections.singletonList(storeVehicleModelAdditionalInfo));
            addedAndInsuranceOne(merchantId, req);
        }
        return null;
    }

    private AddedAndInsuranceResponse addedAndInsuranceOne(Long merchantId, StoreVehicleModelAddedReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送附加服务和保险 {0}, resp:{1}, req:{2}";
        try {
            AddedAndInsuranceResponse resultResp = objectMapper.reader().readValue(result, AddedAndInsuranceResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg,  StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg,  StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg,  StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }

    public DepositAndMileageResponse depositAndMileage(Long merchantId, StoreVehicleModelBaseInfoReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送免押和里程限制 {0}, resp:{1}, req:{2}";
        try {
            DepositAndMileageResponse resultResp = objectMapper.reader().readValue(result, DepositAndMileageResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg,  StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg,  StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg,  StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }

    public static Map<String, String> convertObjectToMapUsingObjectMapperV2(TaobaoBaseReq baseReq) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<HashMap<String, Object>>() {};
        Map<String, Object> map = objectMapper.convertValue(baseReq, typeRef);
        Map<String, String> result = new HashMap<>();
        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                Object value = entry.getValue();
                if (value != null) {
                    if (value instanceof String) {
                        result.put(entry.getKey(), value.toString());
                    } else  {
                        result.put(entry.getKey(), objectMapper.writeValueAsString(value));
                    }
                }
            }
        } catch (JsonProcessingException e) {

        }
        return result;
    }


    public String postTaobaoClient(Long merchantId, TaobaoBaseReq baseReq) {
        ApiConnEntity apiConnEntity = (ApiConnEntity) ApiConnEntity.list(
                ApiConnEntity_.CHANNEL_ID + " = ?1 and " + ApiConnEntity_.MERCHANT_ID + " = ?2", ChannelEnum.FEI_ZHU.getCode(), merchantId)
            .stream().findFirst().orElse(null);
        baseReq.setAppKey(apiConnEntity.getAppKey());
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        format.setTimeZone(TZ_GMT8);
        baseReq.setTimestamp(format.format(new Date()));
        if (!"prod".equals(env)) {
            baseReq.setExtra("{\"env\":\"test\"}");
        }
        Map<String, String> allParams;
        Map<String, String> pathParam = new HashMap<>();
        try {
            allParams = convertObjectToMapUsingObjectMapperV2(baseReq);
            // 根据所有参数，生成签名
            String sign = this.signTopRequest(allParams, null, apiConnEntity.getAppSecret(), baseReq.getSignMethod());
            pathParam.put("app_key", baseReq.getAppKey());
            pathParam.put("method", baseReq.getMethod());
            pathParam.put("v", baseReq.getV());
            pathParam.put("sign", sign);
            pathParam.put("timestamp", baseReq.getTimestamp());
            pathParam.put("partner_id", baseReq.getPartnerId());
            pathParam.put("format", baseReq.getFormat());
            pathParam.put("sign_method", baseReq.getSignMethod());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String body = "";
        try {
            Map<String, String> bodyMap = new HashMap<>(allParams);
            bodyMap.remove("app_key");
            bodyMap.remove("method");
            bodyMap.remove("v");
            bodyMap.remove("sign");
            bodyMap.remove("timestamp");
            bodyMap.remove("partner_id");
            bodyMap.remove("format");
            bodyMap.remove("sign_method");
            body = buildQuery(bodyMap, "UTF-8");
        } catch (Exception e) {

        }
        String resultStr = feiZhuClient.postTaobaoApi(pathParam, body);
        return resultStr;
    }


    public CityChangeNotifyResponse cityChangeNotify(Long merchantId, CityChangeNotifyReq req) {
        String result = this.postTaobaoClient(merchantId, req);
        String msg = "feizhu 推送城市 {0}, resp:{1}, req:{2}";
        try {
            CityChangeNotifyResponse resultResp = objectMapper.reader().readValue(result, CityChangeNotifyResponse.class);
            if (!resultResp.isSuccess()) {
                logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, objectMapper.writeValueAsString(req));
            } else {
                logger.infov(msg, StatusEnum.SUCCESS.getDesc(), result, objectMapper.writeValueAsString(req));
            }
            return resultResp;
        } catch ( IOException e) {
            logger.errorv(msg, StatusEnum.FAILED.getDesc(), result, req, e);
            return null;
        }
    }

    private String jsonToString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            return "";
        }
    }

    public String signTopRequest(Map<String, String> params, String body, String secret, String signMethod) throws IOException {
        String[] keys = (String[])params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        if ("md5".equals(signMethod)) {
            query.append(secret);
        }

        String[] var6 = keys;
        int var7 = keys.length;

        for(int var8 = 0; var8 < var7; ++var8) {
            String key = var6[var8];
            String value = (String)params.get(key);
            if (StringUtils.areNotEmpty(new String[]{key, value})) {
                query.append(key).append(value);
            }
        }

        if (body != null) {
            query.append(body);
        }

        byte[] bytes;
        if ("hmac".equals(signMethod)) {
            bytes = encryptHMAC(query.toString(), secret);
        } else if ("hmac-sha256".equals(signMethod)) {
            bytes = encryptHMACSHA256(query.toString(), secret);
        } else {
            query.append(secret);
            bytes = encryptMD5(query.toString());
        }
        return byte2hex(bytes);
    }

    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (byte aByte : bytes) {
            String hex = Integer.toHexString(aByte & 255);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    public static byte[] encryptMD5(String data) throws IOException {
        return encryptMD5(data.getBytes("UTF-8"));
    }

    public static byte[] encryptMD5(byte[] data) throws IOException {
        byte[] bytes = null;

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            bytes = md.digest(data);
            return bytes;
        } catch (GeneralSecurityException var3) {
            throw new IOException(var3.toString());
        }
    }

    private static byte[] encryptHMAC(String data, String secret) throws IOException {
        byte[] bytes = null;

        try {
            SecretKey secretKey = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacMD5");
            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
            mac.init(secretKey);
            bytes = mac.doFinal(data.getBytes("UTF-8"));
            return bytes;
        } catch (GeneralSecurityException var5) {
            throw new IOException(var5.toString());
        }
    }

    private static byte[] encryptHMACSHA256(String data, String secret) throws IOException {
        byte[] bytes = null;

        try {
            SecretKey secretKey = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256");
            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
            mac.init(secretKey);
            bytes = mac.doFinal(data.getBytes("UTF-8"));
            return bytes;
        } catch (GeneralSecurityException var5) {
            throw new IOException(var5.toString());
        }
    }

    public static boolean areNotEmpty(String... values) {
        boolean result = true;
        if (values != null && values.length != 0) {
            String[] var2 = values;
            int var3 = values.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                String value = var2[var4];
                result &= !isEmpty(value);
            }
        } else {
            result = false;
        }

        return result;
    }

    public static boolean isEmpty(String value) {
        int strLen;
        if (value != null && (strLen = value.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(value.charAt(i))) {
                    return false;
                }
            }
            return true;
        } else {
            return true;
        }
    }

    public static String buildQuery(Map<String, String> params, String charset) throws IOException {
        if (params != null && !params.isEmpty()) {
            StringBuilder query = new StringBuilder();
            Set<Map.Entry<String, String>> entries = params.entrySet();
            boolean hasParam = false;
            Iterator var5 = entries.iterator();

            while(var5.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry)var5.next();
                String name = (String)entry.getKey();
                String value = (String)entry.getValue();
                if (StringUtils.areNotEmpty(new String[]{name, value})) {
                    if (hasParam) {
                        query.append("&");
                    } else {
                        hasParam = true;
                    }

                    query.append(name).append("=").append(URLEncoder.encode(value, charset));
                }
            }

            return query.toString();
        } else {
            return null;
        }
    }
}
