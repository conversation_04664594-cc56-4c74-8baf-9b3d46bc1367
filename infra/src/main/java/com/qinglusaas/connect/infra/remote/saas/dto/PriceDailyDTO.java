package com.qinglusaas.connect.infra.remote.saas.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 价格日历.
 *
 * <AUTHOR>
 * @date 2022/9/25 12:12
 */
@Data
public class PriceDailyDTO {

    /**
     * 日期。格式为yyyy-MM-dd。如” 2021-04-21”。
     */
    @NotNull
    private String date;

    /**
     * 整日价格
     */
    @NotNull
    private Long price;

    /**
     * 时间（H）
     */
    @NotNull
    private Integer time;

    /**
     * 非24小时（整天）时的价格
     * 可以为空
     */
    private Long partPrice;
}
