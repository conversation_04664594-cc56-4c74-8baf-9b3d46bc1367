package com.qinglusaas.connect.infra.constant;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/23, Friday
 **/
public enum EntityFieldsMapperEnum {

    DELETE_FLAG_DELETED("1"), // 已删除
    DELETE_FLAG_UNDELETE("0"), // 未删除
    TEST_FLAG_TEST("1"), // 测试版本
    TEST_FLAG_NO_TEST("0"), // 非测试版本
    STORE_ATTR_FILE_TYPE_COUNTER("1"), // 文件类型柜台照
    STORE_ATTR_FILE_TYPE_FRONTED("2"), // 文件类型正面照
    STORE_ATTR_FILE_TYPE_STREET_VIEW("3"), // 文件类型街景照
    STORE_STATUS_ENABLE("1"), // 门店状态正常营业
    STORE_STATUS_DISABLE("0"), // 门店状态关闭
    SERVICE_PICK_UP_ENABLE("1"), //启用
    SERVICE_PICK_UP_DISABLE("0"), //不启用
    PICKUP_TYPE_FREE_DELIVERY("1"), //免费接送
    PICKUP_TYPE_DOOR_DELIVERY("2"), // 上门取送车
    SERVICE_PICK_UP_CHARGE("0"), // 服务圈免费
    SERVICE_PICK_UP_FREE("1"); // 服务圈收费

    private final String code;


    EntityFieldsMapperEnum(String code) {
        this.code = code;
    }

    public Integer valueOfInteger() {
        return Integer.valueOf(this.code);
    }

    public String value() {
        return this.code;
    }
}
