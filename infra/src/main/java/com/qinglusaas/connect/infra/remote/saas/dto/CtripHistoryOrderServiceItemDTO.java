package com.qinglusaas.connect.infra.remote.saas.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @auther musi
 * @date 2023/7/25 16:51
 */
@Data
public class CtripHistoryOrderServiceItemDTO implements Serializable {

    /**
     * 费用项名称
     */
    private String name;

    /**
     * 费用项code
     */
    private String code;

    /**
     * 数量
     */
    private double quantity;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 总价
     */
    private Integer amount;
    /**
     * 收款方式
     * * 10: 线上
     * * 20: 线下
     */
    private Integer payMode;

}
