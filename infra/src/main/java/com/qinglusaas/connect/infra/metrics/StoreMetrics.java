package com.qinglusaas.connect.infra.metrics;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/21, Tuesday
 **/
public interface StoreMetrics {

    // 门店报价数请求数
    String STORE_PRICE_COUNT = "store_price_count";
    String STORE_PRICE_COUNT_DESC = "门店报价请求数";

    // 门店报价数请求计时
    String STORE_PRICE_TIME = "store_price_time";
    String STORE_PRICE_TIME_DESC = "门店报价请求计时";

    // 门店询价无车辆请求数
    String STORE_PRICE_NO_CAR_COUNT = "store_price_no_car_count";
    String STORE_PRICE_NO_CAR_COUNT_DESC = "门店报价无车辆请求数";

    // 门店车辆报价请求数
    String STORE_VEHICLE_PRICE_COUNT = "store_vehicle_price_count";
    String STORE_VEHICLE_PRICE_COUNT_DESC = "门店车辆报价请求数";

    // 门店车辆报价请求计时
    String STORE_VEHICLE_PRICE_TIME = "store_vehicle_price_time";
    String STORE_VEHICLE_PRICE_TIME_DESC = "门店车辆报价请求计时";

}
