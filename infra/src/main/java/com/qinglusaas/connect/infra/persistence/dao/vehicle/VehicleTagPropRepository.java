package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleTagPropEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/29 10:34
 */
@ApplicationScoped
public class VehicleTagPropRepository extends AbstractPanacheRepository<VehicleTagPropEntity, Long> {

    public List<VehicleTagPropEntity> findAllByIds(Iterable<Long> ids) {
        return find("id in :ids and deleted = :deleted",
                Parameters.with("ids", ids).and("deleted", 0)
        ).list();
    }
}
