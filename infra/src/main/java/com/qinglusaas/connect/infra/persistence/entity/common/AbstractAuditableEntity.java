package com.qinglusaas.connect.infra.persistence.entity.common;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/9/2, Friday
 **/
@MappedSuperclass
@Data
public abstract class AbstractAuditableEntity extends AbstractEntity {

    /**
     * 数据更新人ID
     */
    @NotNull(message = "op_user_id 不能为空")
    @Column(name = "op_user_id", insertable = false)
    private Integer opUserId;

}
