package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 门店图片
 *
 * @TableName store_att
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "store_att")
@Where(clause = "deleted = 0")
public class StoreAttEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店ID
     */
    @ManyToOne
    @JoinColumn(name = "store_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreInfoEntity storeInfoEntity;

    /**
     * 文件路径
     */
    @NotBlank(message = "[文件路径]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @Length(max = 100, message = "编码长度不能超过100")
    @Column(name = "file_path")
    private String filePath;

    /**
     * 文件类型 1:柜台照;2:正面照;3:街景照
     */
    @NotNull(message = "[文件类型 1:柜台照;2:正面照;3:街景照]不能为空")
    @Column(name = "file_type")
    private Integer fileType;

}
