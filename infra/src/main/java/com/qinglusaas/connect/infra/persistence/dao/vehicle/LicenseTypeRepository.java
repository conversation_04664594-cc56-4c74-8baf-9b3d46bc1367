package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.LicenseTypeEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class LicenseTypeRepository extends AbstractPanacheRepository<LicenseTypeEntity, Long> {

    public List<LicenseTypeEntity> findAllByCityId(long cityId) {
        return find("city_id = :cityId", Parameters.with("cityId", cityId)).list();
    }

}
