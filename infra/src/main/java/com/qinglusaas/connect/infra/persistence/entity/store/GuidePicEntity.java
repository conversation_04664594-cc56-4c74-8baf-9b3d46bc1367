package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 门店指引图片表
 *
 * @TableName guide_pic
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "guide_pic")
public class GuidePicEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店指引ID
     */
    @ManyToOne
    @JoinColumn(name = "guide_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
    private StoreGuideEntity storeGuideEntity;

    /**
     * 门店ID
     */
    @NotNull(message = "[门店ID]不能为空")
    @Column(name = "store_id")
    private Integer storeId;

    /**
     * 图片地址
     */
    @NotBlank(message = "[图片地址]不能为空")
    @Size(max = 200, message = "编码长度不能超过200")
    @Length(max = 200, message = "编码长度不能超过200")
    @Column(name = "guide_pic")
    private String guidePic;

}
