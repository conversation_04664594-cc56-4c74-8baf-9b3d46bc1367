package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.ThirdVehicleIdRelationEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 第三方车辆ID关联表Repository
 */
@ApplicationScoped
public class ThirdVehicleIdRelationRepository extends AbstractPanacheRepository<ThirdVehicleIdRelationEntity, Long> {

    /**
     * 根据渠道ID、商户ID、类型和SAAS ID查询映射关系
     */
    public Optional<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndSaasId(
            Long channelId, Long merchantId, Byte type, Long saasId) {
        Parameters parameters = Parameters.with("channelId", channelId)
                .and("merchantId", merchantId)
                .and("type", type)
                .and("saasId", saasId);
        Optional<ThirdVehicleIdRelationEntity> thirdVehicleIdRelationEntity = find(
                "channelId = :channelId and merchantId = :merchantId and type = :type and saasId = :saasId",
                parameters).firstResultOptional();
        return thirdVehicleIdRelationEntity;
    }

    /**
     * 根据渠道ID、商户ID、类型和SAAS thirdID查询映射关系
     */
    public Optional<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndThirdId(
            Long channelId, Long merchantId, Byte type, String thirdId) {
        Parameters parameters = Parameters.with("channelId", channelId)
                .and("merchantId", merchantId)
                .and("type", type)
                .and("thirdId", thirdId);
        Optional<ThirdVehicleIdRelationEntity> thirdVehicleIdRelationEntity = find(
                "channelId = :channelId and merchantId = :merchantId and type = :type and thirdId = :thirdId",
                parameters).firstResultOptional();
        return thirdVehicleIdRelationEntity;
    }

    /**
     * 根据渠道ID、商户ID、类型和第三方ID列表批量查询映射关系
     */
    public List<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndThirdIdIn(
            Long channelId, Long merchantId, Byte type, List<String> thirdIds) {
        Parameters parameters = Parameters.with("channelId", channelId)
                .and("merchantId", merchantId)
                .and("type", type)
                .and("thirdIds", thirdIds);
        return find("channelId = :channelId and merchantId = :merchantId and type = :type and thirdId in :thirdIds",
                parameters).list();
    }

    /**
     * 根据渠道ID、商户ID、门店ID、类型和SAAS ID查询映射关系
     */
    public Optional<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
            Long channelId, Long merchantId, Long storeId, Byte type, Long saasId) {
        Parameters parameters = Parameters.with("channelId", channelId)
                .and("merchantId", merchantId)
                .and("storeId", storeId)
                .and("type", type)
                .and("saasId", saasId);
        Optional<ThirdVehicleIdRelationEntity> thirdVehicleIdRelationEntity = find(
                "channelId = :channelId and merchantId = :merchantId and storeId = :storeId and type = :type and saasId = :saasId",
                parameters).firstResultOptional();
        return thirdVehicleIdRelationEntity;
    }

    public List<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndStoreIdAndTypes(Long channelId,
            Long merchantId, Long storeId, Iterable<Byte> types) {
        return find("channelId = ?1 and merchantId = ?2 and storeId = ?3 and type in ?4 ", channelId, merchantId,
                storeId, types).list();
    }

    public List<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantIdAndType(Long channelId, Long merchantId,
            Byte type) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 ", channelId, merchantId, type).list();
    }

    public List<ThirdVehicleIdRelationEntity> findByChannelIdAndMerchantId(Long channelId, Long merchantId, Byte type) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and deleted = 0", channelId, merchantId, type).list();
    }

    /**
     * 批量保存实体
     */
    @Transactional
    public void saveAll(List<ThirdVehicleIdRelationEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }
        persist(entities);
    }

    /**
     * 保存或更新映射关系
     */
    @Transactional
    public void save(ThirdVehicleIdRelationEntity entity) {
        if (entity == null) {
            return;
        }

        long curTime = System.currentTimeMillis();
        entity.setOpTime(curTime);
        if (entity.getId() == null) {
            entity.setCreateTime(curTime);
            entity.setDeleted(0);
            persist(entity);
        } else {
            String sql = "update ThirdVehicleIdRelationEntity set " +
                    "channelId = :channelId, " +
                    "merchantId = :merchantId, " +
                    "type = :type, " +
                    "thirdId = :thirdId, " +
                    "saasId = :saasId, " +
                    "storeId = :storeId, " +
                    "deleted = :deleted, " +
                    "opTime = :opTime " +
                    "where id = :id";

            Parameters parameters = Parameters.with("channelId", entity.getChannelId())
                    .and("merchantId", entity.getMerchantId())
                    .and("type", entity.getType())
                    .and("thirdId", entity.getThirdId())
                    .and("saasId", entity.getSaasId())
                    .and("storeId", entity.getStoreId())
                    .and("deleted", entity.getDeleted())
                    .and("opTime", entity.getOpTime())
                    .and("id", entity.getId());

            update(sql, parameters);
        }
    }

    /**
     * 根据主键逻辑删除
     * 
     * @param id 主键ID
     */
    @Transactional
    public void logicalDelete(Long id) {
        if (id == null) {
            return;
        }
        String sql = "update ThirdVehicleIdRelationEntity set deleted = 1, opTime = :opTime where id = :id";
        Parameters parameters = Parameters.with("opTime", System.currentTimeMillis())
                .and("id", id);
        update(sql, parameters);
    }
    
    /**
     * 根据主键批量逻辑删除
     * 
     * @param ids 主键ID列表
     */
    @Transactional
    public void logicalDeleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        String sql = "update ThirdVehicleIdRelationEntity set deleted = 1, opTime = :opTime where id in (:ids)";
        Parameters parameters = Parameters.with("opTime", System.currentTimeMillis())
                .and("ids", ids);
        update(sql, parameters);
    }
}