package com.qinglusaas.connect.infra.persistence.dao.merchant;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.merchant.MerchantInfoEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.Optional;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/1/17 23:47
 */
@ApplicationScoped
public class MerchantInfoRepository extends AbstractPanacheRepository<MerchantInfoEntity, Long> {

    public Optional<MerchantInfoEntity> findByIdOptional(long id, int isTest) {
        return find("id = :id and isTest = :isTest", Parameters.with("id", id).and("isTest", isTest))
                .singleResultOptional();
    }

    public Optional<MerchantInfoEntity> findByIdOptional(long id) {
        return findByIdOptional(id, 0);
    }
}
