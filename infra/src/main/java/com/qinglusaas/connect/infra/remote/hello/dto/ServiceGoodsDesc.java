package com.qinglusaas.connect.infra.remote.hello.dto;

import lombok.Data;

import java.util.List;

@Data
public class ServiceGoodsDesc {

    /**
     * 起赔额
     */
    private Long startPaidAmount;

    /**
     * 第三者保险
     */
    private Long thirdAssureAmount;

    /**
     * 包含的服务 1-包含轮胎损失;2-包含玻璃损失
     */
    private List<Long> containTypes;

    /**
     * 折旧费用。
     */
    private DepreciationExpense depreciationExpense;

    /**
     * 停运费
     */
    private ParkFee parkFee;

    /**
     * 自动创建
     */
    private Boolean autoCreate;

    /**
     * 是否需要垫付
     */
    private Boolean needAdvancePayment;

    private AddPurchaseItemDTO addPurchaseItemDTO;

    @Data
    public static class DepreciationExpense {
        /**
         * 是否收取折旧费
         */

        private Boolean needFlag;

        /**
         * 维修定损金额
         */
        private Long carOldFixFee;

        /**
         * 取定损金额费率 百分数
         */

        private Long carOldFeeRate;

        /**
         * 描述
         */
        private String context;
    }

    @Data
    public static class ParkFee {

        /**
         * 是否收取折旧费
         */
        private Boolean needFlag;

        /**
         * 停运费费率
         */
        private Long parkFeeRate;

        /**
         * 描述
         */
        private String context;

        /**
         * 车辆停运金额
         */
        private Long vehicleOutageAmount;
    }
}