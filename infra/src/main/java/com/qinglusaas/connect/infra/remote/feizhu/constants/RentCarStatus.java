package com.qinglusaas.connect.infra.remote.feizhu.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum RentCarStatus {

    PICKUP(121, "已取车"),
    PICK_OFF(122, "已还车");

    private Integer code;
    private String desc;

}
