package com.qinglusaas.connect.infra.remote.hello.dto;

import lombok.Data;
import java.util.List;

@Data
public class AddPurchaseItemDTO {
    /**
     * 商品类型。
     */
    private Integer goodsType;

    /**
     * 门店名称列表。
     */
    private List<String> siteNameList;

    /**
     * 服务描述。
     */
    private String serviceDesc;

    /**
     * 增值服务名称。
     */
    private String addPurchaseName;

    /**
     * 图片URL列表。
     */
    private List<String> pictureUrls;

    /**
     * 价格计算类型。
     */
    private Integer priceCalculateType;

    /**
     * 限制数量。
     */
    private Integer addLimit;

    /**
     * 增值服务库存列表。
     */

    private List<AddPurchaseInventory> addPurchaseInventoryList;

    @Data
    public static class AddPurchaseInventory {

        /**
         * 门店ID。
         */
        private String siteId;

        /**
         * 门店名称。
         */
        private String siteName;

        /**
         * 库存数量。
         */
        private Integer inventoryNum;
    }
}