package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@Data
public class RentCarStatusUpdateReq extends BaseReq {

    /**
     * 121-用车中（用户取车成功） 122-待结算（用户还车成功）
     * Y
     */
    private Integer status;
    /**
     * 服务商平台订单号
     * N
     */
    @JsonProperty("third_order_id")
    private String thirdOrderId;
    /**
     * 飞猪平台订单号
     * Y
     */
    @JsonProperty("order_id")
    private String orderId;
    /**
     * 服务商标识，由飞猪提供给到各服务商
     * Y
     */
    @JsonProperty("provider_id")
    private String providerId;
    /**
     * 车牌号 京A8888
     * N
     */
    @JsonProperty("car_number")
    private String carNumber;
    /**
     * {"errorCode":"1001", "errorMessage":"下单失败原因"}	JSON扩展值
     * N
     */
    private String extra;

}
