package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.enterprise.context.RequestScoped;

/**
 * <AUTHOR>
 */
@Data
@RequestScoped
public class TaobaoBaseReq {

    @JsonProperty("app_key")
    private String appKey;

    @JsonProperty("partner_id")
    private String partnerId;

    private String method;

    private String v;

    private String extra;

    private String format;

    private String timestamp;

    @JsonProperty("sign_method")
    private String signMethod;

    public TaobaoBaseReq () {
        this.partnerId = "top-sdk-java-20240822";
        this.v = "2.0";
        this.format = "json";
        this.signMethod = "hmac-sha256";
    }

}
