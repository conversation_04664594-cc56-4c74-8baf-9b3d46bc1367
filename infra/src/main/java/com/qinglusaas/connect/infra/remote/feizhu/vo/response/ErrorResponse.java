package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/3/11, Saturday
 **/
@Data
public class ErrorResponse {

    /**
     * 请求失败返回的错误码
     */
    public String code;
    /**
     * 请求失败返回的错误信息
     */
    public String msg;
    /**
     * 请求失败返回的子错误码
     */
    @JsonProperty("sub_code")
    public String subCode;
    /**
     * 请求失败返回的子错误信息
     */
    @JsonProperty("sub_msg")
    public String subMsg;

    /**
     * 平台颁发的每次请求访问的唯一标识
     */
    @JsonProperty("request_id")
    public String requestId;

}
