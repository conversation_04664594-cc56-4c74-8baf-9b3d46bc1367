package com.qinglusaas.connect.infra.remote.ctrip.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/31, Monday
 **/
@Data
public class PeccancyDTO {

    /**
     * 违章原因
     */
    private String reason;

    /**
     * 违章罚分
     */
    private Integer score;

    /**
     * 违章罚款金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 违章城市code
     */
    private String cityCode;

    /**
     * 违章地址
     */
    private String address;

    /**
     * 违章发生时间
     */
    private String peccancyTime;

    /**
     * 违章处理截止时间。格式Yyyy-MM-dd HH:mm:ss
     */
    private String deadLineTime;

    /**
     * 违章代处理费用
     * 必填
     */
    private BigDecimal agentFee;

    /**
     * 违章已经收取违章押金
     * 必填
     */
    private BigDecimal payAmount;

    /**
     * 违章已经退还违章押金
     * 必填
     */
    private BigDecimal returnAmount;


}
