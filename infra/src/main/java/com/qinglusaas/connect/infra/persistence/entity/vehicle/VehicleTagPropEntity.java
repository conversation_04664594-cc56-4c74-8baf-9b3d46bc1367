package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * 车辆标签
 * @TableName vehicle_tag_prop
 */
@Table(name="vehicle_tag_prop")
@Entity
@Data
public class VehicleTagPropEntity extends PanacheEntityBase implements Serializable {
    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 标签属性名称
     */
    @Column(name = "tag_name")
    private String tagName;

    /**
     * 是否预设 0:否 1:是
     */
    private Integer preset;

    /**
     * 0:正常;1:删除
     */
    private Integer deleted;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    private Integer lastVer;

    /**
     * 门店创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 门店修改时间
     */
    @Column(name = "op_time")
    private Long opTime;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        VehicleTagPropEntity other = (VehicleTagPropEntity) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getMerchantId() == null ? other.getMerchantId() == null : this.getMerchantId().equals(other.getMerchantId()))
            && (this.getTagName() == null ? other.getTagName() == null : this.getTagName().equals(other.getTagName()))
            && (this.getPreset() == null ? other.getPreset() == null : this.getPreset().equals(other.getPreset()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getLastVer() == null ? other.getLastVer() == null : this.getLastVer().equals(other.getLastVer()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getOpTime() == null ? other.getOpTime() == null : this.getOpTime().equals(other.getOpTime()))
            && (this.getOpUserId() == null ? other.getOpUserId() == null : this.getOpUserId().equals(other.getOpUserId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getMerchantId() == null) ? 0 : getMerchantId().hashCode());
        result = prime * result + ((getTagName() == null) ? 0 : getTagName().hashCode());
        result = prime * result + ((getPreset() == null) ? 0 : getPreset().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getLastVer() == null) ? 0 : getLastVer().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getOpTime() == null) ? 0 : getOpTime().hashCode());
        result = prime * result + ((getOpUserId() == null) ? 0 : getOpUserId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", storeId=").append(storeId);
        sb.append(", merchantId=").append(merchantId);
        sb.append(", tagName=").append(tagName);
        sb.append(", preset=").append(preset);
        sb.append(", deleted=").append(deleted);
        sb.append(", lastVer=").append(lastVer);
        sb.append(", createTime=").append(createTime);
        sb.append(", opTime=").append(opTime);
        sb.append(", opUserId=").append(opUserId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}