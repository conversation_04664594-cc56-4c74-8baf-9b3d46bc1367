package com.qinglusaas.connect.infra.persistence.entity.trade;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;

import javax.persistence.*;
import java.util.Calendar;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/2, Friday
 **/
@MappedSuperclass
@Data
public abstract class AbstractEntity extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    /**
     * 版本号
     */
    @Column(name = "last_ver")
    public Integer lastVer;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}
