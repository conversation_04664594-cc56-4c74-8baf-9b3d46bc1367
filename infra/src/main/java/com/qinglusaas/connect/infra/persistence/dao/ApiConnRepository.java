package com.qinglusaas.connect.infra.persistence.dao;

import java.util.List;
import java.util.Optional;

import javax.enterprise.context.ApplicationScoped;

import com.qinglusaas.connect.infra.constant.ApiConnEntityStatusEnum;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;

import io.quarkus.cache.CacheKey;
import io.quarkus.cache.CacheResult;
import io.quarkus.panache.common.Parameters;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/9/13, Tuesday
 **/
@ApplicationScoped
public class ApiConnRepository extends AbstractPanacheRepository<ApiConnEntity, Long> {

    @CacheResult(cacheName = "api-conn")
    public Optional<ApiConnEntity> findApiConnByAppKey(@CacheKey String appKey) {
        return find("from ApiConnEntity where appKey = :appKey", Parameters.with("appKey", appKey)).firstResultOptional();
    }

    @CacheResult(cacheName = "api-conn-app-key-channel-vendor-code")
    public Optional<ApiConnEntity> findApiConnByAppKeyAndChannelVendorCode(@CacheKey String appKey, @CacheKey String channelVendorCode) {
        return find("from ApiConnEntity where appKey = :appKey and channelVendorCode = :channelVendorCode", Parameters.with("appKey", appKey).and("channelVendorCode", channelVendorCode)).firstResultOptional();
    }

    public Optional<ApiConnEntity> findApiConnByChannelIdAndChannelVendorCode(Long channelId, String channelVendorCode) {
        return find("channelId = :channelId and channelVendorCode = :channelVendorCode", Parameters.with("channelId", channelId).and("channelVendorCode", channelVendorCode)).firstResultOptional();
    }

    public Optional<ApiConnEntity> findApiConnByMerchantId(Long merchantId) {
        return find("merchantId = :merchantId", Parameters.with("merchantId", merchantId)).firstResultOptional();
    }

    public Optional<ApiConnEntity> findByMerchantIdAndChannelId(Long merchantId, Long channelId) {
        return find("merchantId = :merchantId and channelId = :channelId",
            Parameters.with("merchantId", merchantId).and("channelId", channelId))
            .singleResultOptional();
    }

    public Optional<ApiConnEntity> findByMerchantIdAndChannelIdOptional(Long merchantId, Long channelId) {
        return findByMerchantIdAndChannelIdAndStatusOptional(merchantId, channelId, ApiConnEntityStatusEnum.OPEN_AUTHORIZATION.getStatus());
    }

    public Optional<ApiConnEntity> findByMerchantIdAndChannelIdAndStatusOptional(Long merchantId, Long channelId, int status) {
        return find("merchantId = :merchantId and channelId = :channelId and status = :status",
                Parameters.with("merchantId", merchantId).and("channelId", channelId).and("status", status))
                .singleResultOptional();
    }

    public List<ApiConnEntity> findAllByChannelIdAndStatus(Long channelId, int status) {
        return find("channelId = :channelId and status = :status",
                Parameters.with("channelId", channelId).and("status", status))
                .list();
    }

}