package com.qinglusaas.connect.infra.converter.ctrip;

import com.qinglusaas.connect.infra.converter.BizFieldConverter;

import javax.inject.Singleton;
import java.util.Objects;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/10/11, Tuesday
 **/
@Singleton
public class CtripFieldConverter implements BizFieldConverter {

    @Override
    public Integer storeTypeCovert(Integer originType) {
        // 携程 1-实体店；2-服务点；3-接送虚拟点；4-送取虚拟点
        // 业务  0:实体门店;1:服务网点
        switch (originType) {
            case 1:
                return 2;
            case 0:
            default:
                return 1;
        }
    }

    /**
     * 业务 -> 1精装修;2:标准装修;3:普通装修;4:低档装修;5:无门面无装修
     * 携程 -> 1-精装修; 2-标准 装修; 3-普通装修; 4-低档装修。
     *
     * @param storeDecorate
     * @return
     */
    public Integer storeDecorateConvert(Integer storeDecorate) {
        if (Objects.isNull(storeDecorate)) {
            return 2;
        }
        switch (storeDecorate) {
            case 1:
                return 1;
            case 2:
                return 2;
            case 3:
                return 3;
            case 4:
                return 4;
            default:
                return null;
        }
    }

    public Integer storeStatusConvert(Integer status) {
        // 携程 门店状态。0-下线；1-上线
        // 业务 门店状态 0:已关闭; 1:营业中
        switch (status) {
            case 0:
                return 0;
            case 1:
            default:
                return 1;
        }
    }

    /**
     * 业务 -> 门店指引 0:取车指引;1:还车指引
     * 携程 -> 指引类型。1-取车指引;2-还车指 引;3-门店指引。
     *
     * @param guideType
     * @return
     */
    public Integer getGuideTypeConvert(Integer guideType) {
        switch (guideType) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 4:
                return 4;
            case 5:
                return 5;
            default:
                return 3;
        }
    }

    public Integer stationTypeConvert(Integer stationType) {
        // 携程 枢纽站店类型。1-枢纽站内；2-枢纽站外；3-非枢纽站
        // 业务 枢纽类型 0:非枢纽;1:枢纽站外;2:枢纽站内
        switch (stationType) {
            case 0:
                return 3;
            case 1:
                return 2;
            case 2:
            default:
                return 1;
        }
    }

    public String deliveryServiceTypeConvert(Integer deliveryServiceType) {
        // 业务 1：免费接送   2：上门取送车
        // 携程 1-免费接送服务 2-上门送取车服务。
        switch (deliveryServiceType) {
            case 2:
                return "2";
            case 1:
            default:
                return "1";
        }
    }

    public Integer feeTypeConvert(Integer feeType) {
        // 携程 接送服务类型。1-免费接送服务；2-上门送取车服务
        // 业务 0:免费(免费接送服务) 1:收费(上门送取车服务)
        switch (feeType) {
            case 1:
                return 2;
            case 0:
            default:
                return 1;
        }
    }

    @Override
    public Integer vehicleStatusConvert(String ctripCarStatus) {
        return switch (ctripCarStatus) {
            case "FOR_RENT" -> 10;
            case "SYSTEM_OFFLINE" -> 0;
            case "FOREVER_OFFLINE" -> 20;
            default -> null;
        };
    }

    @Override
    public Integer vehicleAuditStatusConvert(String ctripCarStatus) {
        return switch (ctripCarStatus) {
            case "AUDIT_SUCCESS" -> 1;
            case "AUDIT_FAIL" -> 2;
            case "AUDIT_PROCESSING" -> 0;
            default -> null;
        };
    }

    @Override
    public Integer vehicleSaleStatusConvert(String ctripSaleStatus) {
        return switch (ctripSaleStatus) {
            case "SALE_ENABLE" -> 1;
            case "SALE_PART_DISABLE" -> 2;
            case "SALE_ALL_DISABLE" -> 0;
            default -> null;
        };
    }

    /**
     * 燃油形式转换
     *
     * @param fuelForm
     * @return
     */
    public Integer fuelFormConvert(String fuelForm) {
        if ("汽油".equals(fuelForm)) {
            return 0;
        } else if ("柴油".equals(fuelForm)) {
            return 1;
        } else if ("混动".equals(fuelForm)) {
            return 2;
        } else if ("电动".equals(fuelForm)) {
            return 3;
        } else if ("氢燃料".equals(fuelForm)) {
            return 5;
        } else {
            return 4;
        }
    }

    /**
     * 燃油形式转换
     *
     * @param fuelFormDetail
     * @return
     */
    public Integer fuelFormDetailConvert(String fuelFormDetail) {
        if ((Objects.nonNull(fuelFormDetail) && fuelFormDetail.startsWith("汽油"))
                || "汽油+90V轻混系统".equalsIgnoreCase(fuelFormDetail)
                || "汽油+48V轻混系统".equalsIgnoreCase(fuelFormDetail)
                || "汽油+24V轻混系统".equalsIgnoreCase(fuelFormDetail)
                || "汽油+天然气".equalsIgnoreCase(fuelFormDetail)
                || "汽油电驱".equalsIgnoreCase(fuelFormDetail)
                || "汽油+CNG".equalsIgnoreCase(fuelFormDetail)) {
            return 0;
        } else if ((Objects.nonNull(fuelFormDetail) && fuelFormDetail.startsWith("柴油"))
                || "柴油+48V轻混系统".equalsIgnoreCase(fuelFormDetail)) {
            return 1;
        } else if ("插电式混合动力".equalsIgnoreCase(fuelFormDetail)
                || "油电混合".equalsIgnoreCase(fuelFormDetail)
                || "增程式".equalsIgnoreCase(fuelFormDetail)
                || "甲醇混动".equalsIgnoreCase(fuelFormDetail)
                || "插电式".equalsIgnoreCase(fuelFormDetail)
                || "CNG".equalsIgnoreCase(fuelFormDetail)) {
            return 2;
        } else if ("纯电动".equalsIgnoreCase(fuelFormDetail)) {
            return 3;
        } else if ("氢燃料".equalsIgnoreCase(fuelFormDetail)) {
            return 5;
        } else {
            return 4;
        }
    }

    /**
     * 车牌转换
     *
     * @param licenceType
     * @return
     */
    public Integer licenceTypeConvert(String licenceType) {
        if (licenceType.startsWith("京")) {
            return 1;
        } else if (licenceType.startsWith("沪")) {
            return 2;
        } else if (licenceType.startsWith("粤A")) {
            return 3;
        } else if (licenceType.startsWith("粤B")) {
            return 4;
        } else if (licenceType.startsWith("浙A") || licenceType.startsWith("杭牌") || licenceType.startsWith("浙M")) {
            return 5;
        } else if (licenceType.startsWith("津")) {
            return 6;
        } else {
            return 0;
        }
    }


}
