package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;

/**
 * 车辆子车系
 *
 * @TableName vehicle_sub_sery
 */
@Table(name = "vehicle_sub_sery")
@Data
@Entity
@Where(clause = "deleted = 0")
public class VehicleSubSeryEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 车款名称 eg:宝马3系320Li2020款悦动版
     */
    private String name;

    /**
     * 车系ID
     */
    @Column(name = "sery_id")
    private Long seryId;

    /**
     * 车品牌ID
     */
    @Column(name = "brand_id")
    private Long brandId;

    /**
     * 车门数
     */
    private String doors;

    /**
     * 乘坐人数
     */
    private String passengers;

    /**
     * 排量
     */
    private String displacement;

    /**
     * 变速器 eg:手动
     */
    private String transmission;

    /**
     * 变速箱 eg:手动变速箱
     */
    private String gearbox;

    /**
     * 年款 eg:2005款
     */
    private String years;

    /**
     * 车厢/车型结构
     */
    private String carriage;

    /**
     * 车辆级别 eg:微型车
     */
    @Column(name = "vehicle_level")
    private String vehicleLevel;

    /**
     * 燃油形式 柴油/汽油/混动/电动/其他
     */
    @Column(name = "fuel_form")
    private String fuelForm;

    /**
     * 燃油形式补充
     */
    @Column(name = "fuel_form_detail")
    private String fuelFormDetail;

    /**
     * 燃油型号 eg: 92号
     */
    @Column(name = "fuel_num")
    private String fuelNum;

    /**
     * 车价格(万元)
     */
    private BigDecimal price;

    /**
     * 驱动类型 eg:前置前驱
     */
    @Column(name = "drive_type")
    private String driveType;

    /**
     * 车长
     */
    private Integer length;

    /**
     * 车宽
     */
    private Integer width;

    /**
     * 车高
     */
    private Integer height;

    /**
     * 轴距
     */
    private Integer wheelbase;

    /**
     * 行李箱
     */
    private Integer luggage;

    /**
     * 天窗
     */
    private String sunroof;

    /**
     * 空调
     */
    private String aircontition;

    /**
     * 屏幕
     */
    private String screen;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Integer storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Integer merchantId;

    /**
     * 是否预设 0:否 1:是
     */
    private Integer preset;

    /**
     * 0:正常;1:删除
     */
    private Integer deleted;

    @Column(name = "ctrip_sub_sery_id")
    private Long ctripSubSeryId;

    private Byte luxury;

    @Column(name = "model_group_id")
    private Long modelGroupId;

    @Column(name = "model_group_name")
    private String modelGroupName;

    /**
     * 数据更新人ID
     */
    @NotNull(message = "op_user_id 不能为空")
    @Column(name = "op_user_id", insertable = false)
    private Integer opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}