package com.qinglusaas.connect.infra.remote.hello.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/1/4, Wednesday
 **/
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum OrderStatusEnum {

    PICKUP("PICKUPED", "已取车"),
    CANCELLED("CANCELLED", "取消订单"),
    COMPLETED("COMPLETED", "已完成/已还车");

    private String code;
    private String desc;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static OrderStatusEnum fromCode(String code) {
        for (OrderStatusEnum r : OrderStatusEnum.values()) {
            if (null != code && code.equals(r.code)) {
                return r;
            }
        }
        throw new IllegalArgumentException();
    }

    @Override
    public String toString() {
        return code;
    }

}
