package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车型(SKU)
 *
 * @TableName vehicle_model
 */
@Table(name = "vehicle_model")
@Data
@Entity
@Where(clause = "(status = 1 or status = 0) and is_test = 0")
public class VehicleModelEntity extends AbstractAuditableEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 品牌车系ID
     */
    @Column(name = "vehicle_sery_id")
    private Long vehicleSeryId;

    /**
     * 品牌车系名称,冗余字段
     */
    @Column(name = "vehicle_sery_name")
    private String vehicleSeryName;

    /**
     * 车款(子车系)ID
     */
    @Column(name = "vehicle_sub_sery_id")
    private Long vehicleSubSeryId;

    /**
     * 子车系名称
     */
    @Column(name = "vehicle_sub_sery_name")
    private String vehicleSubSeryName;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 车辆年款
     */
    @Column(name = "vehicle_year_style")
    private String vehicleYearStyle;

    /**
     * 排量
     */
    private String displacement;

    /**
     * 变速箱
     */
    private String gearbox;

    /**
     * 座位数,默认为-1
     */
    @Column(name = "seat_num")
    private Integer seatNum;

    /**
     * 车厢式
     */
    private String carriage;

    /**
     * 车门数
     */
    private Integer doors;

    /**
     * 车型结构
     */
    @Column(name = "car_structure")
    private String carStructure;

    /**
     * 燃油形式
     */
    @Column(name = "fuel_form")
    private String fuelForm;

    /**
     * 车型分组ID
     */
    @Column(name = "vehicle_model_group_id")
    private Long vehicleModelGroupId;

    /**
     * 牌照类型Id
     */
    @Column(name = "license_type_id")
    private Long licenseTypeId;

    /**
     * 牌照类型名称
     */
    @Column(name = "license_type")
    private String licenseType;

    /**
     * 库存,不能为负
     */
    private Integer inventory;

    /**
     * 0:停售;1:售卖中;2:删除
     */
    private Integer status;

    /**
     * 测试 0:非测试 1: 测试数据
     */
    @Column(name = "is_test")
    private Integer isTest;

    /**
     * 查询车型
     *
     * @param parameters 参数只支持AND操作
     * @return
     */
    public static List<VehicleModelEntity> getVehicleModels(Map<String, Object> parameters) {
        if (parameters == null) {
            return VehicleModelEntity.listAll();
        }
        Map<String, Object> nonNullParams = parameters.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (nonNullParams.isEmpty()) {
            return VehicleModelEntity.listAll();
        }
        String query = nonNullParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=:" + entry.getKey())
                .collect(Collectors.joining(" and "));

        return VehicleModelEntity.find(query, nonNullParams).list();
    }

    public static List<VehicleSeryEntity> getVehicleSeryEntityList(List<VehicleModelEntity> vehicleModelEntityList) {
        List<Long> vehicleSeryIdList = vehicleModelEntityList.stream().map(v -> v.getVehicleSeryId()).collect(Collectors.toList());
        return VehicleSeryEntity.find(VehicleSeryEntity_.ID + " in ?1", vehicleSeryIdList).list();
    }

    public static List<VehicleSubSeryEntity> getVehicleSubSeryEntityList(List<VehicleModelEntity> vehicleModelEntityList) {
        List<Long> vehicleSubSeryIdList = vehicleModelEntityList.stream().map(v -> v.getVehicleSubSeryId()).collect(Collectors.toList());
        return VehicleSubSeryEntity.find(VehicleSubSeryEntity_.ID + " in ?1", vehicleSubSeryIdList).list();
    }

    public static List<VehicleModelGroupEntity> getVehicleModelGroupEntityList(List<VehicleModelEntity> vehicleModelEntityList) {
        List<Long> vehicleModelGroupIds = vehicleModelEntityList.stream().map(vehicleModelEntity -> vehicleModelEntity.getVehicleModelGroupId()).distinct().collect(Collectors.toList());
        return VehicleModelGroupEntity.find(VehicleModelGroupEntity_.ID + " in ?1", vehicleModelGroupIds).list();
    }

}