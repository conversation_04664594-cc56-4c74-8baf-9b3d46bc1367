package com.qinglusaas.connect.infra.constant.hello;

/**
 * 哈啰接口常量
 */
public class HelloMethodConstants {
    public static final String ADD_VEHICLE = "saas.hl.merchant.car.upload";
    public static final String UPDATE_VEHICLE = "saas.hl.merchant.car.modify";
    public static final String VEHICLE_ONLINE = "saas.hl.merchant.car.up.to.channel";
    public static final String VEHICLE_DELETE = "saas.hl.merchant.car.abandon";
    public static final String VEHICLE_SHUNTING_PRE = "saas.hl.vehicle.car.allocate.pre";
    public static final String VEHICLE_SHUNTING = "saas.hl.vehicle.car.allocate";
    public static final String VEHICLE_PAGE = "saas.hl.merchant.car.page";
    public static final String VEHICLE_DETAIL = "saas.hl.merchant.car.detail";
    public static final String VEHICLE_OFFLINE = "saas.hl.merchant.car.down.from.channel";
    /**
     * 商品列表
     */
    public static final String GOODS_LIST = "saas.hl.goods.page";

    /**
     * 门店上线
     */
    public static final String STORE_ONLINE = "saas.hl.channel.online.site";

    /**
     * 门店下线
     */
    public static final String STORE_OFFLINE = "saas.hl.channel.offline.site";

    /**
     * 新建门店
     */
    public static final String STORE_CREATE = "saas.hl.site.new";

    /**
     * 修改门店
     */
    public static final String STORE_MODIFY = "saas.hl.site.modify";
    /**
     * 门店列表
     */
    public static final String STORE_LIST = "saas.hl.site.query.list";

    /**
     * 新增修改门店服务范围
     */
    public static final String SERVICE_AREA_SAVE = "saas.hl.site.service.area.save";

    /**
     * 车辆库存占用
     */
    public static final String CAR_OCCUPY_INVENTORY = "saas.hl.car.occupy.inventory";

    /**
     * 车辆库存释放
     */
    public static final String CAR_RELEASE_INVENTORY = "saas.hl.car.release.inventory";

    /**
     * 添加保险(普通、优享、尊享)，添加增值服务
     */
    public static final String INSURANCE_CREATE = "saas.hl.goods.service.create";

    /**
     * 新增修改附加服务
     */
    public static final String INSURANCE_MODIFY = "saas.hl.goods.service.modify";

    /**
     * 新增创建附加服务(儿童座椅)
     */
    public static final String ADDED_SERVICE_CREATE = "saas.hl.goods.purchase.create";

    /**
     * 修改附加服务(儿童座椅)
     */
    public static final String ADDED_SERVICE_MODIFY = "saas.hl.goods.purchase.modify";

    /**
     * 修改商品&价格
     */
    public static final String GOODS_PRICE_MODIFY = "saas.hl.goods.price.modify";
    /**
     * 查询saas订单列表
     */
    public static final String ORDER_LIST = "saas.hl.order.list";
    /**
     * 查询saas订单详情
     */
    public static final String ORDER_DETAIL = "saas.hl.order.detail.query";
    /**
     * 查询saas续租单列表
     */
    public static final String ORDER_RENEW_LIST = "saas.hl.orderRenew.queryOrderRenewList";
    /**
     * 查询saas续租单详情
     */
    public static final String ORDER_RENEW_DETAIL = "saas.hl.orderRenew.queryOrderRenewDetail";
    /**
     * 查询门店详情
     */
    public static final String STORE_INFO_DETAIL = "saas.hl.site.query.detail";

    /**
     * 车辆库存占用查询
     */
    public static final String CAR_OCCUPY_INVENTORY_QUERY = "saas.hl.car.inventory.occupy.query";

    /**
     * 门店关闭
     */
    public static final String STORE_CLOSE = "saas.hl.site.close";

    /**
     * 查询门店服务区
     */
    public static final String STORE_SERVICE_AREA_QUERY = "saas.hl.site.service.area.query";

    /**
     * 车辆排车
     */
    public static final String VEHICLE_SETTLE = "saas.hl.vehicle.car.settle";

    /**
     * 查询订单库存占用
     */
    public static final String ORDER_INVENTORY_OCCUPY_QUERY = "saas.hl.order.inventory.occupy.query";
}
