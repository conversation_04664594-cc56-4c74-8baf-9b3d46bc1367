package com.qinglusaas.connect.infra.persistence.dao;

import com.qinglusaas.connect.infra.persistence.entity.common.AreaEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : shui<PERSON>@dian.so
 * @created : 2022/9/16, Friday
 **/
@ApplicationScoped
public class AreaRepository extends AbstractPanacheRepository<AreaEntity, Long> {

    public List<AreaEntity> findAllByIds(Iterable<Long> ids) {
        return find("id in :ids", Parameters.with("ids", ids)).list();
    }

    public List<AreaEntity> findAllByDepth(int depth) {
        return find("depth = :depth", Parameters.with("depth", depth)).list();
    }

    public List<AreaEntity> findAllByDepths(Iterable<Integer> depths) {
        return find("depth in :depths", Parameters.with("depths", depths)).list();
    }

    public List<AreaEntity> findAllByCityCodesAndDepth(Iterable<String> cityCodes, int depth) {
        return find("cityCode in :cityCodes and depth = :depth",
                Parameters.with("cityCodes", cityCodes).and("depth", depth))
                .list();
    }

    public List<AreaEntity> findAllByPhoneCityCodesAndDepth(Iterable<String> phoneCityCodes, int depth) {
        return find("phoneCityCode in :phoneCityCodes and depth = :depth",
                Parameters.with("phoneCityCodes", phoneCityCodes).and("depth", depth))
                .list();
    }

    public List<AreaEntity> findAllByCode(Iterable<Integer> codes, int depth) {
        //return find("code in :codes and depth = :depth", Parameters.with("codes", codes).and("depth", depth)).list();
        return find("code in :codes", Parameters.with("codes", codes)).list();
    }

    public List<AreaEntity> findAllByCode(Iterable<Integer> codes) {
        return find("code in :codes ", Parameters.with("codes", codes)).list();
    }

}
