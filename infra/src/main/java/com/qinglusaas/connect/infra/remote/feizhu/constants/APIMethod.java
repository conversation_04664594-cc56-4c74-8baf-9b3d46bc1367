package com.qinglusaas.connect.infra.remote.feizhu.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum APIMethod {

    UPDATE_RENT_CAR_STATUS("taobao.alitrip.domestic.rent.car.status.update"),

    STORE_CHANGE_NOTIFY("alitrip.rentcar.commodity.store.change.notify"),

    STORE_RANGE_CHANGE_NOTIFY("alitrip.rentcar.commodity.store.range.change.notify"),

    ADDED_SERVICE_NOTIFY("alitrip.rentcar.commodity.storevehmodel.addedservice.change.notify"),

    BASE_VEHICLE_MODEL_NOTIFY("alitrip.rentcar.commodity.storevehmodel.base.change.notify"),

    UPDATE_MERCHANT_MODEL("alitrip.rentcar.commodity.company.vehiclemodel.notify"),

    UPDATE_MERCHANT_VEHICLE("alitrip.rentcar.commodity.store.vehicle.change.notify"),

    CITY_CHANGE_NOTIFY("alitrip.rentcar.commodity.city.change.notify"),

    STORE_RULE_CHANGE_NOTIFY("alitrip.rentcar.commodity.store.rule.change.notify"),
    ;

    private String method;

}
