package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Calendar;

/**
 * 车辆品牌
 *
 * @TableName vehicle_brand
 */
@Table(name = "vehicle_brand")
@Data
@Entity
@Where(clause = "deleted = 0")
public class VehicleBrandEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private Long storeId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 品牌名称;eg:宝马
     */
    @Column(name = "brand_name")
    private String brandName;

    /**
     * 首字母
     */
    private String initials;

    /**
     * 英文名称
     */
    @Column(name = "english_name")
    private String englishName;

    /**
     * 是否预设 0:否 1:是
     */
    private Integer preset;

    /**
     * 0:正常;1:删除
     */
    private Integer deleted;

    /**
     * 数据更新人ID
     */
    @NotNull(message = "op_user_id 不能为空")
    @Column(name = "op_user_id", insertable = false)
    private Integer opUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = Calendar.getInstance().getTimeInMillis();
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Calendar.getInstance().getTimeInMillis();
    }

}