package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.RentMainEntity;

import javax.enterprise.context.ApplicationScoped;
import java.util.Optional;

/**
 * 租金基本信息Repository
 */
@ApplicationScoped
public class RentMainRepository extends AbstractPanacheRepository<RentMainEntity, Long> {

    // 根据车型+门店查询
    public Optional<RentMainEntity> findByVehicleModelIdAndStoreId(Long vehicleModelId, Long storeId) {
        return find("vehicleModelId = ?1 and storeId = ?2", vehicleModelId, storeId).singleResultOptional();
    }
}