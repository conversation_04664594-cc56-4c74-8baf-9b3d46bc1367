package com.qinglusaas.connect.infra.exception;

import com.qinglusaas.connect.client.common.error.ClientName;

import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/9/25 21:02
 */
public class RemoteBizException extends BizException {

    public RemoteBizException(ClientName clientName, String code, String message) {
        super(clientName, code, message);
    }

    public RemoteBizException(ClientName clientName, String code, String message, String subCode, String subMsg) {
        super(clientName, code, message, subCode, subMsg);
    }

    public RemoteBizException(ClientName clientName, String code, List<String> messages) {
        super(clientName, code, messages);
    }
}
