package com.qinglusaas.connect.infra.remote.hello.client;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qinglusaas.connect.client.common.constants.ChannelEnum;
import com.qinglusaas.connect.client.hello.constants.ErrorCode;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.*;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreCloseNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.request.*;
import com.qinglusaas.connect.infra.cache.redis.RedisClientService;
import com.qinglusaas.connect.infra.config.HelloSignConfig;
import com.qinglusaas.connect.infra.constant.RedisConstants;
import com.qinglusaas.connect.infra.constant.hello.HelloMethodConstants;
import com.qinglusaas.connect.infra.logger.LogName;
import com.qinglusaas.connect.infra.remote.hello.constants.APIMethod;
import com.qinglusaas.connect.infra.remote.hello.exception.GetAccessTokenException;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.QueryVehicleModelListResultResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.ResultResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.VehicleAddNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.request.StoreOnlineReq;
import com.qinglusaas.connect.infra.util.SignUtil;
import com.qinglusaas.connect.infra.util.SpanUtil;
import com.qinglusaas.connect.infra.web.exception.UnauthorizedException;
import com.qinglusaas.connect.infra.web.security.bean.SignContext;
import io.quarkus.arc.log.LoggerName;
import org.eclipse.microprofile.faulttolerance.Retry;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.SecurityContext;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/24, Thursday
 **/
@ApplicationScoped
public class HelloClientWrapper {

    @LoggerName(LogName.HELLO)
    Logger logger;
    @Context
    SecurityContext securityContext;
    @Inject
    SignReqBuilder signReqBuilder;
    @RestClient
    HelloClient helloClient;
    @Inject
    RedisClientService redisClientService;
    @Inject
    HelloSignConfig helloSignConfig;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Retry(maxRetries = 3, delay = 1000)
    public String getAccessToken(String appKey, String appSecret, String channelRegPhone) {
        String redisKey = String.format(RedisConstants.HELLO_LOGIN_TOKEN, ChannelEnum.HELLO.getCode(), appKey);
        String token = redisClientService.getStr(redisKey);
        if (null != token && token.length() > 0) {
            return token;
        }
        LoginReq loginReq = new LoginReq();
        loginReq.setMethod(APIMethod.LOGIN.getCode());
        String timestamp = String.valueOf(new Date().getTime());
        loginReq.setTimestamp(timestamp);
        loginReq.setAppKey(appKey);
        loginReq.setPhoneNumber(channelRegPhone);
        String sign = signReqBuilder.signReq(loginReq, appSecret);
        loginReq.setSign(sign);
        ResultResp<String> resultResp = helloClient.loginAPI(loginReq);
        if (resultResp.isSuccess()) {
            token = resultResp.getData();
            redisClientService.setStr(redisKey, Duration.ofDays(5).toMillis(), token);
            return token;
        } else {
            if (resultResp.getCode().equalsIgnoreCase(ErrorCode.INVALID_TOKEN_FAIL.getCode())) {
                redisClientService.del(redisKey);
                logger.infov("token失效，移除redis的token缓存key: {0}", redisKey);
            }
            logger.errorv("hello登录获取token失败, 响应报文：{0}", resultResp);
            throw new GetAccessTokenException("登录hello失败！");
        }
    }

    public ResultResp handleRequest(APIMethod apiMethod, String appKey, String appSecret, String merchantId,
            String phone, BaseReq commonReq) {
        String token = getAccessToken(appKey, appSecret, phone);
        commonReq.setToken(token);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(Calendar.getInstance().getTime());
        commonReq.setTimestamp(timestamp);
        commonReq.setMethod(apiMethod.getCode());
        commonReq.setAppKey(appKey);
        commonReq.setPartnerId(merchantId);
        String sign = signReqBuilder.signReq(commonReq, appSecret);
        commonReq.setSign(sign);
        logger.infov("请求hello接口报文: {0}", commonReq);
        ResultResp resultResp = helloClient.openAPI(commonReq);
        if (resultResp.isTokenInvalid()) {
            String redisKey = String.format(RedisConstants.HELLO_LOGIN_TOKEN, ChannelEnum.HELLO.getCode(), appKey);
            redisClientService.del(redisKey);
            logger.infov("请求方法 {0}, token失效，移除redis的token缓存key: {1}", apiMethod, redisKey);
        }
        return resultResp;
    }

    public ResultResp wrapperSignRequest(APIMethod apiMethod, BaseReq commonReq) {
        Optional<SignContext> signContext = SignUtil.getSignBySecurityContext(securityContext);
        if (signContext.isPresent()) {
            return handleRequest(apiMethod, signContext.get().getAppKey(), signContext.get().getAppSecret(),
                    signContext.get().getMerchantId().toString(), signContext.get().getChannelRegPhone(), commonReq);
        } else {
            throw new UnauthorizedException();
        }
    }

    public QueryVehicleModelListResultResp queryVehicleModelListRequest(APIMethod apiMethod, BaseReq commonReq) {
        Optional<SignContext> signContext = SignUtil.getSignBySecurityContext(securityContext);
        if (signContext.isPresent()) {
            return handleQueryVehicleModelListRequest(apiMethod, signContext.get().getAppKey(),
                    signContext.get().getAppSecret(), signContext.get().getMerchantId().toString(),
                    signContext.get().getChannelRegPhone(), commonReq);
        } else {
            throw new UnauthorizedException();
        }
    }

    public QueryVehicleModelListResultResp handleQueryVehicleModelListRequest(APIMethod apiMethod, String appKey,
            String appSecret, String merchantId, String phone, BaseReq commonReq) {
        String token = getAccessToken(appKey, appSecret, phone);
        commonReq.setToken(token);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(Calendar.getInstance().getTime());
        commonReq.setTimestamp(timestamp);
        commonReq.setMethod(apiMethod.getCode());
        commonReq.setAppKey(appKey);
        commonReq.setPartnerId(merchantId);
        String sign = signReqBuilder.signReq(commonReq, appSecret);
        commonReq.setSign(sign);
        logger.infov("请求hello接口报文: {0}", commonReq);
        QueryVehicleModelListResultResp resultResp = helloClient.queryVehicleModelList(commonReq);
        if (resultResp.isTokenInvalid()) {
            String redisKey = String.format(RedisConstants.HELLO_LOGIN_TOKEN, ChannelEnum.HELLO.getCode(), appKey);
            redisClientService.del(redisKey);
            logger.infov("请求方法 {0}, token失效，移除redis的token缓存key: {1}", apiMethod, redisKey);
        }
        return resultResp;
    }

    private void assembleReq(String method, Long merchantId, BaseReq commonReq) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(Calendar.getInstance().getTime());
        commonReq.setTimestamp(timestamp);
        commonReq.setMethod(method);
        commonReq.setAppKey(helloSignConfig.appKey());
        // commonReq.setPartnerId(merchantId.toString());
        String sign = signReqBuilder.signReq(commonReq, helloSignConfig.appSecret());
        commonReq.setSign(sign);
    }

    private void assembleReq(String method, BaseReq commonReq) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(Calendar.getInstance().getTime());
        commonReq.setTimestamp(timestamp);
        commonReq.setMethod(method);
        commonReq.setAppKey(helloSignConfig.appKey());
        // commonReq.setPartnerId(merchantId.toString());
        String sign = signReqBuilder.signReq(commonReq, helloSignConfig.appSecret());
        commonReq.setSign(sign);
    }

    private void assembleReq(String method, String channelVendorCode, BaseReq commonReq) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(Calendar.getInstance().getTime());
        commonReq.setTimestamp(timestamp);
        commonReq.setMethod(method);
        commonReq.setAppKey(helloSignConfig.appKey());
        commonReq.setMerchantId(channelVendorCode);
        // commonReq.setPartnerId(merchantId.toString());
        String sign = signReqBuilder.signReq(commonReq, helloSignConfig.appSecret());
        commonReq.setSign(sign);
    }

    public ResultResp<VehicleAddNotifyResp> vehicleAddNotify(String channelVendorCode, HelloVehicleInfoDTO vehicleInfo) {
        this.assembleReq(HelloMethodConstants.ADD_VEHICLE, channelVendorCode, vehicleInfo);
        VehicleAddNotifyResp.VehicleAddNotifyResult vehicleAddNotifyResult = helloClient.addVehicle(vehicleInfo);
        logger.infov("请求hello接口报文 车辆添加, req:{0}, resp:{1}", SpanUtil.toJson(vehicleInfo), SpanUtil.toJson(vehicleAddNotifyResult));
        return vehicleAddNotifyResult;
    }

    public ResultResp<BaseResultDTO> vehicleUpdateNotify(String channelVendorCode, HelloVehicleInfoDTO vehicleInfo) {
        this.assembleReq(HelloMethodConstants.UPDATE_VEHICLE, channelVendorCode, vehicleInfo);
        ResultResp<BaseResultDTO> vehicleAddNotifyResult = helloClient.modifyVehicle(vehicleInfo);
        logger.infov("请求hello接口报文 车辆更新, req:{0}, resp:{1}", SpanUtil.toJson(vehicleInfo), SpanUtil.toJson(vehicleAddNotifyResult));
        return vehicleAddNotifyResult;
    }


    public ResultResp vehicleDeleteNotify(String channelVendorCode, HelloVehicleIdReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_DELETE, channelVendorCode, req);
        ResultResp resultResp = helloClient.vehicleOnline(req);
        logger.infov("请求hello接口报文 车辆删除, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(resultResp));
        return resultResp;
    }

    public ResultResp vehicleOfflineNotify(String channelVendorCode, BaseReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_OFFLINE, channelVendorCode, req);
        ResultResp resultResp = helloClient.vehicleOnline(req);
        logger.infov("请求hello接口报文 车辆下线, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(resultResp));
        return resultResp;
    }


    public ResultResp vehicleOnlineNotify(String channelVendorCode, HelloVehicleOnlineReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_ONLINE, channelVendorCode, req);
        ResultResp resultResp = helloClient.vehicleOnline(req);
        logger.infov("请求hello接口报文 车辆上线, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(resultResp));
        return resultResp;
    }


    public VehicleShuntingResp.VehicleShuntingResult vehicleShuntNotify(String channelVendorCode,
                                                                HelloVehicleAllocateReq allocate) {
        this.assembleReq(HelloMethodConstants.VEHICLE_SHUNTING, channelVendorCode, allocate);
        VehicleShuntingResp.VehicleShuntingResult resultResp = helloClient.vehicleShunting(allocate);
        logger.infov("请求hello接口报文 车辆调拨, req:{0}, resp:{1}", SpanUtil.toJson(allocate), SpanUtil.toJson(resultResp));
        return resultResp;
    }

    /**
     * 门店上线通知
     *
     * @param merchantId 商户ID
     * @param req        上线请求
     * @return 接口响应
     */
    public ResultResp<StoreOnlineNotifyResp> storeOnlineNotify(Long merchantId, StoreOnlineReq req) {
        this.assembleReq(HelloMethodConstants.STORE_ONLINE, merchantId, req);
        StoreOnlineNotifyResp.StoreOnlineNotifyResult storeOnlineNotifyResult = helloClient.storeOnline(req);
        logger.infov("请求hello接口报文 门店上线, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeOnlineNotifyResult));
        return storeOnlineNotifyResult;
    }

    /**
     * 门店下线通知
     *
     * @param merchantId 商户ID
     * @param req        下线请求
     * @return 接口响应
     */
    public ResultResp<StoreOfflineNotifyResp> storeOfflineNotify(Long merchantId, StoreOfflineReq req) {
        this.assembleReq(HelloMethodConstants.STORE_OFFLINE, merchantId, req);
        StoreOfflineNotifyResp.StoreOfflineNotifyResult storeOfflineNotifyResult = helloClient.storeOffline(req);
        logger.infov("请求hello接口报文 门店下线, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeOfflineNotifyResult));
        return storeOfflineNotifyResult;
    }

    /**
     * 新建门店
     *
     * @param merchantId 商户ID
     * @param req        新建门店请求
     * @return 接口响应
     */
    public ResultResp<StoreCreateNotifyResp> storeCreateNotify(Long merchantId, StoreCreateReq req) {
        this.assembleReq(HelloMethodConstants.STORE_CREATE, merchantId, req);
        StoreCreateNotifyResp.StoreCreateNotifyResult storeCreateNotifyResult = helloClient.storeCreate(req);
        logger.infov("请求hello接口报文 新建门店, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeCreateNotifyResult));
        return storeCreateNotifyResult;
    }

    /**
     * 修改门店
     *
     * @param merchantId 商户ID
     * @param req        修改门店请求
     * @return 接口响应
     */
    public ResultResp<StoreModifyNotifyResp> storeModifyNotify(Long merchantId, StoreModifyReq req) {
        this.assembleReq(HelloMethodConstants.STORE_MODIFY, merchantId, req);
        StoreModifyNotifyResp.StoreModifyNotifyResult storeModifyNotifyResult = helloClient.storeModify(req);
        logger.infov("请求hello接口报文 修改门店, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeModifyNotifyResult));
        return storeModifyNotifyResult;
    }

    /**
     * 新增修改门店服务范围
     *
     * @param merchantId 商户ID
     * @param req        修改门店请求
     * @return 接口响应
     */
    public ResultResp<ServiceCircleNotifyResp> serviceAreaNotify(Long merchantId, ServiceAreaSaveReq req) {
        this.assembleReq(HelloMethodConstants.SERVICE_AREA_SAVE, merchantId, req);
        ServiceCircleNotifyResp.ServiceCircleNotifyResult serviceCircleNotifyResult = helloClient.serviceAreaSave(req);
        logger.infov("请求hello接口报文 服务范围修改, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(serviceCircleNotifyResult));
        return serviceCircleNotifyResult;
    }

    /**
     * 获取商品列表
     *
     * @param merchantId 商户ID
     * @param req        获取车型列表请求
     * @return 接口响应
     */
    public ResultResp<HelloGoodsInfoList> goodsList(Long merchantId, HelloGoodsListReq req) {
        this.assembleReq(HelloMethodConstants.GOODS_LIST, merchantId, req);
        HelloGoodsInfoList.HelloGoodsInfoListResult result = helloClient.goodsList(req);
        logger.infov("请求hello接口报文 获取商品列表, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 获取门店列表
     *
     * @param merchantId 商户ID
     * @param req        请求参数
     * @return 接口响应
     */
    public ResultResp<HelloStoreListResp> storeList(Long merchantId, HelloStoreListReq req) {
        this.assembleReq(HelloMethodConstants.STORE_LIST, merchantId, req);
        HelloStoreListResp.HelloStoreListResult result = helloClient.storeList(req);
        logger.infov("请求hello接口报文 获取门店列表, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 查询saas订单列表
     *
     * @param merchantId 商户ID
     * @param req        查询订单列表请求
     * @return 接口响应
     */
    public ResultResp<OrderListResp> orderList(Long merchantId, OrderListReq req) {
        this.assembleReq(HelloMethodConstants.ORDER_LIST, merchantId, req);
        OrderListResp.OrderListResult orderListResult = helloClient.orderList(req);
        logger.infov("请求hello接口报文 订单列表, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(orderListResult));
        return orderListResult;
    }

    /**
     * 查询saas订单详情
     *
     * @param merchantId 商户ID
     * @param req        查询订单详情请求
     * @return 接口响应
     */
    public ResultResp<OrderDetailResp> orderDetail(Long merchantId, OrderDetailReq req) {
        this.assembleReq(HelloMethodConstants.ORDER_DETAIL, merchantId, req);
        OrderDetailResp.OrderDetailResult orderDetailResult = helloClient.orderDetail(req);
        logger.infov("请求hello接口报文 订单详情, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(orderDetailResult));
        return orderDetailResult;
    }

    /**
     * 查询saas续租单列表
     *
     * @param merchantId 商户ID
     * @param req        查询续租单列表请求
     * @return 接口响应
     */
    public ResultResp<OrderRenewListResp> orderRenewList(Long merchantId, OrderRenewListReq req) {
        this.assembleReq(HelloMethodConstants.ORDER_RENEW_LIST, merchantId, req);
        OrderRenewListResp.OrderRenewListResult orderRenewListResult = helloClient.orderRenewList(req);
        logger.infov("请求hello接口报文 续租单列表, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(orderRenewListResult));
        return orderRenewListResult;
    }

    /**
     * 查询saas续租单详情
     *
     * @param merchantId 商户ID
     * @param req        查询续租单详情请求
     * @return 接口响应
     */
    public ResultResp<OrderRenewDetailResp> orderRenewDetail(Long merchantId, OrderRenewDetailReq req) {
        this.assembleReq(HelloMethodConstants.ORDER_RENEW_DETAIL, merchantId, req);
        OrderRenewDetailResp.OrderRenewDetailResult orderRenewDetailResult = helloClient.orderRenewDetail(req);
        logger.infov("请求hello接口报文 续租单详情, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(orderRenewDetailResult));
        return orderRenewDetailResult;
    }

    public ResultResp<HelloStoreInfoResp> getStoreInfoDetail(Long merchantId, HelloStoreDetailReq req) {
        this.assembleReq(HelloMethodConstants.STORE_INFO_DETAIL, merchantId, req);
        HelloStoreInfoResp.HelloStoreInfoResult storeInfoResult = helloClient.storeInfoDetail(req);
        logger.infov("请求hello接口报文 门店详情, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeInfoResult));
        return storeInfoResult;
    }

    /**
     * 查询车辆库存占用
     *
     * @param merchantId 商户ID
     * @param req        查询请求
     * @return 接口响应
     */
    public ResultResp<QueryCarOccupyInventoryResp> queryOccupyInventory(Long merchantId, BaseReq req) {
        this.assembleReq(HelloMethodConstants.CAR_OCCUPY_INVENTORY_QUERY, merchantId, req);
        QueryCarOccupyInventoryResp.QueryCarOccupyInventoryResult result = helloClient.queryOccupyInventory(req);
        logger.infov("请求hello接口报文 查询车辆库存占用, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 门店关闭通知
     *
     * @param merchantId 商户ID
     * @param req        关闭请求
     * @return 接口响应
     */
    public ResultResp<StoreCloseNotifyResp> storeCloseNotify(Long merchantId, StoreCloseReq req) {
        this.assembleReq(HelloMethodConstants.STORE_CLOSE, merchantId, req);
        StoreCloseNotifyResp.StoreCloseNotifyResult storeCloseNotifyResult = helloClient.storeClose(req);
        logger.infov("请求hello接口报文 门店关闭, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(storeCloseNotifyResult));
        return storeCloseNotifyResult;
    }

    /**
     * 车辆库存占用
     *
     * @param merchantId 商户ID
     * @param req        占用请求
     * @return 接口响应
     */
    public ResultResp<CarOccupyInventoryResp> occupyInventoryNotify(Long merchantId, CarOccupyInventoryReq req) {
        this.assembleReq(HelloMethodConstants.CAR_OCCUPY_INVENTORY, merchantId, req);
        CarOccupyInventoryResp.CarOccupyInventoryResult carOccupyInventoryResult = helloClient.carOccupyInventory(req);
        logger.infov("请求hello接口报文 车辆库存占用, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(carOccupyInventoryResult));
        return carOccupyInventoryResult;
    }

    /**
     * 释放车辆库存占用
     *
     * @param merchantId 商户ID
     * @param req        释放请求
     * @return 接口响应
     */
    public ResultResp<CarReleaseInventoryResp> releaseInventoryNotify(Long merchantId, CarReleaseInventoryReq req) {
        this.assembleReq(HelloMethodConstants.CAR_RELEASE_INVENTORY, merchantId, req);
        CarReleaseInventoryResp.CarReleaseInventoryResult carReleaseInventoryResult = helloClient.carReleaseInventory(req);
        logger.infov("请求hello接口报文 释放车辆库存占用, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(carReleaseInventoryResult));
        return carReleaseInventoryResult;
    }

    /**
     * 修改商品&价格
     *
     * @param merchantId 商户ID
     * @param req        修改门店请求
     * @return 接口响应
     */
    public ResultResp<StoreModifyNotifyResp> priceModifyNotify(Long merchantId, HelloGoodsPriceSaveReq req) {
        this.assembleReq(HelloMethodConstants.GOODS_PRICE_MODIFY, merchantId, req);
        StoreModifyNotifyResp.StoreModifyNotifyResult result = helloClient.priceModify(req);
        logger.infov("请求hello接口报文 修改商品价格, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 添加保险(普通、优享、尊享)，添加增值服务
     *
     * @param merchantId 商户ID
     * @param req        修改保险请求
     * @return 接口响应
     */
    public ResultResp<InsuranceCreateNotifyResp> insuranceCreateNotify(Long merchantId, InsuranceCreateReq req) {
        this.assembleReq(HelloMethodConstants.INSURANCE_CREATE, merchantId, req);
        InsuranceCreateNotifyResp.InsuranceCreateNotifyResult result = helloClient.insuranceCreate(req);
        logger.infov("请求hello接口报文 添加保险, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 修改保险(普通、优享、尊享)，修改增值服务
     *
     * @param merchantId 商户ID
     * @param req        修改保险请求
     * @return 接口响应
     */
    public ResultResp<InsuranceModifyNotifyResp> insuranceModifyNotify(Long merchantId, InsuranceModifyNotifyReq req) {
        this.assembleReq(HelloMethodConstants.INSURANCE_MODIFY, merchantId, req);
        InsuranceModifyNotifyResp.InsuranceModifyNotifyResult result = helloClient.insuranceModify(req);
        logger.infov("请求hello接口报文 修改保险, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 添加增值服务
     *
     * @param merchantId 商户ID
     * @param req        修改保险请求
     * @return 接口响应
     */
    public ResultResp<AddedServiceCreateNotifyResp> addedServiceCreateNotify(Long merchantId,
            AddedServiceCreateReq req) {
        this.assembleReq(HelloMethodConstants.ADDED_SERVICE_CREATE, merchantId, req);
        AddedServiceCreateNotifyResp.AddedServiceCreateNotifyResult result = helloClient.addedServiceCreate(req);
        logger.infov("请求hello接口报文 添加增值服务, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 添加增值服务
     *
     * @param merchantId 商户ID
     * @param req        修改保险请求
     * @return 接口响应
     */
    public ResultResp<AddedServiceModifyNotifyResp> addedServiceModifyNotify(Long merchantId,
                                                                             AddedServiceModifyNotifyReq req) {
        this.assembleReq(HelloMethodConstants.ADDED_SERVICE_MODIFY, merchantId, req);
        AddedServiceModifyNotifyResp.AddedServiceModifyNotifyResult result = helloClient.addedServiceModify(req);
        logger.infov("请求hello接口报文 修改增值服务, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }


    public ResultResp<HelloVehicleInfoListResp> vehicleList(Long merchantId, HelloVehicleListReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_PAGE, merchantId, req);
        HelloVehicleInfoListResp.HelloVehicleInfoListResult vehicleList = helloClient.vehicleList(req);
        logger.infov("请求hello接口报文 车辆列表, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(vehicleList));
        return vehicleList;
    }

    public ResultResp<HelloVehicleInfoDTO> vehicleDetail(String channelVendorCode, HelloVehicleInfoDetailReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_DETAIL, channelVendorCode, req);
        HelloVehicleInfoDTO.HelloVehicleInfoResult vehicleInfo = helloClient.vehicleDetail(req);
        logger.infov("请求hello接口报文 车辆详情, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(vehicleInfo));
        return vehicleInfo;
    }

    /**
     * 查询门店服务区
     *
     * @param merchantId 商户ID
     * @param req 查询请求
     * @return 接口响应
     */
    public ResultResp<StoreServiceAreaQueryResp> storeServiceAreaQuery(String merchantId, StoreServiceAreaQueryReq req) {
        this.assembleReq(HelloMethodConstants.STORE_SERVICE_AREA_QUERY, merchantId, req);
        StoreServiceAreaQueryResp.StoreServiceAreaQueryResult result = helloClient.storeServiceAreaQuery(req);
        logger.infov("请求hello接口报文 查询门店服务区, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 车辆排车
     *
     * @param merchantId 商户ID
     * @param req 排车请求
     * @return 接口响应
     */
    public ResultResp<VehicleSettleResp> vehicleSettleNotify(String merchantId, HelloVehicleSettleReq req) {
        this.assembleReq(HelloMethodConstants.VEHICLE_SETTLE, merchantId, req);
        VehicleSettleResp.VehicleSettleResult result = helloClient.vehicleSettle(req);
        logger.infov("请求hello接口报文 车辆排车, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }

    /**
     * 查询订单库存占用
     *
     * @param merchantId 商户ID
     * @param req 查询请求
     * @return 接口响应
     */
    public ResultResp<OrderInventoryOccupyQueryResp> orderInventoryOccupyQuery(Long merchantId, OrderInventoryOccupyQueryReq req) {
        this.assembleReq(HelloMethodConstants.ORDER_INVENTORY_OCCUPY_QUERY, merchantId, req);
        OrderInventoryOccupyQueryResp.OrderInventoryOccupyQueryResult result = helloClient.orderInventoryOccupyQuery(req);
        logger.infov("请求hello接口报文 查询订单库存占用, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(result));
        return result;
    }
}
