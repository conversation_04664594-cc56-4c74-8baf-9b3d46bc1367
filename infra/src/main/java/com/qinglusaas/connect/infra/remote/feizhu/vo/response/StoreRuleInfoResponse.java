package com.qinglusaas.connect.infra.remote.feizhu.vo.response;

import com.qinglusaas.connect.client.feizhu.vo.response.FeiZhuBaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class StoreRuleInfoResponse extends FeiZhuBaseResponse {

    private Map<String, List<PutStoreRuleInfo>> searchResp;

    @Data
    public static class PutStoreRuleInfo {
        private List<ProductCancelRule> cancelRuleList;
        private List<ForbiddenDrive> forbiddenDriveList;
        private ProductInvoiceRule invoiceRule;
        private List<RoadAssist> roadAssistList;
        private PickupMaterials pickupMaterials;
        private String storeCode;
        private String ruleCode;
        private EnergyServiceFee energyServiceFee;
        private DepositDeductionPolicy depositDeductionPolicy;
        private List<ScatteredHours> scatteredHoursList;
    }

    @Data
    public static class ScatteredHours {
        private Integer endHour;
        private Integer chargeFee;
        private Integer startHour;
    }

    @Data
    public static class DepositDeductionPolicy {
        private List<VehicleItemLossInfo> vehicleItemLossInfoList;
        private ViolationDealing violationDealing;
        private List<VehicleDamageInfo> vehicleDamageInfoList;
    }

    @Data
    public static class VehicleItemLossInfo {

        private List<VehiclePriceInfo> vehicleItemLossPriceInfoList;
        private Integer type;
    }

    @Data
    public static class VehicleDamageInfo {
        private List<VehiclePriceInfo> vehicleDamagePriceInfoList;
        private Integer type;
    }

    @Data
    public static class VehiclePriceInfo {
        private Integer price;
        private Integer type;
    }

    @Data
    public static class ViolationDealing {
        private IllegalDeductRule illegalDeductRule;
        private DriveLicenseLending driveLicenseLending;
        private NotifyDetail notifyDetail;
    }

    @Data
    public static class NotifyDetail {
        private Integer auditTime;
        private Integer userDealTime;
        private Integer notifyUserTime;

    }

    @Data
    public static class DriveLicenseLending {
        private Integer diffCityLendingAvailabilityTime;
        private Integer sameCityLendingAvailabilityTime;
        private Integer lendingDriveLicenseDeposit;
        private Integer lendingAdvanceBookingTime;

    }

    @Data
    public static class IllegalDeductRule {
        private Integer lateProcessingPenalty;
    }

    @Data
    public static class EnergyServiceFee {
        private String oilServiceFee;
        private String batteryServiceFee;
    }

    @Data
    public static class ProductInvoiceRule {
        private Integer invoiceContent;
        private Integer invoiceFreightType;
        private Integer invoiceType;
        private String freightPrice;
    }

    @Data
    public static class PickupMaterials {

        private Integer idValidMonth;
        private String idType;
        private String depositPayType;
        private Integer creditCardValidMonth;
        private Integer minDriverAge;
        private Integer minDriveExperience;
        private Integer maxDriverAge;
        private Integer driveLicenseExpiryDate;
    }

    @Data
    public static class RoadAssist {
        private Integer serviceCharge;
        private Integer assistType;
        private Integer serviceRange;

    }

    @Data
    public static class ForbiddenDrive {
        private String effectStartTime;
        private Integer forbiddenType;
        private String forbiddenCityCode;
        private String effectEndTime;
        private Integer effectType;
        private Integer status;
    }

    @Data
    public static class ProductCancelRule {

        private Integer dateType;
        private Integer cancelChargeType;
        private String startTime;
        private String endTime;
        private Integer noshowChargePt;
        private CancelCharge cancelCharge;
    }

    @Data
    public static class CancelCharge {
        private List<PickUpCharge> pickUpChargeList;

        private Integer pickUpFreeHours;
    }

    @Data
    public static class PickUpCharge {
        private Integer pickUpChargeBeginHours;
        private Integer pickUpChargeFeePercent;
    }
}
