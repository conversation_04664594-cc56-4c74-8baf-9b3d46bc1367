package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleTagEntity;
import io.quarkus.panache.common.Parameters;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/29 10:35
 */
@ApplicationScoped
public class VehicleTagRepository extends AbstractPanacheRepository<VehicleTagEntity, Long> {

    public List<VehicleTagEntity> findAllByVehicleModelIds(Iterable<Long> vehicleModelIds) {
        return find("vehicleModelId in :vehicleModelIds and deleted = :deleted",
                Parameters.with("vehicleModelIds", vehicleModelIds).and("deleted", 0)
        ).list();
    }

    public List<VehicleTagEntity> findAllByVehicleModelId(Long vehicleModelId) {
        return find("vehicleModelId = :vehicleModelId and deleted = :deleted",
                Parameters.with("vehicleModelId", vehicleModelId).and("deleted", 0)
        ).list();
    }
}
