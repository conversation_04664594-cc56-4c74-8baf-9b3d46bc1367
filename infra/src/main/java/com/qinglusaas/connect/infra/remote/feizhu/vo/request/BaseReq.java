package com.qinglusaas.connect.infra.remote.feizhu.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import javax.enterprise.context.RequestScoped;
import java.util.Date;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/2/10, Friday
 **/
@Data
@RequestScoped
public class BaseReq {


    /**
     * API接口名称，例如:taobao.alitrip.domestic.rent.car.status.update
     * Y
     */
    private String method;
    /**
     * TOP分配给应用的AppKey，例如：12345678
     * Y
     */
    @JsonProperty("app_key")
    private String appKey;
    /**
     * 用户登录授权成功后，TOP颁发给应用的授权信息，详细介绍请点击这里。当此API的标签上注明：“需要授权”，则此参数必传；“不需要授权”，则此参数不需要传；“可选授权”，则此参数为可选
     * N
     */
    private String session;
    /**
     * 时间戳，格式为yyyy-MM-dd HH:mm:ss，时区为GMT+8，例如：2015-01-01 12:00:00。淘宝API服务端允许客户端请求最大时间误差为10分钟
     * Y
     */
    @JsonFormat
            (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timestamp;
    /**
     * API协议版本，可选值：2.0
     * Y
     */
    private String v;
    /**
     * 签名的摘要算法，可选值为：hmac，md5，hmac-sha256。
     * Y
     */
    @JsonProperty("sign_method")
    private String signMethod;
    /**
     * API输入参数签名结果，签名算法介绍请点击这里
     * Y
     */
    private String sign;
    /**
     * 响应格式。默认为xml格式，可选值：xml，json。
     * N
     */
    private String format;
    /**
     * 是否采用精简JSON返回格式，仅当format=json时有效，默认值为：false
     * N
     */
    private Boolean simplify;

    /**
     * 请求扩展信息
     */
    private String extra;

    public void init(String method, String appKey, String extra) {
        this.method = method;
        this.appKey = appKey;
        this.timestamp = new Date(System.currentTimeMillis());
        this.v = "2.0";
        this.signMethod = "md5";
        this.format = "json";
        this.simplify = false;
        if (extra != null && !"".equals(extra)) {
            try {
                JsonNode jsonNode = new ObjectMapper().reader().readTree(extra);
                if (jsonNode.get("env") != null) {
                    this.extra = "{\"env\":\"test\"}";
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
