package com.qinglusaas.connect.infra.persistence.entity.vehicle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/10/7 21:52
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "added_service_setting")
public class AddedServiceSettingEntity extends AbstractEntity implements Serializable {

    @Column(name = "store_id", nullable = false)
    private int storeId;

    @Column(name = "merchant_id", nullable = false)
    private int merchantId;

    @Column(name = "preset", nullable = false)
    private byte preset;

    @Column(name = "name", nullable = false, length = 20)
    private String name;

    @Column(name = "limit_per_order", nullable = false)
    private byte limitPerOrder;

    @Column(name = "status", nullable = false)
    private byte status;

    @Column(name = "op_user_id", nullable = false)
    private int opUserId;
}
