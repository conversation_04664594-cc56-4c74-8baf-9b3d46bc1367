package com.qinglusaas.connect.infra.persistence.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.geolatte.geom.Point;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 门店
 *
 * @TableName store_info
 */
@NamedNativeQueries({
        @NamedNativeQuery(name = "find_store_info_with_service_pickup_by_city_code",
                query =
                        "SELECT " +
                                "si.city_code as cityCode " +
                                "FROM store_info as si " +
                                "JOIN service_pickup as sp on si.id = sp.store_id " +
                                "WHERE sp.enabled = 1 and si.deleted = 0 and si.is_test = 0 and si.city_code in (:cityCode) " +
                                "GROUP BY si.city_code"
        )
})
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "store_info")
//@SQLDelete(sql = "update storeInfoEntity set deleted=1 where id=?")
@Where(clause = "deleted = 0")
public class StoreInfoEntity extends AbstractAuditableEntity implements Serializable {

    /**
     * 门店联系人
     */
    @OneToMany(mappedBy = "storeInfoEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StoreContactEntity> storeContacts;

    /**
     * 门店指引
     */
    @OneToMany(mappedBy = "storeInfoEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StoreGuideEntity> storeGuideEntities;

    /**
     * 门店夜间服务费
     */
    @OneToMany(mappedBy = "storeInfoEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BusinessTimeEntity> businessTimeEntities;

    /**
     * 门店图片
     */
    @OneToMany(mappedBy = "storeInfoEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StoreAttEntity> storeAttEntities;

    /**
     * 门店上门取送车服务
     */
    @OneToMany(mappedBy = "storeInfoEntity", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ServicePickupEntity> servicePickupEntities;

    /**
     * 门店名称
     */
    @NotBlank(message = "[门店名称]不能为空")
    @Size(max = 60, message = "编码长度不能超过60")
    @Length(max = 60, message = "编码长度不能超过60")
    @Column(name = "store_name")
    private String storeName;

    /**
     * 国编码
     */
    @NotNull(message = "[国编码]不能为空")
    @Column(name = "country_id")
    private Long countryId;

    /**
     * 省编码
     */
    @NotNull(message = "[省编码]不能为空")
    @Column(name = "province_id")
    private Long provinceId;

    /**
     * 城市编码
     */
    @NotNull(message = "[城市编码]不能为空")
    @Column(name = "city_id")
    private Long cityId;

    /**
     * 商家ID
     */
    @NotNull(message = "[商家ID]不能为空")
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 门店大小 单位:平米
     */
    @NotNull(message = "[门店大小 单位:平米]不能为空")
    @Column(name = "store_size")
    private Integer storeSize;

    /**
     * 门店装修 1精装修;2:标准装修;3:普通装修;4:低档装修;5:无门面无装修
     */
    @NotNull(message = "[门店装修 1精装修;2:标准装修;3:普通装修;4:低档装修;5:无门面无装修]不能为空")
    @Column(name = "store_decorate")
    private Integer storeDecorate;

    /**
     * 门店地址
     */
    @NotBlank(message = "[门店地址]不能为空")
    @Size(max = 200, message = "编码长度不能超过200")
    @Length(max = 200, message = "编码长度不能超过200")
    @Column(name = "address")
    private String address;

    /**
     * 门店经纬度信息
     */
    @NotNull(message = "[门店经纬度信息]不能为空")
    @Column(name = "gis", columnDefinition = "geometry(Point,4326)")
    private Point gis;

    /**
     * 门店位置类型 1:市区门店;2:机场火车站店
     */
    @NotNull(message = "[门店位置类型 1:市区门店;2:机场火车站店]不能为空")
    @Column(name = "store_pos_type")
    private Integer storePosType;

    /**
     * 枢纽类型
     */
    @NotNull(message = "枢纽类型 0:非枢纽;1:枢纽站外;2:枢纽站内")
    @Column(name = "pos_hub")
    private Integer posHub;

    /**
     * 支付方式
     */
    @NotNull(message = "支付方式 0:全部; 1: 仅现金; 2: 仅预付")
    @Column(name = "pay_mode")
    private Integer payMode;

    /**
     * 最小提前预定小时数，如 0、1、2、3等
     */
    @NotNull(message = "[最小提前预定小时数，如 0、1、2、3等]不能为空")
    @Column(name = "min_advance_booking_time")
    private Double minAdvancedBookingTime;

    /**
     * 最大提前预定周数，如 0、1、2、3等
     */
    @NotNull(message = "[最大提前预定周数，如 0、1、2、3等]不能为空")
    @Column(name = "max_advance_booking_time")
    private Double maxAdvancedBookingTime;

    /**
     * 最短租期
     */
    @NotNull(message = "最短租期(1天1小时 1.1:1天1小时;1.23 一天23小时")
    @Column(name = "min_rent_term")
    Double minRentTerm;

    /**
     * 最长租期
     */
    @NotNull(message = "最长租期(1天1小时 1.1:1天1小时;1.23 一天23小时")
    @Column(name = "max_rent_term")
    Double maxRentTerm;

    /**
     * 订单间隔时间
     */
    @NotNull(message = "订单间隔时间 单位为分")
    @Column(name = "order_interval")
    Integer orderInterval;

    /**
     * 是否支持异地还车
     */
    @NotNull(message = "是否支持异地还车 0:否;1:是")
    @Column(name = "allopatry_return_enabled")
    Integer allopatryReturnEnabled;

    /**
     * 异地取还服务收费
     */
    @NotNull(message = "异地取还服务收费 单位分")
    @Column(name = "allopatry_return_fee")
    Integer allopatryReturnFee;

    /**
     * 门店类型 0:实体门店;1:服务网点
     */
    @NotNull(message = "[门店类型 0:实体门店;1:服务网点]不能为空")
    @Column(name = "store_type")
    private Integer storeType;

    /**
     * 门店状态 0:已关闭;1:营业中
     */
    @NotNull(message = "[门店状态 0:已关闭;1:营业中]不能为空")
    @Column(name = "store_status")
    private Integer storeStatus;

    /**
     * 免费接送
     */
    @NotNull(message = "免费接送 1:启用  0：禁用")
    @Column(name = "free_shuttle_enabled")
    private Integer freeShuttleEnabled;

    /**
     * 免费接送范围
     */
    @NotNull(message = "免费接送范围（公里）")
    @Column(name = "free_shuttle")
    private Integer freeShuttle;

    /**
     * 上门送取车服务是否开启 1:已开启；0:未开启
     */
    @NotNull(message = "上门送取车服务是否开启")
    @Column(name = "pickup_enabled")
    private Integer pickupEnabled;

    /**
     * 测试门店 1:测试门店；0: 非测试门店
     */
    @NotNull(message = "[测试门店 1:测试门店；0: 非测试门店]不能为空")
    @Column(name = "is_test")
    private Integer isTest;

}
