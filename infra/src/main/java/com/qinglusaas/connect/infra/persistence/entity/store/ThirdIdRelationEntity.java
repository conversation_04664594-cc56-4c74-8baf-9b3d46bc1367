package com.qinglusaas.connect.infra.persistence.entity.store;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/7/28, Friday
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "third_id_relation")
//@SQLDelete(sql = "update storeInfoEntity set deleted=1 where id=?")
@Where(clause = "deleted = 0")
public class ThirdIdRelationEntity extends PanacheEntityBase implements Serializable {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    public Long createdAt;

    /**
     * 操作时间
     */
    @Column(name = "op_time", nullable = false)
    public Long updatedAt;

    /**
     * 删除 0:否; 1:是
     */
    @NotNull(message = "[删除 0:否; 1:是]不能为空")
    @Column(name = "deleted")
    private Integer deleted;

    /**
     * 渠道ID
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 第三方业务ID
     */
    @Column(name = "third_id")
    private String thirdId;

    /**
     * saas业务ID
     */
    @Column(name = "saas_id")
    private Long saasId;

    /**
     * 类型（1：商家 2：门店 3：服务圈）
     */
    @Column(name = "type")
    private Byte type;

    /**
     * 门店ID
     */
    @Column(name = "store_id", nullable = false)
    private Long storeId;
}
