package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import java.util.List;
import java.util.Optional;

import javax.enterprise.context.ApplicationScoped;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleSubSeryEntity;

import io.quarkus.panache.common.Parameters;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/11, Friday
 **/
@ApplicationScoped
public class VehicleSubSeryRepository extends AbstractPanacheRepository<VehicleSubSeryEntity, Long> {


    public Optional<VehicleSubSeryEntity> findByIdOptional(Long id) {
        return findByIdOptional(id, 0);
    }

    public Optional<VehicleSubSeryEntity> findByIdOptional(long id, int deleted) {
        return find("id = :id and deleted = :deleted",
                Parameters.with("id", id).and("deleted", deleted)
        ).singleResultOptional();
    }

    public List<VehicleSubSeryEntity> findAllByIds(Iterable<Long> ids) {
        return findAllByIds(ids, 0);
    }

    public List<VehicleSubSeryEntity> findAllByCtripSubSeryIds(Iterable<Long> ids) {
        return find("ctrip_sub_sery_id in :ids",
                Parameters.with("ids", ids)
        ).list();
    }

    public List<VehicleSubSeryEntity> findAllByIds(Iterable<Long> ids, int deleted) {
        return find("id in :ids and deleted = :deleted",
                Parameters.with("ids", ids).and("deleted", deleted)
        ).list();
    }

    /**
     * 分页查询车型子系列（按ctrip_sub_sery_id排序）
     *
     * @param startId 起始ctrip_sub_sery_id
     * @param limit   查询数量限制
     * @return 车型子系列列表
     */
    public List<VehicleSubSeryEntity> findByCtripSubSeryIdGreaterThanEqual(Long startId, int limit) {
        return find("ctrip_sub_sery_id >= :startId and ctrip_sub_sery_id is not null and preset = 1 order by ctrip_sub_sery_id asc",
                Parameters.with("startId", startId))
                .page(0, limit)
                .list();
    }

    /**
     * 统计ctrip_sub_sery_id不为空的记录总数
     *
     * @return 总数
     */
    public long countByCtripSubSeryIdNotNull() {
        return count("ctrip_sub_sery_id is not null");
    }

}
