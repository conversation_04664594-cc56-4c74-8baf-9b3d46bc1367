package com.qinglusaas.connect.infra.persistence.dao.vehicle;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleInfoEntity;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * .
 *
 * <AUTHOR>
 * @date 2022/9/25 22:44
 */
@ApplicationScoped
public class VehicleInfoRepository extends AbstractPanacheRepository<VehicleInfoEntity, Long> {

    public Optional<VehicleInfoEntity> findByLicence(String license, Long merchantId) {
        return find("license = ?1 and merchantId = ?2", license, merchantId).firstResultOptional();
    }


    public List<VehicleInfoEntity> findAllByMerchantId(Long merchantId) {
        return find("merchantId = ?1 and platformSold = ?2", merchantId, 1).list();
    }

}
