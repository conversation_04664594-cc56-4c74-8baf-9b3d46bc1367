package com.qinglusaas.connect.infra.converter;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/11, Tuesday
 **/
public interface BizFieldConverter {

    /**
     * 门店类型
     *
     * @param originType
     * @return
     */
    Integer storeTypeCovert(Integer originType);

    /**
     * 门店状态
     *
     * @param status
     * @return
     */
    Integer storeStatusConvert(Integer status);

    /**
     * 枢纽站
     *
     * @param stationType
     * @return
     */
    Integer stationTypeConvert(Integer stationType);

    /**
     * 接送服务类型
     *
     * @param feeType
     * @return
     */
    Integer feeTypeConvert(Integer feeType);


    Integer vehicleStatusConvert(String ctripCarStatus);


    Integer vehicleAuditStatusConvert(String ctripCarStatus);

    Integer vehicleSaleStatusConvert(String ctripSaleStatus);
}
