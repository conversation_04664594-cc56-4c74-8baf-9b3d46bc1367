package com.qinglusaas.connect.infra.remote.saas.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 价格日历.
 *
 * <AUTHOR>
 * @date 2022/9/25 12:12
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DailyPriceDTO {

    /**
     * 日期。格式为yyyy-MM-dd。如” 2021-04-21”。
     */
    @NotNull
    private String date;

    /**
     * 整日价格(单位分)
     */
    @NotNull
    private Integer price;

    /**
     * 时间（H）
     */
    @NotNull
    private Integer hour;

    /**
     * 非24小时（整天）时的价格(单位分)
     * 可以为空
     */
    private Integer partDailyPrice;

    /**
     * 收取天百分比
     */
    private Double per;

    /**
     *  无忧租的金额（整天）
     *    null：没有开通无忧租
     *    有值：已开通无忧租且返回无忧租金额
     */
    private Integer worryFreePrice;

    /**
     * 无忧租的金额（实际时间）
     */
    private Integer worryFreeDailyPrice;
}
