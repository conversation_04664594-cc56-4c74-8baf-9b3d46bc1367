package com.qinglusaas.connect.infra.persistence.dao.store;

import com.qinglusaas.connect.infra.persistence.dao.AbstractPanacheRepository;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/7/31, Monday
 **/
@ApplicationScoped
public class ThirdIdRelationRepository extends AbstractPanacheRepository<ThirdIdRelationEntity, Long> {

    public Optional<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndThirdId(Long channelId, Long merchantId, Byte type, String thirdId) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and thirdId = ?4", channelId, merchantId, type, thirdId).firstResultOptional();
    }

    public Optional<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndSaasId(Long channelId, Long merchantId, Byte type, Long saasId) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and saasId = ?4", channelId, merchantId, type, saasId).firstResultOptional();
    }

    public List<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndInThirdId(Long channelId, Long merchantId, Byte type, Iterable<String> thirdIdList) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and thirdId in ?4", channelId, merchantId, type, thirdIdList).list();
    }

    public List<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndInSaasId(Long channelId, Long merchantId, Byte type, Iterable<Long> saasId) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and saasId in ?4", channelId, merchantId, type, saasId).list();
    }

    public List<ThirdIdRelationEntity> findByChannelIdAndMerchantId(Long channelId, Long merchantId, Byte type) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and deleted = 0", channelId, merchantId, type).list();
    }

    /**
     * 根据渠道ID、商户ID和类型查询关系数据
     * 
     * @param channelId 渠道ID
     * @param merchantId 商户ID
     * @param type 类型
     * @return 关系数据列表
     */
    public List<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndType(Long channelId, Long merchantId, Byte type) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and deleted = 0", 
            channelId, merchantId, type).list();
    }

    /**
     * 根据渠道ID、商户ID、类型查询不在指定saasId列表中的关系数据
     * @param channelId 渠道ID
     * @param merchantId 商户ID  
     * @param type 类型
     * @param saasIds saasId列表
     * @return 关系数据列表
     */
    public List<ThirdIdRelationEntity> findByChannelIdAndMerchantIdAndTypeAndNotInSaasId(Long channelId, Long merchantId, Byte type, List<Long> saasIds) {
        return find("channelId = ?1 and merchantId = ?2 and type = ?3 and saasId not in ?4 and deleted = 0", 
            channelId, merchantId, type, saasIds).list();
    }

    /**
     * 批量删除关系数据
     * @param entities 要删除的实体列表
     */
    public void deleteInBatch(List<ThirdIdRelationEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }
        
        long now = System.currentTimeMillis();
        for (ThirdIdRelationEntity entity : entities) {
            entity.setDeleted(1);
            entity.setUpdatedAt(now);
        }
        persist(entities);
    }

}
