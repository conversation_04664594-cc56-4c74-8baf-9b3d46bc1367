package com.qinglusaas.connect.infra.persistence.entity.common;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @TableName area
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "area")
public class AreaEntity extends PanacheEntityBase implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 地区编码
     */
    @NotNull(message = "[地区编码]不能为空")
    @Column(name = "code")
    private Integer code;

    /**
     * 地区名称
     */
    @NotBlank(message = "[地区名称]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @Length(max = 20, message = "编码长度不能超过20")
    @Column(name = "name")
    private String name;

    /**
     * 别名
     */
    @Column(name = "alias_name")
    private String aliasName;

    /**
     * 0:国家;1:省;2:市;3:区/县
     */
    @NotNull(message = "[0:国家;1:省;2:市;3:区/县]不能为空")
    @Column(name = "depth")
    private Integer depth;

    /**
     * 父ID
     */
    @NotNull(message = "[父ID]不能为空")
    @Column(name = "parent_code")
    private Integer parentCode;

    /**
     * 区号
     */
    @Column(name = "phone_city_code")
    private String phoneCityCode;

    /**
     * 首字母拼音
     */
    @NotNull(message = "[首字母拼音]不能为空")
    @Column(name = "inital_pinyin")
    private String initalPinyin;

    /**
     * 字母拼音
     */
    @NotNull(message = "[首字母拼音]不能为空")
    @Column(name = "short_pinyin")
    private String shortPinyin;

    /**
     * 地区全拼
     */
    @NotBlank(message = "[地区全拼]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @Length(max = 50, message = "编码长度不能超过50")
    @Column(name = "pinyin")
    private String pinyin;


}
