package com.qinglusaas.connect.infra.persistence.dao;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Parameters;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> robintse
 * @mailto : s<PERSON><PERSON>@dian.so
 * @created : 2022/9/5, Monday
 **/
public class AbstractPanacheRepository<ENTITY, ID> implements PanacheRepositoryBase<ENTITY, ID> {

    public List<ENTITY> findAllByIds(Iterable<ID> ids) {
        return findAllByIds(ids, 0, 0);
    }

    public List<ENTITY> findAllByIds(Iterable<ID> ids, int deleted, int test) {
        return find("id in :ids and deleted = :deleted and isTest = :test",
                Parameters.with("ids", ids).and("deleted", deleted).and("test", test)
        ).list();
    }

    protected boolean isNotEmpty(List<?> list) {
        return list != null && !list.isEmpty();
    }

    protected List<String> toUpperCase(List<String> subjectList) {
        return subjectList.stream().map(String::toUpperCase).collect(Collectors.toList());
    }

}
