package com.qinglusaas.connect.infra.remote.hello.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public  class ServiceGoodsPrice {
    // V2注释部分为Hello线上文档删除部分
    /**
     * 服务价格主键
     */
    private String priceId;

    /**
     * 服务价格
     */
    private Long servicePrice;

    /**
     * 24小时内费用规则。
     */
    private List<FeeRule> less24HourFeeRule;

    /**
     * 24小时以上费用规则。
     */
    private List<FeeRule> greater24HourFeeRule;

    /**
     * ID。
     */
    private Integer id;

    /**
     * 商品服务ID。
     */
    private Integer goodsServiceId;

    /**
     * 价格值(单位:分)。
     */
    private Long priceValue;

    /**
     * 维度类型。
     */
    private Integer dimensionType;

    /**
     * 维度值。
     */
    private String dimensionValue;


    /**
     * 创建时间。
     */
    private String createTime;

    /**
     * 更新时间。
     */
    private String updateTime;

    /**
     * 价格值Map。goodsId : priceValue
     */
    private Map<Integer, Long> priceValueMap;


    @Data
    public static class FeeRule {
        /**
         * 开始小时。
         */
        private Integer startHour;

        /**
         * 结束小时。
         */
        private Integer endHour;

        /**
         * 费率。
         */
        private Long feeRate;
    }
}