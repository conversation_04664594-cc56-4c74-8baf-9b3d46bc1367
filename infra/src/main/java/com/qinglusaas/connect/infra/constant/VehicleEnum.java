package com.qinglusaas.connect.infra.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface VehicleEnum {

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum FuelForm {
        PETROL("汽油"),
        PETROL_CNG("汽油+CNG"),
        DIESEL("柴油"),
        OIL_ELECTRIC_HYBRID("油电混动"),
        ELECTRIC("纯电动"),
        PLUG_IN_HYBRIDS("插电式混合动力"),
        ADD_ONS("增程式");

        private final String name;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum TagProp {
        NEW_SIX_MONTHS("半年内新车"),
        NEW_ONE_YEAR("一年内新车"),
        OVER_TWO_YEAR("两年以上车龄"),
        NEW_TWO_YEAR("两年内新车"),
        NEW_THREE_YEAR("三年内新车"),
        REVERSING_RADAR("倒车雷达"),
        REVERSING_CAMERA("倒车影像"),
        CAR_RECORDERS("行车记录仪"),
        PHONE_HOLDER("手机支架"),
        // 标签库中不存在该标签，需要被转换成倒车雷达和倒车影像
        REVERSING_CAMERA_RADAR("倒车影像/雷达"),
        WATER("瓶装水"),
        ONE_CAR_WASH("一车一洗"),
        FUEL_GUARANTEE("油量保障"),
        SNOW_TIRE("雪地胎"),
        ETC("ETC"),
        UPGRADE_VEHICLE_DAMAGE("可升级车损免赔"),
        SMALL_DAMAGE_FREE("小伤剐蹭免赔"),
        ;

        private final String name;
    }

}
