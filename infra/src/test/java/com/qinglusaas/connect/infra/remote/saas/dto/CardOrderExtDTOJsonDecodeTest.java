package com.qinglusaas.connect.infra.remote.saas.dto;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;

public class CardOrderExtDTOJsonDecodeTest {

    @Test
    public void decodeValue_shouldParseDerateMapCorrectly() {
        String rentCardAttrMap = "{\"cardOrderId\":\"2859056979948540560\",\"basicRentFee\":\"690.0\",\"cardId\":\"5795734344305\",\"cardTradeNo\":\"921894901029\",\"basicServiceFee\":\"150.0\",\"derateMap\":\"{\\\"03001\\\":\\\"20\\\",\\\"02001\\\":\\\"150\\\"}\"}";

        CardOrderExtDTO dto = Json.decodeValue(rentCardAttrMap, CardOrderExtDTO.class);

        Assertions.assertNotNull(dto);
        Assertions.assertEquals("2859056979948540560", dto.getCardOrderId());
        Assertions.assertEquals("690.0", dto.getBasicRentFee());
        Assertions.assertEquals("5795734344305", dto.getCardId());
        Assertions.assertEquals("921894901029", dto.getCardTradeNo());
        Assertions.assertEquals("150.0", dto.getBasicServiceFee());

        Assertions.assertNotNull(dto.getDerateMap());
        JsonObject derate = new JsonObject(dto.getDerateMap());
        Assertions.assertEquals("20", derate.getString("03001"));
        Assertions.assertEquals("150", derate.getString("02001"));
    }
}


